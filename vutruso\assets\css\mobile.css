
.mobile-show li.show a.dropdown-toggle:after {content: "\2212" !important;}
.nav-mobile .dropdown-toggle::after{border-top: 0}
.navbar-toggle .icon-bar:last-child {width: 22px;margin-left: 6px;margin-top: 3px;}
.navbar-toggle .icon-bar:nth-child(2){margin-left: 4px;margin-top: 3px;}
.navbar-toggle .icon-bar + .icon-bar {margin-top: 6px;}
.mobile-no{display:none;}
.navbar .navbar-toggle{display:block; border:none;}
.navbar .navbar-collapse{position:fixed;top:68px;right:-280px;display:block;width:270px;height:100%;margin:0;background-color:#000;transition:right 0.35s ease;z-index:999999999999;}
.navbar .navbar-nav .open .dropdown-menu{position:static;float:none;background-color:transparent;border:0;box-shadow:none;}
.navbar .navbar-form{float:none;padding:0;}
.navbar .navbar-nav>li{float: none; margin-bottom: 0; border-bottom: 1px solid #4b4b4b; padding: 15px 0 15px;}
.navbar .navbar-collapse.in{right:0;}.navbar-collapse{padding-right:35px !important;padding-top:10px;margin-right:0 !important;}
.navbar-nav>li{float:left;}
.img-responsive{display:block;max-width:100%;height:auto;}
.sr-only{position:absolute;width:1px!important;height:1px!important;padding:0;margin:-1px!important;overflow:hidden;clip:rect(0,0,0,0);border:0!important;}
.navbar-toggle:focus{outline:0;}
.navbar-toggle .icon-bar{display:block!important;width:32px!important;height:2px!important;border-radius:0px;}
.navbar-nav>li>.dropdown-menu{margin-top:2%;border-top-left-radius:0;border-top-right-radius:0;border: none;width:100%;padding-left: 2%;}
.navbar-inverse{border-color:#080808;}
.navbar-inverse .navbar-nav>li>a{color:#777;}
.navbar-inverse .navbar-nav>li>a:focus,.navbar-inverse .navbar-nav>li>a:hover{color:#fff;background-color:transparent;}
.navbar-wrapper{padding-top:0;padding-bottom:0;position:relative;top:0;right:0;left:0;}
.nav{font-weight:400;float:none!important;display:inline-block;vertical-align:middle;}
/* .entypo-dot{width:4px;height:4px;background:url(../img/icon/reddot.png) no-repeat right bottom;display:inline-block;} */
.navbar{webkit-transition:background .5s ease-in-out, padding .2s ease-in-out;-moz-transition:background .5s ease-in-out, padding .2s ease-in-out;transition:background .5s ease-in-out, padding .2s ease-in-out;z-index:1000;padding: 0!important;border-radius:0;padding-left:0;}
.dropdown-menu{min-width:auto;width:100%;margin:0;padding:0;}
.navbar-nav>li>a{padding:0px!important;margin-right:18px!important;font-family:'Montserrat', sans-serif;color:#464955!important;-webkit-font-smoothing:antialiased;}
.new-toggle{position:fixed!important;float:right;right:0;top:43px;}
.navbar-toggle{cursor:pointer; position:relative!important; line-height:0; float:right; margin:0!important; width:32px; padding:5px 0!important; border:0; background:transparent; top:0; right:0}
.navbar-inverse .navbar-toggle:focus,.navbar-inverse .navbar-toggle:hover{background-color:transparent!important;}
.fullMenu{padding:4% 0;background:rgba(26, 29, 71, .95);position:fixed;left:0;width:100%;top:0;z-index:1;}
.navbar-push{padding:30px 30px;margin-left:0;margin-right:0;min-height:100%;    background-image: linear-gradient(to right bottom, #1c1a91, #002480, #00276b, #002655, #10243d);}
.push_menu{z-index:9999!important;float:right;height: 41px; width: 35px;}
.push_menu .push-nav{transition:all 0.5s ease-out;width: 25px; height: 41px; margin-right: 10px;}
.mobile-show .navbar-inverse .navbar-toggle{border-color:transparent;}
.mobile-show .navbar-push.navbar-push-left{right:-100%;}
.mobile-show .navbar-push.navbar-push-left.in{right:0;}
.mobile-show .navbar-push{-webkit-transition:all 0.5s ease-out;-moz-transition:all 0.5s ease-out;-o-transition:all 0.5s ease-out;transition:all 0.5s ease-out;height:100%;position:fixed;width:100%;top:0;padding:30px 40px;}
.mobile-show .close-button,.drop-main-menu:hover p::before,.drop-main-menu p::before{display:none;}
.mobile-show .box-menu-drop,.mobile-show  .list-menu-drop{padding-left:0!important;width:100%;}
.mobile-show .navbar-nav>li>a{margin-right:0px!important;}
.mobile-show li.dropdown a.dropdown-toggle:after { font-family: "FontAwesome"; text-decoration:inherit; font-size:22px; float:right; content:'\002B'; font-weight:400; }
.mobile-show .entypo-dot{display:none!important;}
.mobile-show .navigation-close{top:15px!important;right:20px!important;display:block;}
ul.nav.navbar-nav{width:50%;margin:0 auto!important;display:block;}
.mobile-show .navbar-push .nav>li>a{font-size:16px!important;color:#fff!important;text-transform: uppercase;}
.mobile-show .navbar-push .nav>li>a:hover{color: #2169c4!important;}
.mobile-show .navbar-push .drop-main-menu p{font-size:12px;padding:6px 0 12px;}
.mobile-show .fullMenu{padding:0px;position:inherit;height:auto;width:auto;padding-left: 5%;overflow: hidden;background-image: linear-gradient(to right top, #0f3978, #003482, #002d8a, #002491, #0a1796);
    border-radius: 6px;}
.mobile-show .drop-main-menu{ border-radius:0px; opacity:1!important; background:transparent!important; margin-right:0px; margin-bottom:0; display:inline-block; min-height:inherit; width:100%!important; border-bottom: 1px solid #fefefe0d!important; padding: 9px; }
.mobile-show .nav.navbar-nav.navbar-right li.dropdown a:hover{background:transparent;transform:inherit!important;}
.mobile-show .navbar .navbar-nav>li{float: left; margin-bottom: 0; border-bottom: 1px dotted #1e407a94; padding: 14px 0; width: 100%;}
.navigation-close{top:30px!important;right:25px!important;z-index:0!important;display:block!important;}
.nav > li > a{padding:10px 4px;-webkit-font-smoothing:subpixel-antialiased;-webkit-text-stroke:1px transparent;font-weight:700;margin-right:10px;}
.close-button{background: url(https://vutruso.com/wp-content/uploads/2020/11/close-1.svg) no-repeat;
    background-size: cover;/* background-position: -459px -624px; */height:25px;width:25px;border:0px;position:absolute;right:0;z-index:99999;margin:0;top:-30px;-webkit-transition:-webkit-transform 0.3s cubic-bezier(0.455, 0.03, 0, 1);-moz-transition:-moz-transform 0.3s cubic-bezier(0.455, 0.03, 0, 1);-o-transition:-o-transform 0.3s cubic-bezier(0.455, 0.03, 0, 1);transition:transform 0.3s cubic-bezier(0.455, 0.03, 0, 1);}
.close-button:hover,.close-button:focus{-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);-ms-transform:rotate(90deg);-o-transform:rotate(90deg);transform:rotate(90deg);border:0px;outline:none;}
.drop-main-menu{background:#4a4f74;margin-Right:0px;padding:13px;margin-bottom:14px;transition-duration:0.3s;transition-property:transform;-webkit-transition-duration:0.3s;border-radius:4px;transform:translateZ(0px);-webkit-transform:translateZ(0px);display:block;min-height:142px;}
.drop-main-menu:hover{background:#fff;transform:translateY(-5px);-webkit-transform:translateY(-5px);}
.drop-main-menu:hover p{color:#df1f48;}
.drop-main-menu p{font-size:17px;font-family:'Montserrat', sans-serif;color:#d4dce6;line-height:normal;position:relative;padding-bottom:10px;margin-bottom:0;text-transform:capitalize;}
.drop-main-menu p::before{background:#fff;-webkit-transition:width 0.2s;-o-transition:width 0.2s;-ms-transition:width 0.2s;transition:width 0.2s;position:absolute;left:0;bottom:0;width:40%;height:2px;content:'';top:auto;}
.drop-main-menu:hover p::before{width:80%;background:#df1f48;}
.nav.navbar-nav.navbar-right li.dropdown a:hover{color:#1976d2!important}

@media only screen and (max-width:1394px){
	.navbar .navbar-toggle{display:block!important;border:none;}
	.navbar .navbar-nav>li{float:none;margin-bottom:0;border-bottom:1px solid #4b4b4b;padding:15px 0 15px;}
	.nav{display:block;}
	.navbar-nav>li>a{color:#fff!important;padding:10px 30px;}
	.navbar-nav>li{float:left;}
}

@media only screen and (max-width:1200px){
	.close-button,.drop-main-menu:hover p::before,.drop-main-menu p::before{display:none;}
	.box-menu-drop,.list-menu-drop{width:100%;float:left;}
	.drop-main-menu p{font-size:14px;}
	.drop-main-menu{background:transparent;margin-Right:0px;padding:15px;margin-bottom:0;display:inline-block;min-height:inherit;width:220px;}
	.fullMenu{padding:0px;background:transparent;position:inherit;height:auto;width:auto;}
	.fullMenu .container{width:100%;}
	.navbar-inverse .navbar-toggle{border-color:transparent!important;}
	.navbar{margin-bottom:0;}
}

@media only screen and (max-width:1024px){
	.mobile-show .push-nav ul.nav.navbar-nav{width:100%;margin-top:25px;}
}

@media only screen and (max-width:767px){
	.entypo-dot{display:inline-block!important;}
	.woocommerce-cart .cart-empty {
		font-size: 1em;
	}
}

.mobile-show .navbar-inverse .navbar-toggle {
    z-index: 9999;
}

.mobile-show .drop-main-menu {
    color: #1976d2 !important;
}

