/*
Theme Name: Vu Tru So
Theme URI: https://vutruso.com/
Author: Vu Tru So
Author URI: https://vutruso.com/lien-he/
Description: Dich vu thiet ke web tren nen tang WordPress - Contact: 0868017791 - www.vutruso.com - fb.com/vutruso
Version: 1.3
Tags: one-column, two-columns, right-sidebar, accessibility-ready, custom-background, custom-colors, custom-header, custom-menu, editor-style, featured-images, post-formats, sticky-post, threaded-comments, translation-ready, blog
Text Domain: vutruso
*/

/*reset css */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}
* {
  outline: 0;
}
a {
  text-decoration: none;
}
a:hover {
  text-decoration: none;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul,
nav {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}

.table-baogia p {
    padding: 5px 10px;
}

h1 {
  color: #29607c;
}
h2 {
  font-size: 15px;
  color: #3d3d3d;
}
small {
  font-size: 11px;
}
p {
  color: #595959;
}
strong,
b {
  font-weight: 700;
}
/* end reset css */

.wrapper {
  position: relative;
  width: 1186px;
  margin: 0 auto;
}
.clear {
  clear: both;
}
.page-normal {
  padding-top: 1.5%;
  padding-bottom: 1.5%;
}
.page-normal > .title {
  display: block;
  height: 50px;
  line-height: 50px;
  font-weight: 700;
  color: #000;
  font-size: 16.67px;
  border: 1px #e9e9e9 solid;
  border-left: 4px #0081c9 solid;
  padding-left: 16px;
  margin-bottom: 5px;
  margin-top: 10px;
}
.right {
  float: right !important;
}
.left {
  float: left !important;
}
.margin10 {
  margin: 10px 0px;
}

#content .maintitle {
  /*height: 100px;*/
  line-height: 90px;
  background: rgba(0, 0, 0, 0.03);
  color: #666666;
  text-align: center;
  font-size: 23.5px;
  margin-bottom: 2%;
}

#content .maintitle h1,
#content .maintitle h2,
#content .maintitle h3 {
  display: inline-block;
  font-size: 32.5px;
}

#content .maintitle span {
  display: inline;
  background: #c1bf68;
  color: #232323;
  padding: 5px 8px;
  height: 50px;
  margin-left: 4px;
}

#content .theme-list {
  overflow: hidden;
}
#content .theme-list .item:nth-child(4n) {
  margin-right: 0px;
}
#content .theme-list .item {
  position: relative;
  margin-bottom: 3%;
  border: 1px #c9cfdf solid;
  border-bottom: 3px #a9becd solid;
  border-radius: 4px;
}
.theme-list .item h3 {
  font-weight: bold;
}
#content .theme-list .item .thumb {
  padding: 7px;
}
#content .theme-list .item h3 {
  display: block;
  height: 45px;
  line-height: 45px;
  font-weight: 500;
  font-size: 15px;
  text-align: center;
  border-top: 1px #e6e6e6 solid;
  color: #333;
  overflow: hidden;
  padding: 0 10px;
}

#content .theme-list .item strong {
  display: block;
  height: 30px;
  text-align: center;
  line-height: 30px;
  font-weight: 300;
  font-size: 12.5px;
  border-top: 1px #e6e6e6 solid;
  color: #818f81;
}

#content .theme-list .item .vts-price {
  position: absolute;
  display: inline-block;
  bottom: 88px;
  right: -8px;
  background: #095c89;
  height: 34px;
  line-height: 32px;
  font-size: 20px;
  font-weight: 700;
  padding: 0px 12px;
  color: #fff;
  border-radius: 5px 0 0 5px;
}

#content .theme-list .item .vts-price small {
  display: block;
  float: right;
  height: 34px;
  line-height: 34px;
  font-size: 15px;
  font-weight: 300;
  margin-left: 2px;
}

#content .theme-list .item .vts-price:after {
  content: "";
  display: block;
  position: absolute;
  top: -8px;
  right: 0px;
  width: 0px;
  height: 0px;
  border-top: 8px solid transparent;
  border-left: 8px #0076b6 solid;
}

#content .theme-list .item .vts-price:before {
  content: "";
  display: block;
  position: absolute;
  bottom: -8px;
  right: 0px;
  width: 0px;
  height: 0px;
  border-bottom: 8px solid transparent;
  border-left: 8px #0076b6 solid;
}

#content .theme-list .item .link-detail {
  position: absolute;
  bottom: 0px;
  right: 0px;
  left: 0px;
  height: 75px;
  background: #fff;
  opacity: 0;
  transition: opacity 0.3s;
}
#content .theme-list .item:hover .link-detail {
  opacity: 1;
}
.link-detail a {
  display: block;
  float: left;
  margin: 18px 6px;
  width: 95%;
  height: 40px;
  line-height: 44px;
  background: #389cd5;
  color: #fff !important;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  border-radius: 4px;
}

#content .vts-feature {
  margin: 25px 0px;
  overflow: hidden;
}
#content .vts-feature .item {
  display: block;
  float: left;
  width: 330px;
  margin-right: 20px;
  margin-bottom: 70px;
}
#content .vts-feature .item:nth-child(3n) {
  margin-right: 0px;
}
#content .vts-feature .item h3,
#content .vts-feature .item h4 {
  display: block;
  float: left;
  font-size: 16.67px;
  line-height: 49px;
  color: #5a5a5a;
  font-weight: 700;
  padding-left: 10px;
}
#content .vts-feature .item:hover h3,
#content .vts-feature .item:hover h4 {
  color: #0081c9;
}
#content .vts-feature .item span {
  display: block;
  font-size: 16.67px;
  line-height: 24px;
  color: #5a5a5a;
  font-weight: 300;
  padding: 10px 0;
  float: left;
  clear: both;
}

@media (max-width: 540px) {
  .breadcrumb {
    display: block;
    flex-wrap: wrap;
    margin-bottom: 1rem;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    padding: 0.3rem 1rem;
    margin: 0;
    list-style: none;
    padding-left: 6px;
  }
  .breadcrumb ol {
    list-style-type: decimal;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
  }

  .vts_popular-tags {
    padding: 0 15px;
  }
}

#content .vts-gallery {
  display: block;
  position: relative;
  float: left;
  height: 372px;
  border: 1px #f5fafd solid;
  padding: 8px;
  overflow: hidden;
}

#content .buy-vts label > strong {
  display: block;
  float: left;
  margin-top: 14px;
  width: 320px;
  padding: 0px 5px;
  color: #4f5253;
  font-size: 16.67px;
  font-weight: 400;
  line-height: 20px;
}

#content .buy-vts small {
  display: block;
  float: left;
  width: 320px;
  padding: 0px 5px;
  color: #88898a;
  font-size: 12px;
  font-weight: 300;
  line-height: 14px;
  letter-spacing: -0.4px;
}

#content .buy-vts mark {
  position: relative;
  background: none;
  display: block;
  width: 125px;
  height: 60px;
  border-left: 1px #e9e9e9 solid;
  font-weight: bold;
  float: right;
  color: #000;
  line-height: 60px;
  font-size: 16.67px;
  text-align: center;
}

#content .buy-vts mark small {
  position: absolute;
  top: 5px;
  right: 0px;
  width: 100%;
  text-decoration: line-through;
  line-height: 12px;
  font-size: 12px;
}


.demo {
  background-color: #095c89;
  border: none;
  margin: 0;
  padding: 0;
  font-weight: bold;
  color: #fff;
  text-transform: uppercase;
  font-size: 16px;
}
#content .buy-vts input:checked + label span i {
  background: #0081c9;
}

#content .buy-vts input:checked + label mark {
  color: #d32f2f;
}

#content .buy-vts input:disabled + label span {
  border: 1px #999 solid;
}

#content .buy-vts input:disabled:checked + label span i {
  background: #999;
}

#content .buy-vts a.demo {
  display: block;
  float: left;
  width: 96%;
  border: 2px #8c8c8c solid;
  background: none;
  text-align: center;
  padding: 13px 5px;
  margin-left: 0px;
  text-decoration: none;
  transition: background 0.3s;
  color: #8c8c8c;
  font-weight: 700;
  margin-top: 10px;
  text-transform: uppercase;
}

#content .buy-vts a.demo:hover {
  background: #30506f;
  color: #fff;
}

#content .vts-detail {
  display: block;
  border: 1px #e9e9e9 solid;
  overflow: hidden;
  border-radius: 4px;
  margin-top: 10px;
  float: left;
  width: 100%;
}

#content ul.tabs {
  margin: 0px;
  padding: 0px;
  border-bottom: 1px solid rgba(0, 34, 51, 0.1);
  overflow: hidden;
}

#content ul.tabs li {
  float: left;
  list-style: none;
  padding: 0;
  margin: 0;
}

.wc-tabs ul {
  border-bottom: 1px solid rgba(0, 34, 51, 0.1);
  overflow: hidden;
}

#content ul.tabs li a {
  display: block;
  padding: 0px 30px;
  text-decoration: none;
  font-size: 16.67px;
  line-height: 46px;
  border-right: 1px #e9e9e9 solid;
}

#content ul.tabs li a.active {
  color: rgb(25, 118, 210);
  border-top: 3px solid rgb(25, 118, 210);
  border-bottom-color: #fff;
  font-weight: bold;
  text-transform: uppercase;
}

#content .tab2,
#content .tab3,
#content .tab4 {
  display: none;
}

#content .vts-describe {
  padding: 8px 0;
}

#content .maincontent {
  overflow: hidden;
}
#content .domain-vts-price-list {
  display: block;
  margin: 10px 0;
  overflow: hidden;
  width: 600px;
  float: left;
}
.single {
  font-size: 14px;
  line-height: 18px;
  font-weight: 300;
  color: #333;
}

.single .alignleft {
  display: inline;
  float: left;
  margin: 4px 4px 4px 0px;
}

.single .alignright {
  display: inline;
  float: right;
  margin: 4px 0px 4px 4px;
}

.single .aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.main-single-post h1,
.main-single-post h2,
.main-single-post h3,
.main-single-post h4,
.main-single-post h5,
.main-single-post h6 {
  clear: both;
  font-weight: bold;
  margin: 15px 0px;
  font-family: "Baloo Chettan 2";
}
.single h1 {
  font-family: "Montserrat", sans-serif;
  font-size: 1.3rem;
}

.main-single-post h6 {
  font-size: 14px;
  line-height: 14px;
  color: #43627f;
}
.main-single-post h5 {
  font-size: 15px;
  line-height: 15px;
  color: #43627f;
}
.main-single-post h4 {
  font-size: 16px;
  line-height: 16px;
  color: #43627f;
}
.main-single-post h3 {
  font-size: 17px;
  line-height: 17px;
  color: #43627f;
}
.vts-single-content h2 {
  font-size: 22px;
  line-height: 22px;
  color: #43627f;
}
.vts-single-content img {
  border-radius: 8px;
}

.single hr {
  background-color: #ccc;
  border: 0;
  height: 1px;
  margin-bottom: 1.625em;
}

.single p {
  margin-bottom: 10px;
  line-height: 24px;
  font-size: 16px;
}
.single ol {
  list-style-type: decimal;
}
.single ol ol {
  list-style: upper-alpha;
}
.single ol ol ol {
  list-style: lower-roman;
}
.single ol ol ol ol {
  list-style: lower-alpha;
}

.single ul ul,
ol ol,
ul ol,
ol ul {
  margin-bottom: 0;
}

.single dl {
  margin: 0 1.625em;
}
.single dt {
  font-weight: bold;
}
.single dd {
  margin-bottom: 1.625em;
}
.single strong {
  font-weight: bold;
}
.single cite,
.single em,
.single i {
  font-style: italic;
}

.single i.fa {
  font-style: normal;
  text-align: center;
}
blockquote {
  font-style: italic;
  font-weight: normal;
  margin: 1em 0em;
  border-left: 5px #3c9dd8 solid;
  padding: 10px;
  background: #f5fafd;
}
blockquote p {
  margin: 0;
  font-size: 16px !important;
}
blockquote em,
blockquote i,
blockquote cite {
  font-style: normal;
}

blockquote cite {
  color: #666;
  font: 12px Arial, sans-serif;
  font-weight: 400;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

pre {
  border: 1px #ddd solid;
  background: #e4f3f885;
  margin-bottom: 1.6em;
  overflow: auto;
  max-width: 100%;
  padding: 2em 1em 1em;
  line-height: 1.5;
  font-size: 100%;
  position: relative;
  border-radius: 4px;
}

.single address {
  display: block;
  margin: 0 0 10px;
}
.single .infomation ins {
  background: #fff9c0;
  text-decoration: none;
}
.single sup,
.single sub {
  font-size: 10px;
  height: 0;
  line-height: 1;
  position: relative;
  vertical-align: baseline;
}
.single sup {
  bottom: 1ex;
}
.single sub {
  top: 0.5ex;
}
.single small {
  font-size: smaller;
}

/* Forms */
.single input[type="text"],
.single input[type="password"],
.single input[type="email"],
.single input[type="url"],
.single input[type="tel"],
.single input[type="number"],
.single textarea {
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background: #fff;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  color: #333;
  padding: 0.8em;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: none;
  font-size: 16px;
  line-height: normal;
  border-radius: 4px;
  -webkit-box-shadow: 0 0 0 1px #ddd;
  box-shadow: 0 0 0 1px #ddd;
}

.single textarea {
  height: 109px;
  font-size: 13px;
}
.single input[type="text"]:focus,
.single input[type="password"]:focus,
.single input[type="email"]:focus,
.single input[type="url"]:focus,
.single input[type="number"]:focus,
.single input[type="tel"]:focus,
.single textarea:focus {
  -moz-box-shadow: inset 0 0 0 3px rgba(86, 191, 212, 0.15);
  -o-box-shadow: inset 0 0 0 3px rgba(86, 191, 212, 0.15);
  box-shadow: inset 0 0 0 3px rgba(86, 191, 212, 0.15);
  border: 1px solid #56bfd4;
}

.single input#s {
  -moz-border-radius: 2px;
  border-radius: 2px;
  font-size: 14px;
  height: 22px;
  line-height: 1.2em;
  padding: 4px 10px 4px 28px;
}
.single a {
  color: #1976d2;
  text-decoration: none;
}

.lien-ket-nhanh {
  float: left;
}

.single img {
  max-width: 100%;
  height: auto;
}
#content .vts-feature i {
  color: #21c2f8;
  float: left;
  text-align: center;
}
.footerright > h5 {
  padding-left: 28px;
  float: left !important;
}
.card {
  border-radius: 8px;
  height: auto;
  padding: 30px 25px;
  margin: auto;
  margin-top: 32px;
}
.card form {
  width: 100%;
}
input[type="email"],
input[type="submit"] {
  border-radius: 45px;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 7px;
  padding-bottom: 7px;
}

input[type="email"] {
  background-color: #fff;
  border: solid #0069a3;
  color: #59546a;
  width: 46%;
}

input[type="submit"] {
  background-color: #343839;
  border: solid #0069a3;
  color: white;
}

.bg {
  background: #4281eb;
  background: -moz-linear-gradient(left, #4281eb 0%, #4281eb 100%);
  background: -webkit-linear-gradient(left, #4281eb 0%, #4281eb 100%);
  background: linear-gradient(to right, #4281eb 0%, #4281eb 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4281eb', endColorstr='#4281eb', GradientType=1);
}

.categoryFocusedTabPosts .item:hover a.title {
  color: #1976d2;
}
.sidebar .item .thumb {
  float: left;
  padding: 2px;
  border: 2px solid #ecf0f1;
}
.sidebar .item {
  margin-bottom: 15px;
  padding-bottom: 15px;
}
.itemContainer {
  width: 100%;
}
.item .detail .inside {
  line-height: 19px;
  height: 87px;
  overflow: hidden;
}
.sidebar .item .title {
  font-size: 16px;
  margin-bottom: 5px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ecf0f1;
}

.item .detail .summary {
  font-size: 13px;
  color: #9fa4a4;
}
.sidebar .item .detail {
  margin-left: 100px;
}

.sidebar .mainHeading {
  margin-bottom: 11px;
  padding: 0 1px 1px;
  border-bottom: 2px solid #ecf0f1;
}

.mainHeading .title {
  line-height: 20px;
  font-weight: 600;
  text-transform: uppercase;
}

.main-content {
  float: left;
  padding: 0;
  margin: 0;
}

.sidebar {
  float: left;
  padding-left: 10px;
  width: 29%;
}
.main-content .title {
  display: block;
  overflow: hidden;
  background: transparent;
  color: #333;
  margin: 0;
}
.main-content .title span {
  display: block;
  overflow: hidden;
  font-weight: 700;
  font-size: 18px;
  color: #333;
  text-transform: uppercase;
  line-height: 49px;
  padding-left: 0px;
}

.main-content .title > i {
  display: block;
  float: left;
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 19px;
  color: #333;
  background: #fff;
}

#content .vts-gallery img {
  height: auto !important;
  box-shadow: none;
  display: block;
  cursor: pointer;
  opacity: 1 !important;
}

.page-template-default .default-page .entry-content a {
  border-bottom: 1px solid rgba(0, 34, 51, 0.1);
}

input[type="checkbox"]:checked + label:before {
  width: 10px;
  top: -5px;
  left: 5px;
  border-radius: 0;
  opacity: 1;
  border-top-color: transparent;
  border-left-color: transparent;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.cf7_hidden {
  display: none;
}

/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
  margin-bottom: 1.5em;
  max-width: 100%;
}
.wp-caption img[class*="wp-image-"] {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.wp-caption .wp-caption-text {
  margin: 0.8075em 0;
}
.wp-caption-text {
  text-align: center;
}
.sticky {
  display: block;
}

/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
/* Text meant only for screen readers. */
.screen-reader-text {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important; /* Many screen reader and browser combinations announce broken words as they would appear visually. */
}

.screen-reader-text:focus {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  clip-path: none;
  color: #21759b;
  display: block;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
  /* Above WP toolbar. */
}

.gallery-caption {
  display: block;
}

/*--------------------------------------------------------------
# Single
--------------------------------------------------------------*/
.tags {
  display: block;
  overflow: hidden;
  line-height: 53px;
  height: 53px;
  background: #fff;
  color: #333;
  border: 1px #f1f1f1 solid;
  box-shadow: inset 0px -5px 0px 0px #f6f6f6;
  margin-bottom: 10px;
  width: 100%;
}
.tags strong {
  display: block;
  height: 53px;
  float: left;
  color: #333333;
  font-weight: 500;
  padding: 0 10px 0 10px;
  border-right: 1px #f1f1f1 solid;
  border-top: 3px #fba627 solid;
  font-size: 18px;
}
.tags mark {
  background: none;
  display: block;
  float: left;
}
.tags mark a {
  display: block;
  margin: 10px 5px;
  padding: 4px;
  border: 2px #dddddd solid;
  color: #929292;
  line-height: 18px;
  font-weight: 300;
  font-size: 15px;
  transition: all 0.3s;
}
.tags mark a:hover {
  color: #00aaec;
  border: 2px #00aaec solid;
}

.single.fa {
  display: inline-block;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.single.title {
  display: block;
  overflow: hidden;
  line-height: 50px;
  height: 50px;
  background: #f5f5f5;
  color: #333;
  border: 1px #f0f0f0 solid;
}
.single.title span {
  float: left;
  display: block;
  line-height: 50px;
  height: 50px;
  overflow: hidden;
  width: 585px;
  padding: 0 15px;
  font-weight: 700;
  font-size: 14.82px;
  color: #333;
}
.single.title > i {
  display: block;
  float: left;
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 19px;
  color: #333;
  background: #fff;
}

/* author box */
.vutruso_author_box {
  position: relative;
  background: #fff;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  display: inline-block;
  width: 100%;
  margin-bottom: 1em;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
}

.vutruso_author_box p {
  line-height: 1.6;
}

.vutruso_author_social_links .facebook {
  background-color: #1877f2 !important;
}

.vutruso_author_social_links .twitter {
  background-color: #1da1f2 !important;
}

.vutruso_author_social_links .instagram {
  background-color: #c32aa3 !important;
}

.vutruso_authorbox {
  overflow: hidden;
}

.vutruso_author_box_content p {
  margin-top: 0.5rem;
}

.vutruso_author_box_content {
  display: flex;
  width: 100%;
  padding: 1em;
}

.vutruso_author_box_content img {
  float: left;
  margin-right: 2em;
  width: 70px;
  height: 70px !important;
  border-radius: 100%;
}

.vutruso_author_box_name > a {
  margin-right: 10px;
}

.vutruso_author_box_name .vutruso_author_social_links a {
  margin: 0 0 0 0.25em;
  background: 0 0 !important;
  font-weight: 400;
  font-size: 1rem;
  width: auto;
  line-height: inherit;
  height: auto;
}

.vutruso_author_box_name .vutruso_author_social_links a:hover {
  opacity: 0.5;
}

.vutruso_author_box_name {
  display: flex;
  align-items: center;
}

.vutruso_author_box_name a {
  font-size: 16px;
  letter-spacing: -0.03em;
  font-weight: 600;
}

.vutruso_author_social_links a {
  width: 26px;
  line-height: 26px;
  text-align: center;
  height: 26px;
  margin-top: 10px;
  font-size: 14px;
  display: inline-block;
  border-radius: 100%;
}

@media (max-width: 768px) {
  .vutruso_author_box_content img {
    float: none;
    margin: 0 0 10px;
  }
  .post_sidebar {
    display: none;
  }
}

.vutruso_author_box_avatar {
  float: left;
  flex: 0 0 90px;
  width: 90px;
  height: 90px;
  margin-right: 1em;
}

.vutruso_author_social_links i {
  font-size: 15px;
  margin: 0 4px;
}

/*Header Social*/
#infoBlock .cell .icon:before,
.vtsSocial a:before {
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.innerContainer {
  max-width: 1268px;
  margin: auto;
  overflow: hidden;
  padding: 0 5px;
}
.vtsSocial {
  overflow: hidden;
}
.vtsSocial li {
  float: left;
  margin-left: 6px;
}
.vtsSocial li:first-child {
  margin-left: 0;
}
.vtsSocial a {
  display: block;
  width: 20px;
  height: 20px;
}

#infoBlock {
  background: #0d47a1;
  padding: 6px 0;
  color: #fff !important;
  clear: both;
}
#infoBlock .innerContainer {
  background-size: 6px 56px;
  padding-left: 15px;
}
#infoBlock .table {
  width: 100%;
  display: table;
  color: #fff !important;
  margin: 0 !important;
}
#infoBlock .cell {
  display: table-cell;
  vertical-align: middle;
  padding: 3px 10px 2px 60px;
  background-size: 6px 56px;
  line-height: 20px;
  font-size: 14px;
  position: relative;
}
#infoBlock .cell.social {
  width: 27%;
}
#infoBlock .cell .icon {
  display: inline-block;
  height: 40px;
  margin-right: 10px;
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -20px;
}
#infoBlock .cell .icon:before {
  font-size: 40px;
  vertical-align: middle;
}
#infoBlock .cell a {
  color: #fbbd00;
  display: inline-block;
  font-size: 15px;
}
#infoBlock .cell br + a,
#infoBlock .vtsSocial {
  margin-top: 5px;
}
@media all and (max-width: 1100px) {
  #infoBlock {
    display: none;
    border-top: 1px solid #f1f1f1;
    padding: 0;
  }
  #infoBlock .innerContainer {
    background: rgba(0, 0, 0, 0.15);
    padding: 0;
  }
  #infoBlock .table,
  #infoBlock .row {
    display: block;
  }
  #infoBlock .cell {
    display: block;
    background: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    width: 50% !important;
    padding: 10px 10px 10px 60px;
    min-height: 70px;
    font-size: 13px;
    line-height: 16px;
    float: left;
  }
  #infoBlock .cell .data {
    padding-top: 8px;
  }
}
@media all and (max-width: 768px) {
  #infoBlock .cell {
    width: 100% !important;
  }
  .vts-search-overlay {
    width: 90% !important;
  }
}

/*search-overlay*/
#search-box span {
  border: none !important;
  width: auto !important;
  height: auto !important;
  float: none !important;
  font-size: 18px !important;
}

#search-btn {
  border-radius: 2px;
  font-size: 22px;
  color: #e0eff6;
  transition: color 0.3s;
  float: left;
  border: 1px #e0eff6 solid;
  width: 35px;
  height: 35px;
  margin: 0 5px;
}
.fa-search:before {
  display: block;
  width: 32px;
  height: 32px;
  font-size: 22px;
  text-align: center;
  line-height: 32px;
  color: #6ca6c6;
  transition: color 0.3s;
}

#search-btn:hover {
  background-color: #d7e7f0;
  cursor: pointer;
  border-radius: 7px;
}

#search-overlay {
  display: none;
}

.search-block {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  margin: 0;
  z-index: 99999;
}

.search-block:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: 0;
}

.vts-search-overlay {
  display: inline-block;
  vertical-align: middle;
  padding: 10px 15px;
  color: #fff;
  border: none;
  background: transparent;
  width: 70%;
}

#search-box {
  position: relative;
  width: 100%;
  margin: 0;
}

#search-form {
  height: 4em;
  border: 1px solid #999;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  background-color: #fff;
  overflow: hidden;
}

#search-text {
  font-size: 14px;
  color: #ddd;
  border-width: 0;
  background: transparent;
}

#search-box input[type="text"] {
  padding: 28px 10px;
  color: #333;
  outline: none;
  font-size: 1.4em;
  width: 100%;
  height: 100%;
}

#search-button {
  position: absolute;
  top: 0;
  right: 0;
  height: 4em;
  width: 100px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  line-height: 42px;
  border-width: 0;
  background-color: #4d90fe;
  -webkit-border-radius: 0 2px 2px 0;
  -moz-border-radius: 0 2px 2px 0;
  border-radius: 0 2px 2px 0;
  cursor: pointer;
}

#close-btn {
  position: fixed;
  top: 1em;
  right: 1em;
}

#close-btn:hover {
  color: #777;
  cursor: pointer;
}

div.wpcf7 .ajax-loader {
  text-align: center;
  display: block;
  margin: 0 auto;
  margin-top: 10px;
}

.modal {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}

.modal-content {
  position: relative;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  outline: 0;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  border-radius: 0;
}
.modal-header {
  min-height: 16.43px;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  background: rgb(51, 144, 197);
  color: #fff;
}

.modal-header .close {
  margin-top: -2px;
}

.modal-body {
  position: relative;
  padding: 15px;
  background: #26414c;
}
@media (min-width: 768px) {
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
}

.modal-title {
  margin: 0;
  line-height: 1.42857143;
  text-align: center;
  color: #fff;
  font-weight: 700;
  letter-spacing: 3px;
  text-transform: uppercase;
  font-size: 16px;
}

.modal-body input,
.modal-body textarea {
  border: 1px solid #9e8787;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-body input[type="submit"] {
  background-color: #30a43b;
  border-radius: 3px;
  border: solid 1px #30a43b;
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  padding: 10px 47px !important;
}

.modal-body input[type="submit"]:hover {
  background-color: #3390c5;
}

.modal-body div.wpcf7-response-output {
  margin: 0;
  color: #fff;
  border-color: #f7e700;
  text-align: center;
  font-size: 14px;
}

span.wpcf7-form-control-wrap {
  float: left;
  width: 100%;
}
.wpcf7-form {
  overflow: hidden;
}
.wpcf7-form input {
  padding: 0 50px !important;
}

.wpcf7-form textarea {
  padding: 5px 50px;
}

.wpcf7-form .input-name span.wpcf7-form-control-wrap:before,
.wpcf7-form .input-email span.wpcf7-form-control-wrap:before,
.wpcf7-form .input-phone span.wpcf7-form-control-wrap:before,
.wpcf7-form .input-title span:before,
.wpcf7-form .input-message span:before {
  font-family: FontAwesome;
  background: #3390c5;
  color: #fff;
  width: 40px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  font-size: 20px;
  left: 0;
}

.wpcf7-form .input-name span.wpcf7-form-control-wrap:before {
  content: "\f007";
}

.wpcf7-form .input-email span.wpcf7-form-control-wrap:before {
  content: "\f0e0";
}

.wpcf7-form .input-phone span.wpcf7-form-control-wrap:before {
  content: "\f095";
}

.wpcf7-form .input-title span.wpcf7-form-control-wrap:before {
  content: "\f173";
}

.wpcf7-form .input-message span.wpcf7-form-control-wrap:before {
  content: "\f086";
  line-height: 120px;
  float: left;
}

.wpcf7-form .input-message textarea {
  height: 120px;
  max-width: 100%;
}

div.wpcf7 {
  margin: 0;
  padding: 0;
}

div.wpcf7 .screen-reader-response {
  position: absolute;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  width: 1px;
  margin: 0;
  padding: 0;
  border: 0;
}

div.wpcf7-response-output {
  margin: 2em 0.5em 1em;
  padding: 0.2em 1em;
}

.wpcf7-form-control-wrap {
  position: relative;
}

.wpcf7-form .input-message textarea,
.wpcf7-form input {
  padding: 0 50px;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  font-size: 14px !important;
  /*padding: 10px 47px !important;*/
  line-height: 23px;
  color: #fff !important;
}

.wpcf7-form input[type="text"],
.wpcf7-form input[type="email"],
.wpcf7-form input[type="tel"],
.wpcf7-form textarea {
  border: 0;
  border-radius: 0;
  margin: 0;
  padding: 0 15px;
  font-size: 13px;
  color: #fff;
  outline: none;
  float: left;
  background: rgba(0, 0, 0, 0.5);
  height: 40px;
  margin-bottom: 10px;
  max-width: 100%;
}
.wpcf7-form > p {
  max-width: 100%;
}
span.wpcf7-not-valid-tip {
  color: #f00;
  font-weight: normal;
  display: block;
  overflow: hidden;
  clear: both;
  font-size: 13px;
  outline: none;
  float: right;
}

.price button {
  background-color: #ecf7ed;
  border-radius: 3px;
  border: solid 1px #c4ddc8;
  margin: 5px 10px 5px;
  padding: 4px 11px;
  font-weight: bold;
  color: #30a43b;
  line-height: 30px;
  cursor: pointer;
  text-transform: uppercase;
}

#form-bao-gia,
#form-yeu-cau {
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  z-index: 9999;
}
#popup {
  width: 80%;
  height: 91%;
  padding: 20px;
  position: relative;
  /*background: #fff;*/
  margin: 20px auto;
  top: 0;
  max-width: 568px;
}

#tat,
#close {
  position: absolute;
  top: 17px;
  right: 10px;
  cursor: pointer;
  color: #ffeb3b;
  z-index: 9991;
  font-size: 30px;
}

/*Footer*/
footer #footer1 {
  background: #0082ca;
  height: 105px;
  border-bottom: 10px rgba(0, 0, 0, 0.1) solid;
}
footer #footer1 .wrapper > div {
  height: 115px;
}
footer #footer1 .wrapper > div.footerleft img {
  margin: 18px 30px;
  height: auto;
}
footer #footer1 .wrapper > div.footerright .social {
  float: right;
  width: 182px;
  height: 44px;
  margin: 30px 3px;
}
footer #footer1 .wrapper > div.footerright .social span {
  display: block;
  float: right;
  border: 2px #fff solid;
  width: 31px;
  height: 31px;
  margin: 10px 5px;
  transition: border 0.3s;
  cursor: pointer;
}
footer #footer1 .wrapper > div.footerright .social span i {
  display: block;
  width: 32px;
  height: 32px;
  font-size: 22px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  transition: color 0.3s;
}
footer #footer1 .wrapper > div.footerright .social span:hover {
  border-color: #eee;
}
footer #footer1 .wrapper > div.footerright .social span:hover i {
  color: #eee;
}
footer #footer1 .wrapper > div.footerright h5,
footer #footer1 .wrapper > div.footerright span {
  display: block;
  width: 470px;
  font-size: 15px;
  line-height: 18px;
  font-weight: 700;
  color: #fff;
  margin-top: 27px;
  float: right;
}
footer #footer1 .wrapper > div.footerright span {
  font-weight: 300;
  line-height: 16px;
  margin-top: 0px;
}
footer #footer2 {
  background: #343839;
  height: 289px;
}
footer #footer2 .wrapper > div {
  height: 274px;
}
footer #footer2 .wrapper > div.footerleft {
  padding-top: 15px;
}
footer #footer2 .wrapper > div.footerleft p {
  margin: 0px 10px 10px 15px;
  color: #929394;
  font-size: 16px;
  line-height: 22px;
}
footer #footer2 .wrapper > div.footerright {
  padding-top: 8px;
  padding-left: 30px;
  width: 690px;
}
footer #footer2 .wrapper > div.footerright h5 {
  color: #fff;
  font-weight: 700;
  font-size: 18px;
  line-height: 28px;
  margin-bottom: 9px;
}
footer #footer2 .wrapper > div.footerright .link-detail {
  float: left;
  width: 460px;
}
footer #footer2 .wrapper > div.footerright .link-detail ul li {
  width: 50%;
  float: left;
}
footer #footer2 .wrapper > div.footerright .vts-corporate ul li {
  width: 100%;
}
div.footerright ul li {
  width: 100%;
  list-style-type: disc;
  color: #c1c7ca;
}
footer #footer2 .wrapper > div.footerright ul li a {
  color: #fff;
  font-size: 15px;
  font-weight: 300;
  line-height: 28px;
  transition: color 0.3s;
}
footer #footer2 .wrapper > div.footerright ul li a:hover,
footer #footer2 .wrapper > div.footerright ul li a:hover:before {
  color: #ddd;
}
footer #footer3 {
  background: #252728;
  height: 60px;
}
footer #footer3 .wrapper > div {
  height: 60px;
}
footer #footer3 .wrapper > div.footerleft {
  line-height: 60px;
  text-align: center;
  color: #fff;
}
footer #footer3 .wrapper > div.footerright {
  color: #fff;
  text-align: right;
  width: auto;
}
footer #footer3 .wrapper > div.footerright .tel {
  float: right;
  font-size: 25px;
  font-weight: 300;
  line-height: 60px;
}
footer #footer3 .wrapper > div.footerright .adres {
  float: right;
  font-size: 13px;
  font-weight: 300;
  line-height: 20px;
  margin: 10px;
}
footer #footer3 .wrapper > div.footerright img {
  float: left;
}
footer .footerleft {
  float: left;
  width: 320px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
}
footer .footerright {
  float: right;
  width: 720px;
  overflow: hidden;
}

.desc p {
  color: #959595;
}
footer .footerBottom {
  padding: 15px 0;
  text-align: center;
  font-size: 12px;
  color: #fff;
  padding-bottom: 0;
  background-image: linear-gradient(
    to right top,
    #182d95,
    #173499,
    #163a9c,
    #17419f,
    #1947a2,
    #1b4ca6,
    #1d50a9,
    #2055ad,
    #215ab3,
    #225eb8,
    #2363be,
    #2468c4
  );
}
footer .footerBottom li {
  display: inline-block;
  padding: 2px 12px;
  border-left: 1px solid #3261b1;
}
footer .footerBottom li:first-child {
  border-left: 0;
}
footer .footerBottom a {
  display: block;
  color: #86a3e0;
  font-size: 1.2em;
  text-transform: capitalize;
  padding: 0;
}
footer .footerBottom li:hover {
  background: #0051ab;
}

footer .footerCols .title:before,
footer .footerCols .menu a:before,
footer .footerCols .contact .infoBlock .item .icon:before,
footer .footerBottom .safirTop:before {
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

footer {
  clear: both;
  position: relative;
  z-index: 10;
  background-image: linear-gradient(
    to right top,
    #0d47a1,
    #174ba3,
    #1e4fa5,
    #2553a6,
    #2b57a8,
    #1d61ae,
    #0d6bb2,
    #0075b6,
    #0086b8,
    #0095b5,
    #1ca2af,
    #4eaea8
  );
}
footer .footerCols {
  overflow: hidden;
  padding: 0 15px;
  padding-top: 18px;
}

footer .footerCols .title {
  margin-bottom: 10px;
  padding: 20px;
  line-height: 20px;
  color: #fff;
  font-weight: bold;
  padding-left: 10px;
  border-radius: 6px;
  border-bottom: 2px solid #08366e3b;
}

footer .footerCols .title:before {
  width: 25px;
  height: 20px;
  line-height: 20px;
  font-size: 20px;
  color: #fff;
  display: block;
  float: left;
}

footer .footerCols .about .desc {
  color: #959595;
  line-height: 1.8;
}
footer .footerCols .about .desc a:hover {
  color: #595959;
}
footer .footerCols .menu ul {
  overflow: hidden;
}
footer .footerCols .menu li {
  padding: 5px 0;
  float: left;
  width: 100%;
}
footer .footerCols .menu a {
  display: block;
  padding: 10px;
  font-weight: 500;
  text-transform: capitalize;
  color: #fff;
  font-size: 14.5px;
  line-height: 20px;
  border-bottom: 2px solid #08366e3b;
  border-radius: 0 0 10px 10px;
}

footer .footerCols .menu a:hover {
  color: #ddd;
}
footer .footerCols .contact .infoBlock {
    margin-bottom: 20px;
    margin-left: 10px;
}
footer .footerCols .contact .infoBlock .item {
  margin-bottom: 10px;
  overflow: hidden;
  font-size: 11px;
  color: #fff;
}

footer .footerCols .contact .infoBlock .item .icon {
  float: left;
  width: 40px;
  height: 40px;
  background: #0d5da2;
  border-radius: 8px;
}

footer .footerCols .contact .infoBlock .item .icon:before {
  display: block;
  width: 20px;
  height: 20px;
  margin: 10px;
  font-size: 20px;
  text-align: center;
  line-height: 20px;
}

footer .footerCols .contact .infoBlock .item.address .icon:before {
  content: "\E013";
}

footer .footerCols .contact .infoBlock .item.contactmail .icon:before {
  content: "\f003";
  font-family: FontAwesome;
  line-height: 20px;
  margin-right: 3px;
  font-size: 19px;
  color: #fff;
}

footer .footerCols .contact .infoBlock .item.contactphone .icon:before {
  content: "\f098";
  font-family: FontAwesome;
  line-height: 20px;
  margin-right: 3px;
  font-size: 21px;
  color: #fff;
}

footer .footerCols .contact .infoBlock .item.fax .icon:before {
  content: "\f098";
  font-family: FontAwesome;
  line-height: 20px;
  margin-right: 3px;
  font-size: 21px;
  color: #fff;
}
footer .footerCols .contact .infoBlock .item.facebook .icon:before {
  content: "\f082";
  font-family: FontAwesome;
  line-height: 20px;
  margin-right: 3px;
  font-size: 21px;
  color: #fff;
}

footer .footerCols .contact .infoBlock .item .table {
  height: 33px;
  width: auto;
  color: #fff;
  display: table-cell;
  vertical-align: middle;
  padding: 0 10px;
}

footer .footerCols .contact .infoBlock .item .table .row {
  display: table-row;
}

footer #footerSocial a {
  color: #fff !important;
}
footer .footerBottom .copyright {
  line-height: 20px;
  padding: 10px 0;
  color: #869fd4;
  font-size: 13px;
  clear: both;
}

footer .footerBottom .innerContainer {
  position: relative;
  min-height: 60px;
  padding: 0 70px 0 5px;
}

@media all and (max-width: 650px) {
  footer .footerCols .title {
    padding: 20px 9px;
  }
  footer .innerContainer {
    padding: 0 5px;
  }
  footer .footerCols .menu li {
    width: 50%;
    padding: 0;
  }
}

@media all and (max-width: 500px) {
  footer .footerCols .menu li {
    width: 100%;
  }
}

/*Read mored project button*/

.all_theme {
  width: 100%;
  -webkit-box-align: center;
  align-items: center;
  background-color: #1976d2;
  border-radius: 2em;
  border: none;
  color: #fff;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  height: 32px;
  -webkit-box-pack: center;
  justify-content: center;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
}

.all-theme:focus,
.all-theme:hover {
  background-color: #170540;
  color: #fff;
  outline: none;
}
.all_theme_span {
  font-size: 0.7579em;
}
.all_theme .all_theme_span {
  font-size: 0.8706em;
}
.all_theme1 {
  height: -webkit-calc(1.25 * var(--space));
  height: 40px;
  padding-left: 2em;
  padding-right: 2em;
  width: 29%;
}
.all_theme1 .all_theme_span {
  font-size: 0.8706em;
}

/*Pagination*/
ul.pagination {
  text-align: center;
  margin-bottom: 20px;
  padding-top: 20px;
}
ul.pagination li {
  display: inline-block;
  margin-right: 5px;
  width: 40px;
  height: 42px;
  line-height: 40px;
  text-align: center;
  background: #dde0e2;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.4s ease-in-out;
  color: #aaa;
}
ul.pagination li a {
  padding: 10px;
}
ul.pagination li:hover {
  background: #0051ab;
  color: #fff !important;
  border-color: #3f51b5;
}
ul.pagination li:hover a {
  color: #fff;
}
ul.pagination li .current {
  background: #0051ab;
  color: #fff !important;
  border-color: #3f51b5;
  display: inline-block;
  margin-right: 5px;
  width: 42px;
  height: 42px;
  line-height: 40px;
  text-align: center;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.4s ease-in-out;
}

.post-template-default .row {
  max-width: 1286px;
  margin: 0 auto;
}

/*single post style*/
.single-product-row {
  margin-bottom: 1%;
}
.main-single-post li {
  list-style-type: disc;
  margin-left: 20px;
  list-style-position: inside;
  line-height: 1.5em;
  margin-bottom: 10px;
  font-size: 16px;
  color: #43627f;
}
.main-single-post ul {
  padding: 10px;
}
#content p {
  margin: 10px 0;
  line-height: 31px;
  font-size: 18px;
  color: #43627f;
}
.toc_list li {
  padding-top: 0 !important;
  margin-left: 0 !important;
  line-height: 1.5em !important;
  margin-bottom: 3px !important;
  list-style-type: circle !important;
}
.toc_list li ul {
  padding: 2px;
}
.toc_list {
  padding: 0 !important;
}
#toc_container ul ul {
  margin-left: 1.5em;
  padding: 0 !important;
}
#toc_container {
  display: table;
  float: left;
  width: auto;
  background: #f5fafd;
  padding: 0px 11px 11px 11px;
  margin: 0 10px 10px 0;
  border-radius: 8px;
}
.toc_list li a {
  line-height: 26px;
}

.toc_list > li::marker {
  padding-right: 0;
}
.toc_depth_1::after {
  content: ".";
  font-size: 16px;
  color: #595959;
}
.blog-archive {
  background: #fff;
  overflow: hidden;
  transition: -webkit-transform 0.2s ease;
  -webkit-transition: -webkit-transform 0.2s ease;
  border-radius: 4px;
  height: auto;
  box-shadow: -1px 1px 9px 3px rgb(113 152 191 / 80%);
  clear: both;
  margin: 3% 0;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}

.blog-archive img {
  border-style: none;
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.blog-archive .content {
  padding-right: 12px !important;
  padding-left: 12px !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.related-archive {
  min-height: 235px !important;
  border-radius: 8px;
  margin: 10px;
}
.related-main .blog-archive img {
  border-style: none;
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.related-main .blog-archive .content {
  padding-right: 12px !important;
  padding-left: 12px !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  min-height: 80px;
}
.archive-blog .related-post-content {
  margin-bottom: 8px !important;
}
.archive-blog .meta-blog {
  justify-content: space-between !important;
  display: flex !important;
}
.cat-blog {
  margin-bottom: 8px !important;
  font-size: 12px;
  line-height: 1.5;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  color: #4616c7 !important;
  font-family: Space Mono, monospace;
}
.meta-blog {
  justify-content: space-between !important;
  display: flex !important;
}

.meta-blog span {
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  opacity: 0.5 !important;
  font-family: Space Mono, monospace;
  font-size: 12px;
  line-height: 1.5;
}

.related-post-content .title-blog {
  cursor: pointer;
  transition: opacity 0.1s ease-in-out;
  clear: both;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4 !important;
  width: 100%;
  opacity: 0.84 !important;
  margin: 0;
  border: none;
}

.blog-archive:hover {
  -webkit-transform: translate(0, -5px);
  -ms-transform: translate(0, -5px);
  transform: translate(0, -5px);
}

.related-post .col-lg-4 {
  margin-bottom: 0%;
  border-radius: 4px;
  float: left;
  padding: 0;
}
img {
  border-style: none;
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
.toc_toggle {
  display: none;
}
.single-post code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}
.related-main {
  overflow: hidden;
}
.related-post img {
  border-radius: 8px 8px 0 0;
}

.post-media img {
  border-radius: 8px;
}

/* end single post style*/

/*archive style*/
.bgcolor {
  background-repeat: no-repeat;
  padding-top: 128px !important;
  background-position: bottom;
  background-size: cover;
  background-color: #170540 !important;
  padding-bottom: 96px !important;
}

.feature-blog a {
  cursor: pointer;
  transition: opacity 0.1s ease-in-out;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  font-family: Space Mono, monospace;
  font-size: 12px;
  line-height: 1.5;
  color: rgb(255, 255, 255) !important;
}
.feature-blog span {
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  font-family: Space Mono, monospace;
  font-size: 12px;
  line-height: 1.5;
  margin-left: 16px !important;
  color: hsla(0, 0%, 100%, 0.5) !important;
}
.feature-image img {
  width: 100%;
}
.feature-blog h2 {
  max-width: 410px;
  margin-bottom: 8px !important;
  margin-top: 16px !important;
  font-size: 26px !important;
  font-weight: 600 !important;
  line-height: 1.31 !important;
}

.feature-blog p {
  opacity: 0.84 !important;
  font-size: 15px;
  line-height: 1.9;
  color: #f6f6f6;
}
.meta-head-blog {
  opacity: 0.72 !important;
}
.feature-blog .doc-tiep:hover {
  background: #2d0f80;
}
.feature-blog .doc-tiep {
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  font-family: Montserrat, sans-serif;
  display: inline-block;
  border-radius: 3px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  padding: 8px 14px;
  cursor: pointer;
  user-select: none;
  text-decoration: none;
  transition-property: all;
  transition-duration: 0.2s;
  transition-timing-function: cubic-bezier(0.64, 0, 0.35, 1);
  outline: none;
  border: 0;
  overflow: hidden;
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
  color: hsla(0, 0%, 100%, 0.5) !important;
  -webkit-appearance: button;
  background: #4e31df;
  margin: 6% 0;
}

.vts-nav {
  padding-left: 4px;
}

.related-singlepost {
  padding-right: 9px !important;
}

.blog-archive .excerpt-blog {
  margin-top: 8px !important;
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.72 !important;
}

/* end archive style*/

.login-form-2 input {
  border-radius: 45px !important;
}
.login-form-1 h3,
.login-form-2 h3 {
  font-size: 20px;
  padding-top: 15px;
}

#ez-toc-container {
  display: table;
  float: left;
  width: auto;
  background: #f5fafd;
  padding: 16px;
  margin: 0 10px 10px 0;
  border-radius: 8px;
  border: none;
}
.main-single-page h1 {
  font-size: 22px;
}
.main-single-page h2 {
  font-size: 20px;
  padding: 18px 0;
}
.main-single-page h3 {
  font-size: 18px;
  padding: 18px 0;
}
.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/*Home button*/
a {
  cursor: pointer;
  background: transparent;
  text-decoration: none;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}
::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar-thumb {
  background-image: linear-gradient(45deg, #ffbd84, #ff1f8e);
}
::-webkit-scrollbar-track {
  background: #e8e8e8;
}

.icon-angle-right-effect {
  margin-left: 5px;
  animation: 2s infinite ArrowAnimation;
  position: relative;
  top: -2px;
}

.btn-custom {
  background-image: linear-gradient(
    to left top,
    #017bda,
    #9f6bd0,
    #e15aa8,
    #fd5f71,
    #f37f3a
  );
  color: #fff;
  position: relative;
  display: inline-block;
  font-weight: 500;
  text-align: center;
  text-transform: capitalize;
  height: 48px;
  line-height: 48px;
  border: none;
  outline: none;
  border-radius: 48px;
  padding: 0 20px;
  -webkit-box-shadow: 0 10px 15px 0 rgba(56, 0, 189, 0.2);
  box-shadow: 0 10px 15px 0 rgba(56, 0, 189, 0.2);
  -webkit-transition: 1.2s cubic-bezier(0.17, 0.85, 0.438, 0.99);
  transition: 1.2s cubic-bezier(0.17, 0.85, 0.438, 0.99);
  overflow: hidden;
  cursor: pointer;
}
.btn-custom::before {
  background: rgba(255, 255, 255, 0.2);
  content: "";
  display: block;
  position: absolute;
  top: -10%;
  right: -130px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  -webkit-transition: 1.2s cubic-bezier(0.17, 0.85, 0.438, 0.99);
  transition: 1.2s cubic-bezier(0.17, 0.85, 0.438, 0.99);
}
.btn-custom i {
  font-size: 12px;
}

.btn-custom:hover {
  color: #fff;
}
.btn-custom:hover::before {
  top: -10%;
  right: -80px;
}
.btn-custom2 {
  background-image: linear-gradient(to left, #017bda 0, #2196f3 100%);
}

@media (max-width: 768px) {
    .back-to-top {
        width: 40px;
    }

    .back-to-top svg {
        height: 30px;
        width: 30px;
    }
}


@media (max-width: 767px) {
  .breadcrumb-btn .btn__custom {
    padding-left: 10px;
    padding-right: 12px;
  }

  .breadcrumb-btn .btn__custom:first-child {
    margin-right: 15px;
  }
  .breadcrumb-btn .icon-angle-right-effect {
    margin-left: 0;
  }

  .post-template-default #content > .container-fluid {
    padding: 0 !important;
    margin: 0 !important;
  }
}

@media (min-width: 768px) {
  .breadcrumb-btn .btn__custom {
    min-width: 220px;
    margin-bottom: 15px;
  }
  .breadcrumb-btn .btn__custom:first-child {
    margin-right: 25px;
  }
}

@-webkit-keyframes FadeInDown {
  from {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
    opacity: 1;
  }
}
@keyframes FadeInDown {
  from {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
    opacity: 1;
  }
}
@-webkit-keyframes ArrowAnimation {
  0%,
  100% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  50% {
    -webkit-transform: translate(8px, 0);
    transform: translate(8px, 0);
  }
}
@keyframes ArrowAnimation {
  0%,
  100% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  50% {
    -webkit-transform: translate(8px, 0);
    transform: translate(8px, 0);
  }
}
@keyframes kfTextFillBg {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 2000px 0;
  }
}

.post-template-full-width .related-post-content .title-blog {
  font-size: 15px;
  font-weight: 400;
}

.post-template-full-width .related-main > div {
  padding-right: 0;
  padding-left: 0;
}


