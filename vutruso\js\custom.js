jQuery(document).ready(function($) {
    // Tab reviews single Woo
    $('ul.tabs li a').on('click', function() {
        var who = $(this).attr('data');
        $('ul.tabs li a').removeClass('active');
        $(this).addClass('active');
        $('.tab1,.tab2,.tab3,.tab4').hide();
        $('.' + who).fadeIn();
        return false;
    });
    
    //Bao gia
    $('#gui-yeu-cau').click(function() {
        $('#form-yeu-cau').fadeIn(300);
    });
    $('#tat').click(function() {
        $('#form-yeu-cau').fadeOut(300);
    });

    //Bao gia
    $('#bao-gia').click(function() {
        $('#form-bao-gia').fadeIn(300);
    });
    $('#close').click(function() {
        $('#form-bao-gia').fadeOut(300);
    });

    // Search overlay
    $('#close-btn').click(function() {
        $('#search-overlay').fadeOut();
        $('#search-btn').show();
    });
    $('#search-btn').click(function() {
        $(this).hide();
        $('#search-overlay').fadeIn();
    });

    // Contact float slide
    var selector = $('#wp-nt-vts-wrapper');
    selector.on('click', '.js__nt_vts_active', function() {
        if (selector.hasClass('nt-vts-active')) {
            selector.removeClass('nt-vts-active').removeClass('nt-vts-show-list').removeClass('nt-vts-show-popup');
        } else {
            selector.addClass('nt-vts-active').addClass('nt-vts-show-list');
        }
        return false;
    });
    
    // mobile menu
    $('.navbar-toggle').click(function(e){
        e.preventDefault();
        // Sử dụng requestAnimationFrame để cải thiện hiệu suất INP
        requestAnimationFrame(function() {
            $(".navbar-toggle").toggleClass("active");
            $(".navbar-push").toggleClass("in");
        });
    });

    $('#mobile_close_button').click(function(e){
        e.preventDefault();
        // Lưu tham chiếu đến this trước khi sử dụng trong requestAnimationFrame
        var $this = $(this);
        // Sử dụng requestAnimationFrame để cải thiện hiệu suất
        requestAnimationFrame(function() {
            $this.parent().removeClass('in');
        });
    });

    // Listen for clicks on dropdown toggles
    $('.dropdown-toggle').click(function(e) {
        e.preventDefault(); // Prevent the default link action

        // Sử dụng requestAnimationFrame để cải thiện hiệu suất
        requestAnimationFrame(function() {
            // Find the nearest dropdown menu to the clicked link
            var $dropdownMenu = $(this).closest('.dropdown').find('.dropdown-menu');

            // Toggle the 'mobile-active' class on the dropdown menu
            $dropdownMenu.toggleClass('mobile-active');
        }.bind(this));
    });

    // Ensure clicks within the dropdown menu are allowed to proceed
    $('.dropdown-menu').click(function(e) {
        e.stopPropagation(); // Prevent the click from closing the dropdown
    });

    // Optional: Close the dropdown menu when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').removeClass('mobile-active');
        }
    });

    //sidebar tabs
    $('.vts_tabs li').click(function(){
        var tab_id = $(this).attr('data-tab');

        $('.vts_tabs li').removeClass('active show');
        $('.tab-pane').removeClass('active show');

        $(this).addClass('active show');
        $("#"+tab_id).addClass('active show');
    });

    //woocomerce
    $('#username').attr('placeholder', 'Tên hoặc email đăng nhập');
    $('#password').attr('placeholder', 'Mật khẩu');

    //Yt responsive iframe
    $(".vts-single-content iframe").wrap('<div class="EmbedContainer"></div>');

    //Focus overlay search input
    $("#search-btn").click(function() {
        $("#search-overlay").fadeIn();
        $('#search-text').trigger('focus');
    });

    $("#close-btn").click(function() {
        $("#search-overlay").fadeOut();
        $('#search-text').trigger('blur');
    });

    // tabbed content tang toc wp
    $(".tab_content").hide();
    $(".tab_content:first").show();
    /* if in tab mode */
    $("ul.tabs li").click(function() {
        $(".tab_content").hide();
        var activeTab = $(this).attr("rel");
        $("#" + activeTab).fadeIn();
        $("ul.tabs li").removeClass("active");
        $(this).addClass("active");
        $(".tab_drawer_heading").removeClass("d_active");
        $(".tab_drawer_heading[rel^='" + activeTab + "']").addClass("d_active");
    });
    $(".tab_container").css("min-height", function() {
        return $(".tabs").outerHeight() + 50;
    });
    /* if in drawer mode */
    $(".tab_drawer_heading").click(function() {
        $(".tab_content").hide();
        var d_activeTab = $(this).attr("rel");
        $("#" + d_activeTab).fadeIn();
        $(".tab_drawer_heading").removeClass("d_active");
        $(this).addClass("d_active");
        $("ul.tabs li").removeClass("active");
        $("ul.tabs li[rel^='" + d_activeTab + "']").addClass("active");
    });
});

// ===== DOMContentLoaded Events - Section 1 =====
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash) {
        setTimeout(function() {
            window.scrollTo(0, 0);
        }, 1);
    }

    const tocLinks = document.querySelectorAll('#vts-toc-article a');
    if (tocLinks) {
        tocLinks.forEach(function(anchor) {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const hash = this.getAttribute('href');
                const target = document.querySelector(hash);

                if (target) {
                    const offset = target.getBoundingClientRect().top + window.pageYOffset - 20;
                    window.scrollTo({
                        top: offset,
                        behavior: 'smooth'
                    });
                    history.pushState(null, null, hash);
                }
            });
        });
    }

    const toggleButton = document.getElementById('toggle-read-more');
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            const container = document.getElementById('vts-toc-container');
            if (container) {
                container.classList.toggle('show');
                this.textContent = container.classList.contains('show') ? 'Thu gọn' : 'Xem thêm';
            }
        });
    }
});


// ===== Footer Menu Toggle =====
document.addEventListener('DOMContentLoaded', function() {
    const dichvus = document.querySelectorAll('.footermenu');
    
    if (dichvus.length > 0) {
        dichvus.forEach(dichvu => {
            const title = dichvu.querySelector('.title');
            
            // Chỉ bind event cho title thay vì toàn bộ container
            if (title) {
                title.addEventListener('click', handleMenuToggle, { passive: true });
                
                // Thêm cursor pointer cho title
                title.style.cursor = 'pointer';
                
                // Thêm aria attributes cho accessibility
                title.setAttribute('aria-expanded', dichvu.classList.contains('active'));
                title.setAttribute('role', 'button');
                title.setAttribute('tabindex', '0');
                
                // Support keyboard navigation
                title.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleMenuToggle.call(this, e);
                    }
                }, { passive: false });
            }
        });
    }
    
    // Optimized toggle handler
    function handleMenuToggle(e) {
        e.stopPropagation();
        
        const dichvu = this.closest('.footermenu');
        const catMenu = dichvu.querySelector('.catMenu');
        
        if (!dichvu || !catMenu) return;
        
        // Use requestAnimationFrame for smooth animation
        requestAnimationFrame(() => {
            const isActive = dichvu.classList.contains('active');
            
            // Toggle class
            dichvu.classList.toggle('active');
            
            // Update aria-expanded
            this.setAttribute('aria-expanded', !isActive);
            
            // Optimize animation with transform instead of display
            if (!isActive) {
                catMenu.style.maxHeight = catMenu.scrollHeight + 'px';
                catMenu.style.opacity = '1';
            } else {
                catMenu.style.maxHeight = '0';
                catMenu.style.opacity = '0';
            }
        });
    }
    
    // Debounce function for multiple rapid clicks
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});



// ===== Back to Top Button =====
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        const backToTop = document.getElementById('back-to-top');

        if (backToTop) {
            // Show/hide button based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 100) {
                    backToTop.classList.add('active-to-top');
                } else {
                    backToTop.classList.remove('active-to-top');
                }
            });

            // Smooth scroll to top
            backToTop.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    });
})();


document.addEventListener("DOMContentLoaded", function() {
    // Handle URL copy button
    const copyTextBtn = document.querySelector(".copy-text button");
    if (copyTextBtn) {
        copyTextBtn.addEventListener("click", function() {
            const copyText = document.querySelector(".copy-text");
            const input = copyText.querySelector("input.text");
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(input.value)
                .then(() => {
                    copyText.classList.add("active");
                    setTimeout(() => {
                        copyText.classList.remove("active");
                    }, 2500);
                })
                .catch(err => {
                    console.error('Failed to copy: ', err);
                });
            } else {
                // Fallback for older browsers
                input.select();
                document.execCommand("copy");
                copyText.classList.add("active");
                window.getSelection().removeAllRanges();
                setTimeout(() => {
                    copyText.classList.remove("active");
                }, 2500);
            }
        });
    }
    
    // Handle code copy buttons
    const preTags = document.querySelectorAll('pre');
    preTags.forEach(function(preTag) {
        // Kiểm tra xem đã có copy button chưa để tránh tạo trùng
        if (preTag.querySelector('.copy-button')) {
            return;
        }
        
        // Lưu nội dung gốc trước khi thêm copy button
        const originalContent = preTag.textContent;
        
        const copyButton = document.createElement("span");
        copyButton.innerText = "Copy";
        copyButton.classList.add("copy-button");
        copyButton.style.cssText = `
            position: absolute;
            top: 1px;
            right: 1px;
            background: rgb(0, 124, 186);
            color: white;
            padding: 1px 9px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            z-index: 10;
        `;
        
        // Đặt position relative cho pre tag nếu chưa có
        if (getComputedStyle(preTag).position === 'static') {
            preTag.style.position = 'relative';
        }
        
        preTag.appendChild(copyButton);
        
        copyButton.addEventListener("click", (e) => {
            e.stopPropagation();
            
            // Sử dụng nội dung gốc đã lưu thay vì lấy từ textContent hiện tại
            const codeText = originalContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(codeText)
                .then(() => {
                    copyButton.innerText = "Copied!";
                    copyButton.style.background = "#28a745";
                    setTimeout(() => {
                        copyButton.innerText = "Copy";
                        copyButton.style.background = "#007cba";
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy: ', err);
                });
            } else {
                // Fallback - tạo textarea ẩn để copy
                const textarea = document.createElement('textarea');
                textarea.value = codeText;
                textarea.style.position = 'fixed';
                textarea.style.opacity = '0';
                document.body.appendChild(textarea);
                textarea.select();
                
                try {
                    document.execCommand("copy");
                    copyButton.innerText = "Copied!";
                    copyButton.style.background = "#28a745";
                    setTimeout(() => {
                        copyButton.innerText = "Copy";
                        copyButton.style.background = "#007cba";
                    }, 2000);
                } catch (err) {
                    console.error("Unable to copy text:", err);
                } finally {
                    document.body.removeChild(textarea);
                }
            }
        });
    });
});


document.addEventListener('DOMContentLoaded', function() {
    // Lấy tất cả các tab-link
    var tabLinks = document.querySelectorAll('.vts_tabs .tab-link');
    
    // Thêm sự kiện click cho mỗi tab
    tabLinks.forEach(function(tab) {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Lấy ID của tab-pane tương ứng
            var tabId = this.getAttribute('data-tab');
            
            // Ẩn tất cả tab-pane
            var tabPanes = document.querySelectorAll('.tab-content .tab-pane');
            tabPanes.forEach(function(pane) {
                pane.classList.remove('active', 'show');
            });
            
            // Xóa class active cho tất cả tab links
            tabLinks.forEach(function(link) {
                link.classList.remove('active', 'show');
            });
            
            // Hiển thị tab-pane tương ứng
            var activePane = document.getElementById(tabId);
            if (activePane) {
                activePane.classList.add('active', 'show');
            }
            
            // Thêm class active cho tab được click
            this.classList.add('active', 'show');
        });
    });
});