<?php

/* Login Page
*===============================================================*/
  function vts_login_logo_url() {return home_url();}
  add_filter( 'login_headerurl', 'vts_login_logo_url' );

  function vts_loginlogo() {
    echo '<style type="text/css">
      h1 a {
        background-image: url(https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg) !important;
      }
   .login h1 a {
      background-size: 320px;
      background-position: center top;
      background-repeat: no-repeat;
      color: #444;
      height: 130px;
      font-size: 20px;
      font-weight: 400;
      line-height: 1.3;
      margin: 0 auto 2px;
      padding: 0;
      text-decoration: none;
      width: 320px;
      text-indent: -9999px;
      outline: 0;
      overflow: hidden;
      display: block;
  }
      
    </style>';
  }
  add_action('login_head', 'vts_loginlogo');


  function vts_login_footer() {
  echo '<div class="container">
    <div class="blue-planet"></div>
    <div class="blue-planet1"></div>
    <div class="star-1"></div>
    <div class="star-2"></div>
    <div class="star-3"></div>
  </div>
  <div class="small-stars">
    <div class="small-star" id="small-star-1"></div>
    <div class="small-star" id="small-star-2"></div>
    <div class="small-star" id="small-star-3"></div>
    <div class="small-star" id="small-star-4"></div>
    <div class="small-star" id="small-star-5"></div>
    <div class="small-star" id="small-star-6"></div>
  </div>
  <div class="white-gradient"></div>';
  }
  add_action('login_headertext', 'vts_login_footer');


  function vts_login_stylesheet() {
      wp_enqueue_style( 'vts-login', get_stylesheet_directory_uri() . '/inc/login/login.css' );
      wp_enqueue_script( 'vts-login', get_stylesheet_directory_uri() . '/inc/login/login.js', array('jquery'), '1.0', true );
  }
  add_action( 'login_enqueue_scripts', 'vts_login_stylesheet' );


 /* Remove notice
*======================================================================*/
add_action('admin_head', 'vts_admin_page');
function vts_admin_page() {
    echo '<style>
        .taxonomy-category #edittag {
          max-width: 1200px;
      }
    </style>';
}