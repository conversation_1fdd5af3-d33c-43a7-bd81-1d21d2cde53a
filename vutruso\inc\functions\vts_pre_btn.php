<?php
/**
 * Add Pre QuickTag button to WordPress editor
 */
function add_quicktags_to_text_editor() {
    if (!wp_script_is('quicktags')) {
        return;
    }
    ?>
    <script type="text/javascript">
        if (typeof QTags !== 'undefined') {
            QTags.addButton(
                'qt_content_pre',       // ID của button
                'Pre',                  // Text hiển thị trên button
                '<pre>',               // Starting tag
                '</pre>',              // Ending tag
                '',                    // Extra attributes
                'Pre formatted text',  // Title của button
                111                    // Vị trí của button
            );
        }
    </script>
    <?php
}
add_action('admin_print_footer_scripts', 'add_quicktags_to_text_editor', 100);