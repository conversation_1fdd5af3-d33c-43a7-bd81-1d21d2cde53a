<?php

/* ========================================
   COMPLETE FUNCTIONS.PHP - NO LOST FEATURES
   - All original functions preserved
   - Only added conflict protection
   - No functionality removed
========================================= */

// Preload image product_thumbnail
add_action('wp_head', function() {
    if (is_product() && function_exists('is_woocommerce')) {
        if (function_exists('preload_current_product_thumbnail')) {
            preload_current_product_thumbnail();
        }
    }
}, 1);

if (!function_exists('preload_current_product_thumbnail')) {
    function preload_current_product_thumbnail() {
        if (!is_product()) return;

        global $product;

        if (!$product || !is_a($product, 'WC_Product')) return;

        $thumbnail_id = $product->get_image_id();
        if (!$thumbnail_id) return;

        $image_url = wp_get_attachment_image_url($thumbnail_id, 'woocommerce_thumbnail');

        if ($image_url) {
            echo '<link rel="preload" as="image" href="' . esc_url($image_url) . '" fetchpriority="high">' . "\n";
        }
    }
}

// Toi uu heartbeat
if (!function_exists('optimize_heartbeat_for_basic_site')) {
    add_action( 'init', 'optimize_heartbeat_for_basic_site' );
    function optimize_heartbeat_for_basic_site() {
        global $pagenow;
        
        // Tắt hoàn toàn ở frontend (không cần thiết cho visitor)
        if ( ! is_admin() ) {
            wp_deregister_script('heartbeat');
            return;
        }
        
        // Trong admin, chỉ giữ ở trang edit post/product
        $allowed_pages = array( 'post.php', 'post-new.php' );
        
        if ( ! in_array( $pagenow, $allowed_pages ) ) {
            wp_deregister_script('heartbeat');
        }
    }
}

// Giảm tần suất khi cần thiết (ở trang edit)
if (!function_exists('slow_down_heartbeat')) {
    add_filter( 'heartbeat_settings', 'slow_down_heartbeat' );
    function slow_down_heartbeat( $settings ) {
        $settings['interval'] = 60; // 1 phút thay vì 15 giây
        return $settings;
    }
}

/**
 * Defer JavaScript files except jQuery and other critical scripts
 */
if (!function_exists('defer_js_except_jquery')) {
    function defer_js_except_jquery($tag, $handle, $src) {
        // Không áp dụng trong admin
        if (is_admin()) {
            return $tag;
        }
        
        // Danh sách các script không nên defer
        $excluded_handles = array(
            //'jquery',
            //'jquery-core',
            //'jquery-migrate',
            'wp-polyfill',
            //'wp-hooks',

        );
        
        // Kiểm tra nếu handle nằm trong danh sách loại trừ
        foreach ($excluded_handles as $excluded) {
            if (strpos($handle, $excluded) !== false) {
                return $tag;
            }
        }
        
        // Kiểm tra nếu script đã có defer hoặc async
        if (strpos($tag, 'defer') !== false || strpos($tag, 'async') !== false) {
            return $tag;
        }
        
        // Chỉ áp dụng cho file .js và không phải inline script
        if (strpos($tag, '.js') !== false && !empty($src)) {
            return str_replace(' src', ' defer src', $tag);
        }
        
        return $tag;
    }
    add_filter('script_loader_tag', 'defer_js_except_jquery', 10, 3);
}

if (!function_exists('sp_social_og_locale')) {
    function sp_social_og_locale($html) { 
        //you can add here all your conditions as if is_page(), is_category() etc.. 
        $html = '<meta property="og:locale" content="vi_VN" />';
        return $html;
    }
    add_filter('seopress_social_og_locale', 'sp_social_og_locale');
}

add_action( 'after_setup_theme', function() {
    remove_theme_support( 'core-block-patterns' );
} );

add_filter( 'should_load_remote_block_patterns', '__return_false' );

// Tắt hoàn toàn WooCommerce Blocks
add_filter('woocommerce_blocks_enabled', '__return_false');

// Tắt block editor cho WooCommerce
add_filter('woocommerce_admin_disabled', '__return_true');

// Tắt các tính năng blocks cụ thể
add_filter('woocommerce_admin_features', function($features) {
    return array_diff($features, [
        'product-block-editor',
        'checkout-blocks',
        'cart-blocks'
    ]);
});

// Vô hiệu hóa các cron events cụ thể
if (!function_exists('disable_specific_cron_events')) {
    function disable_specific_cron_events() {
        // Danh sách các cron hooks cần vô hiệu hóa
        $cron_hooks_to_disable = array(
            'seopress_google_analytics_cron',
            'seopress_matomo_analytics_cron',
            'wp_update_themes',
            'woocommerce_marketplace_cron_fetch_promotions',
            'seopress_page_speed_insights_cron',
            'jetpack_v2_heartbeat',
            'seopress_insights_gsc_cron',
            'woocommerce_geoip_updater',
            'jetpack_clean_nonces'
        );

        foreach ( $cron_hooks_to_disable as $hook ) {
            // Xóa tất cả các events đã lên lịch của hook này
            wp_clear_scheduled_hook( $hook );
            
            // Ngăn hook này được lên lịch lại trong tương lai
            add_filter( 'schedule_event', function( $event ) use ( $hook ) {
                if ( isset( $event->hook ) && $event->hook === $hook ) {
                    return false;
                }
                return $event;
            } );
        }
    }
    add_action( 'init', 'disable_specific_cron_events' );
}

/* Register JS/CSS - Optimized Single Function
*===============================================================*/
if (!function_exists('vts_optimized_scripts')) {
    function vts_optimized_scripts() {
        
        // ========== ENQUEUE STYLES & SCRIPTS (Priority 10) ==========
        
        // Enqueue CSS files
        wp_enqueue_style('vts-fontawesome', get_template_directory_uri() . '/css/font-awesome.min.css');
        wp_enqueue_style('layout-style', get_template_directory_uri() . '/assets/css/layout.css', array(), '1.0.4');
        wp_enqueue_style('vts-style', get_stylesheet_uri() . '?v=1.5');
        wp_enqueue_style('vts-woo', get_template_directory_uri() . '/assets/css/woo.css', array(), '1.0.3');
        wp_enqueue_style('vts-mobile-menu', get_template_directory_uri() . '/assets/css/mobile.css');
        wp_enqueue_style('custom-style', get_template_directory_uri() . '/assets/css/custom.css', array(), '1.1.9');

        // Enqueue JS files
        wp_enqueue_script('jquery');
        wp_enqueue_script('vts-custom', get_template_directory_uri() . '/js/custom.js?v=1.1', array(), '', true);

        // Conditional styles for home page
        if (is_home() || is_front_page()) {
            wp_enqueue_style('vts-home-styles', get_template_directory_uri() . '/css/home.css', array(), '1.0.0');
        }

        // ========== REMOVE DEFAULT WORDPRESS STYLES (Priority 100) ==========
        
        // Remove classic theme styles
        wp_dequeue_style('classic-theme-styles');
        wp_deregister_style('classic-theme-styles');
        
        // Remove global styles
        wp_dequeue_style('global-styles');
        wp_deregister_style('global-styles');

        // Remove WooCommerce inline CSS
        add_filter('woocommerce_enqueue_styles', '__return_empty_array');
        wp_dequeue_style('woocommerce-inline');
        remove_action('wp_head', 'woocommerce_dark_mode_inline_css');

        // Remove SEOPress styles
        wp_dequeue_style('wpseopress-local-business-style');
        wp_dequeue_style('wpseopress-table-of-contents-style');
        
        // Remove SEOPress filters
        add_filter('seopress_faq_block_inline_css', '__return_false');
        add_action('seopress_pro_breadcrumbs_css', '__return_false');

        // ========== CONDITIONAL DEQUEUE (Priority 999) ==========
        
        // WooCommerce conditional dequeue
        if (function_exists('is_woocommerce')) {
            if (!is_woocommerce() && !is_cart() && !is_checkout()) {
                // Dequeue WooCommerce styles
                $woo_styles = [
                    'woocommerce-general', 'woocommerce-layout', 'woocommerce-smallscreen',
                    'woocommerce_frontend_styles', 'woocommerce_fancybox_styles',
                    'woocommerce_chosen_styles', 'woocommerce_prettyPhoto_css'
                ];
                foreach ($woo_styles as $style) {
                    wp_dequeue_style($style);
                }

                // Dequeue WooCommerce scripts
                $woo_scripts = [
                    'wc_price_slider', 'wc-single-product', 'wc-add-to-cart', 'wc-checkout',
                    'wc-add-to-cart-variation', 'wc-cart', 'wc-chosen', 'woocommerce',
                    'prettyPhoto', 'prettyPhoto-init', 'jquery-blockui', 'jquery-placeholder',
                    'fancybox', 'jqueryui'
                ];
                foreach ($woo_scripts as $script) {
                    wp_dequeue_script($script);
                }
            }
        }

        // Page specific dequeue
        if (!is_page('28')) {
            wp_dequeue_script('wc-cart-fragments');
            wp_dequeue_script('wc-cart-fragments-js-extra');
        }

        if (is_page('2684')) {
            wp_dequeue_style('vts-woo-css');
        }

        // Single post condition
        if (!is_single()) {
            wp_dequeue_script('toc-front');
        }

        // Home/Front page conditions
        if (is_home() || is_front_page()) {
            $home_scripts = ['venobox-start', 'venobox-wp'];
            $home_styles = ['venobox-wp', 'vts-woo'];
            
            foreach ($home_scripts as $script) {
                wp_dequeue_script($script);
            }
            foreach ($home_styles as $style) {
                wp_dequeue_style($style);
            }
        }

        // Remove dashboard icons
        wp_dequeue_style('dashicons');

        // Remove WooCommerce blocks styles
        $wc_block_styles = [
            'wc-blocks-vendors-style', 'wc-blocks-style', 'wc-blocks-style-product-sale-badge',
            'wc-blocks-style-product-search', 'wc-blocks-style-product-sku',
            'wc-blocks-style-product-stock-indicator', 'wc-blocks-style-product-summary',
            'wc-blocks-style-product-title', 'wc-blocks-style-rating-filter',
            'wc-blocks-style-reviews-by-category', 'wc-blocks-style-reviews-by-product',
            'wc-blocks-style-product-details', 'wc-blocks-style-single-product',
            'wc-blocks-style-stock-filter', 'wc-blocks-style-cart', 'wc-blocks-style-checkout',
            'wc-blocks-style-mini-cart-contents', 'bsearch-style'
        ];
        
        foreach ($wc_block_styles as $style) {
            wp_dequeue_style($style);
        }
    }

    // Single action hook with appropriate priority
    add_action('wp_enqueue_scripts', 'vts_optimized_scripts', 999);
}

// Remove <style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
remove_action( 'wp_head', 'wp_print_auto_sizes_contain_css_fix', 1 );

// Disable Speculative Loading
add_filter(
    'wp_speculation_rules_configuration',
    function ( $config ) {
        return null; // Trả về null để tắt hoàn toàn Speculative Loading
    }
);

add_filter( 'rocket_minify_html_options', function( $options ) {
    if ( ! isset( $options['ignoreCustomComments'] ) ) {
        $options['ignoreCustomComments'] = array();
    }
    
    // Thêm thẻ pre vào danh sách loại trừ
    $options['excludes'][] = '<pre';
    $options['excludes'][] = '</pre>';
    $options['excludes'][] = '<code';
    $options['excludes'][] = '</code>';

    return $options;
} );

// Cho phép HTML trong mô tả của các taxonomy
if (!function_exists('allow_html_in_term_descriptions')) {
    function allow_html_in_term_descriptions() {
        // Xóa bộ lọc mặc định
        remove_filter('pre_term_description', 'wp_filter_kses');
        remove_filter('term_description', 'wp_kses_data');
        
        // Thêm bộ lọc cho phép HTML
        add_filter('pre_term_description', 'wp_kses_post');
        add_filter('term_description', 'wptexturize');
        add_filter('term_description', 'convert_chars');
        add_filter('term_description', 'wpautop');
    }
    add_action('init', 'allow_html_in_term_descriptions');
}

add_filter('pre_determine_locale', function($locale) {
    if (is_admin()) {
        return 'vi';
    }
    return $locale;
});

// Trong functions.php
if (!function_exists('add_canonical_tags')) {
    function add_canonical_tags() {
        if (is_paged()) {
            $canonical = get_pagenum_link(1);
            echo '<link rel="canonical" href="' . esc_url($canonical) . '" />';
        }
    }
    add_action('wp_head', 'add_canonical_tags');
}

/* Chặn wp-rocket call API
*===============================================================*/
add_filter('pre_http_request', function($pre, $parsed_args, $url) {
    if (strpos($url, 'api.wp-rocket.me/check_update.php') !== false) {
        return new WP_Error('blocked', 'API call blocked');
    }
    return $pre;
}, 10, 3);

/* Translate
*===============================================================*/
if (!function_exists('vutruso_custom_translations')) {
    function vutruso_custom_translations( $strings ) {
        $text = array(
          'Category Archives:'						=> '',
          'vote'                                            => 'Đánh giá',
          'Related products'                        => 'Mẫu website tương tự',
          'Proceed to checkout'                 => 'Tiến hành thanh toán',
          'Cart totals'                                 => 'Tổng tiền',
          'Apply coupon'                            => 'Áp dụng',
          'Categories:'                                  => 'Danh mục:'
      
        );
        $strings = str_ireplace( array_keys( $text ), $text, $strings );
          return $strings;
        }
    add_filter( 'gettext', 'vutruso_custom_translations', 20 );
}

// Remove the hook that displays the "clear cache" message.
add_action( 'admin_notices', function() {
    remove_action( 'admin_notices', 'rocket_warning_plugin_modification' );
}, 1 );

// x2 preloading - wp-rocket
if (!function_exists('vutruso_x2_preloading')) {
    function vutruso_x2_preloading( $concurrent_batches ) {
        return $concurrent_batches * 2;
    }
    add_filter( 'action_scheduler_queue_runner_concurrent_batches', 'vutruso_x2_preloading' );
}

/* Chặn cài đặt mới theme/plugin
*===============================================================*/
if (!function_exists('vutruso_disable_installation')) {
    add_action('admin_init', 'vutruso_disable_installation');
    function vutruso_disable_installation() {
        // Hide menu plugin and theme install
        remove_menu_page('plugin-install.php');
        remove_menu_page('theme-install.php');
        
        // Block all users from installing plugins and themes
        add_filter('user_has_cap', function($allcaps, $cap, $args) {
            if (isset($args[0]) && ('install_plugins' === $args[0] || 'install_themes' === $args[0])) {
                $allcaps[$cap[0]] = false;
            }
            return $allcaps;
        }, 10, 3);

        // Redirect from install pages if someone tries to access directly
        global $pagenow;
        if ($pagenow == 'plugin-install.php' || $pagenow == 'theme-install.php') {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
    }
}

/* Show các bài viết mới sửa lên đầu
*===============================================================*/
if (!function_exists('vutruso_modify_query_order')) {
    add_action('pre_get_posts', 'vutruso_modify_query_order');
    function vutruso_modify_query_order($query) {
        // Kiểm tra xem chúng ta không ở trong trang quản trị và đây là truy vấn chính
        if (!is_admin() && $query->is_main_query()) {
            // Áp dụng cho trang chủ, trang lưu trữ, danh mục và thẻ
            if (is_home() || is_archive() || is_category() || is_tag()) {
                // Sắp xếp bài viết theo ngày sửa đổi gần đây nhất
                $query->set('orderby', 'modified');
                $query->set('order', 'DESC');

                // Bao gồm cả bài viết riêng tư nếu cần
                $query->set('post_status', array('publish'));

                // Ghi log để gỡ lỗi
                //error_log('Đã sửa đổi truy vấn: ' . print_r($query->query_vars, true));
            }
        }
    }
}

/* Disable REST API
*===============================================================*/
// https://vutruso.com/wp-json/wp/v2/posts
// https://vutruso.com/wp-json/wp/v2/pages

//add_filter( 'rest_authentication_errors', 'vutruso_turn_off_rest_api_not_logged_in' );
if (!function_exists('vutruso_turn_off_rest_api_not_logged_in')) {
    function vutruso_turn_off_rest_api_not_logged_in( $errors ) {
        // if there is already an error, just return it
        if( is_wp_error( $errors ) ) {
            return $errors;
        }

        //if( ! current_user_can( 'administrator' ) ) {
        if( ! is_user_logged_in() ) {
            // return WP_Error object if user is not logged in
            return new WP_Error( 'no_rest_api_sorry', 'REST API not allowed', array( 'status' => 401 ) );
        }
        
        return $errors;
        
    }
}

/* Remove pagination on homepage
*===============================================================*/
if (!function_exists('vutruso_remove_homepage_pagination')) {
    function vutruso_remove_homepage_pagination($query) {
        if ($query->is_main_query() && is_front_page()) {
            $query->set('no_found_rows', true);
            $query->set('posts_per_archive_page', 1); // Adjust number accordingly
        }
    }
    add_action('pre_get_posts', 'vutruso_remove_homepage_pagination');

    // redirect cac page ve home
    function vutruso_redirect_homepage_pagination() {
        if (is_front_page() && (get_query_var('paged') > 1 || get_query_var('page') > 1)) {
            wp_redirect(home_url(), 301);
            exit;
        }
    }
    add_action('template_redirect', 'vutruso_redirect_homepage_pagination');
}

// khong nen anh jpeg
add_filter( 'jpeg_quality', function ( $arg ) {
    return 100;
} );

//Big Image Size
add_filter( 'big_image_size_threshold', function() {
    return 4000; // Hoặc false để tắt hoàn toàn
}, 999 );

add_filter('http_request_args', 'block_specific_api_calls', 10, 2);
add_filter('pre_http_request', 'kevinlearynet_filter_slow_http_requests', 99, 3);

// block api calls
if (!function_exists('block_specific_api_calls')) {
    function block_specific_api_calls($args, $url) {
        $blocked_urls = array(
            'https://objectcache.pro/api/plugin/update',
            'https://public-api.wordpress.com/wpcom/v2/wcpay/incentives',
        );

        if (in_array($url, $blocked_urls)) {
            // Thay vì timeout 0, return false để block hoàn toàn
            return new WP_Error('blocked', 'API call blocked');
        }
        return $args;
    }
}

/* Them rss page to main feed
*===============================================================*/
if (!function_exists('vts_include_pages_in_rss_feed')) {
    add_filter( 'pre_get_posts', 'vts_include_pages_in_rss_feed' );
    function vts_include_pages_in_rss_feed( $query ) {
        if ( $query->is_feed() ) {
          $exclude_page_ids = array( 22166, 30197, 3, 29, 627, 27, 28, 639, 3274, 3035, 479, 3280, 7886, 3277, 626, 3202, 2653, 6568, 2678, 28702 ); //  IDs of the page
          $query->set( 'post_type', array( 'post', 'page' ) );
          $query->set( 'post__not_in', $exclude_page_ids );
        }
        return $query;
    }
}

/* Thay doi so bai viet trong rss feed
*===============================================================*/
if (!function_exists('vts_custom_rss_feed_limit')) {
    add_action('pre_get_posts', 'vts_custom_rss_feed_limit');
    function vts_custom_rss_feed_limit($query) {
        if ($query->is_feed) {
            $query->set('posts_per_rss', 12); // Replace '10' with the number of posts you want to show in the RSS feed
        }
    }
}

/* Thoi gian xuat ban - [vts_time]
*===============================================================*/
if (!function_exists('vts_publish')) {
    add_shortcode('vts_time', 'vts_publish');
    function vts_publish(){
        global $post;	
        $published_time = get_the_time('d-m-Y', $post->ID);
        $modified_time = get_the_modified_time('d-m-Y', $post->ID);
        $author = get_the_author();
        $author_url = get_author_posts_url(get_the_author_meta('ID')); 

            return '<div class="vts_time post_on"><i class="fa fa-calendar" aria-hidden="true"></i> <span content="'.$published_time.'" itemprop="datePublished">'.$published_time.'</span> <i class="fa fa-clock-o" aria-hidden="true"></i> <span content="'.$modified_time.'"  itemprop="dateModified">'.$modified_time.'</span>  <i class="fa fa-user" aria-hidden="true"></i> <span><a href="'.$author_url.'">'.$author.'</a></span></div>';
    }
}

/* Custom User Profile Fields
*===============================================================*/
if (!function_exists('user_custom_additionalfields')) {
    function user_custom_additionalfields($contactmethods){
        // ADD CONTACT CUSTOM FIELDS
        $contactmethods['facebook'] = 'Facebook';
        $contactmethods['twitter'] = 'Twitter';
        $contactmethods['pinterest'] = 'Pinterest';
        $contactmethods['github'] = 'Github';
        $contactmethods['linkedin'] = 'Linkedin';
        $contactmethods['mobile'] = 'Mobile Phone';
        $contactmethods['zalo'] = 'Zalo';
        $contactmethods['jobTitle'] = 'Nghề nghiệp';
        $contactmethods['birthPlace'] = 'Nơi sinh';
        $contactmethods['diachi'] = 'Địa chỉ';
        $contactmethods['namsinh'] = 'Năm sinh';

        return $contactmethods;
    }
    add_filter('user_contactmethods', 'user_custom_additionalfields', 10, 1);
}

// custom profile
if (!function_exists('vutruso_add_user_profile_fields')) {
    add_action('show_password_fields', 'vutruso_add_user_profile_fields');
    function vutruso_add_user_profile_fields($bool) {
        global $current_user;
      
        // Get the current user's data
        $current_user_data = get_userdata($current_user->ID);
      
        echo '<tr><th><label for="s_box">Giới tính</label></th><td><select name="s_box">';
        echo '<option value="Nam" ' . selected('Nam', $current_user_data->s_box, false) . '>Nam</option>';
        echo '<option value="Nữ" ' . selected('Nữ', $current_user_data->s_box, false) . '>Nữ</option>';
        echo '</select></td></tr>';
      
        return $bool;
    }
}

// Update
if (!function_exists('update_user_profile_fields')) {
    function update_user_profile_fields($user_id, $old_user_data) {
      
        // Update select box
        if(isset($_POST['s_box'])) {
          update_user_meta($user_id, 's_box', $_POST['s_box']);
        }
    }
    add_action('profile_update', 'update_user_profile_fields', 10, 2);
}

/* Valid Phone (contact form 7)
*===============================================================*/
if (!function_exists('custom_filter_wpcf7_is_tel')) {
    add_filter( 'wpcf7_is_tel', 'custom_filter_wpcf7_is_tel', 10, 2 );
    function custom_filter_wpcf7_is_tel( $result, $tel ) { 
        $result = preg_match(
            '/^(0|\+84)(\s|\.)?((3[2-9])|(5[6689])|(7[0679])|(8[1-9])|(9[9]))(\d)(\s|\.)?(\d{3})(\s|\.)?(\d{3})$/',
            $tel
        );
        return $result; 
    }
}

/* Include
*===============================================================*/
require get_stylesheet_directory() . '/inc/functions/vts_clean.php';
require get_stylesheet_directory() . '/inc/functions/vts_register_sidebar.php';
require get_stylesheet_directory() . '/inc/functions/vts_woocommerce.php';
require get_stylesheet_directory() . '/inc/functions/vts_related_post_in_content.php';
require get_stylesheet_directory() . '/inc/functions/vts_related_posts_cat.php';
require get_stylesheet_directory() . '/inc/functions/vts_featured_image_column.php';
require get_stylesheet_directory() . '/inc/functions/vts_login.php';
require get_stylesheet_directory() . '/inc/functions/vts_performance.php';

// fixed toc
require get_stylesheet_directory() . '/inc/functions/vts_fixed_toc.php';
require get_stylesheet_directory() . '/inc/functions/vts_seo.php';

require get_stylesheet_directory() . '/inc/functions/vts_secure.php';
require get_stylesheet_directory() . '/inc/functions/vts_pre_btn.php';

//require get_stylesheet_directory() . '/inc/functions/vts_pagespeed.php';

//require get_stylesheet_directory() . '/inc/functions/vts_comments.php';
//require get_stylesheet_directory() . '/languages/translation.php';
require get_stylesheet_directory() . '/inc/widgets/custom_menu.php';

/* VTS Theme setup
*===============================================================*/
if ( ! function_exists( 'vts_setup' ) ) :
    function vts_setup() {
        //load_theme_textdomain( 'vutruso', get_template_directory() . '/languages' );
        
        // Restoring the classic Widgets Editor
        remove_theme_support( 'widgets-block-editor' );

        // Menu
        register_nav_menus(array(
            'main_menu'         => __('Main Menu' , 'vutruso'),
            'footer_menu'       => __('Footer Menu' , 'vutruso'),
            'mobile_menu'       => __('Mobile Menu' , 'vutruso'),
        ));
    
        // Add default posts and comments RSS feed links to head
        add_theme_support( 'automatic-feed-links' );
        
        // Adds <title> tag support
        add_theme_support( 'title-tag' ); 

        // Add theme support for selective refresh for widgets
        add_theme_support( 'customize-selective-refresh-widgets' );

        // Add support for core custom logo
        add_theme_support( 'custom-logo', array(
            'height'      => 28,
            'width'       => 140,
            'flex-width'  => false,
            'flex-height' => false,
        ) );

        // Post formats
        add_theme_support( 'post-formats', array( 'image' ) );

        // Add support for responsive embeds.
        add_theme_support( 'responsive-embeds' );

        // Content width
        if ( ! isset( $content_width ) ) $content_width = 1200;

        // Remove the REST API lines from the HTML Header
        remove_action( 'wp_head', 'rest_output_link_wp_head', 10 );
        remove_action( 'wp_head', 'wp_oembed_add_discovery_links', 10 );
        // Remove the REST API endpoint.
        // Remove_action( 'rest_api_init', 'wp_oembed_register_route' );
        // Turn off oEmbed auto discovery.
        add_filter( 'embed_oembed_discover', '__return_false' );
        // Don't filter oEmbed results.
        // Remove_filter( 'oembed_dataparse', 'wp_filter_oembed_result', 10 );
        // Remove oEmbed discovery links.
        remove_action( 'wp_head', 'wp_oembed_add_discovery_links' );
        // Remove oEmbed-specific JavaScript from the front-end and back-end.
        remove_action( 'wp_head', 'wp_oembed_add_host_js' );
        // Remove emoji js
        remove_action( 'wp_head', 'print_emoji_detection_script', 7 );
        remove_action( 'wp_print_styles', 'print_emoji_styles' );
        // Remove EditURI/RSD + wlwmanifest + wp version
        remove_action ('wp_head', 'rsd_link');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'wp_generator'); 
        // Display the links to the general feeds: Post and Comment Feed
        remove_action( 'wp_head', 'feed_links', 2 ); 
    }
endif;
add_action( 'after_setup_theme', 'vts_setup' );

/* Add new size images
*===============================================================*/
if (!function_exists('vts_register_thumbnail_sizes')) {
    add_action('init', 'vts_register_thumbnail_sizes', 1);
    function vts_register_thumbnail_sizes(){
            add_theme_support('post-thumbnails');

            //add_image_size( "product_thumb", 363, 181, true );
            //archive + product thumb
            //add_image_size( "product_thumb", 460, 266, true );
            add_image_size( "archive_thumb", 480, 300, true );
            
            //related post
            //add_image_size( "related", 363, 181, true );
    }
}

/* Remove WordPress Image Sizes
*===============================================================*/
if (!function_exists('vts_disable_other_image_sizes')) {
    add_action('init', 'vts_disable_other_image_sizes');
    function vts_disable_other_image_sizes() {
        remove_image_size('2048x2048');
        remove_image_size('1536x1536');   
    }
}

// Combine both functions into one and improve performance
if (!function_exists('vutruso_optimize_admin_scripts')) {
    add_action('admin_enqueue_scripts', 'vutruso_optimize_admin_scripts', 100);
    function vutruso_optimize_admin_scripts() {
        if (!is_admin()) {
            return;
        }

        // Array of scripts to dequeue/deregister
        $scripts_to_remove = [
            'seopress-pro-ga',
            'seopress-pro-ga-embed',
            'wc-reports',
            'seopress-pro-matomo'
        ];

        // Array of styles to dequeue/deregister if needed
        $styles_to_remove = [
            // 'style-handle'  // Uncomment if needed
        ];

        // Remove scripts
        foreach ($scripts_to_remove as $script) {
            wp_dequeue_script($script);
            wp_deregister_script($script);
        }

        // Remove styles
        foreach ($styles_to_remove as $style) {
            wp_dequeue_style($style);
            wp_deregister_style($style);
        }
    }
}

// Remove the original wp_print_scripts action as it's now redundant
remove_action('wp_print_scripts', 'vutruso_wp_dashboard_dequeue_script', 100);

/* Style wp-admin
*===============================================================*/
if (!function_exists('vts_admin_script')) {
    function vts_admin_script() {
        if(is_admin()){
            wp_enqueue_style( 'vts-admin-style', get_template_directory_uri() . '/css/vts-admin-style.css' );
            
            // CSS cho admin interface
            echo '<style type="text/css">
            #wp-sp-welcome-box, #wpr-deactivation-modal, .wpr-Modal, #toplevel_page_wpintense{display:none!important}
            #toplevel_page_eos_dp_menu, #toplevel_page_wpintense{display:none!important}
            #toplevel_page_image-regenerate-select-crop-settings,
            #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            #toplevel_page_woocommerce-marketing,
            #toplevel_page_admin-page-wc-admin-task-payments, a[href="site-editor.php?path=/patterns"]{display: none!important;}
        
            </style>';
            
            // JavaScript để inject CSS vào TinyMCE khi nó load xong
            echo '<script type="text/javascript">
            jQuery(document).ready(function($) {
                // Đợi TinyMCE khởi tạo xong
                $(document).on("tinymce-editor-init", function(event, editor) {
                    if (editor.id === "content") {
                        editor.dom.addStyle("body, p, div, span, h1, h2, h3, h4, h5, h6 { font-family: \'Montserrat\', serif !important; }");
                    }
                });
                
                // Fallback: kiểm tra TinyMCE mỗi 500ms
                var checkTinyMCE = setInterval(function() {
                    if (typeof tinymce !== "undefined" && tinymce.get("content")) {
                        var editor = tinymce.get("content");
                        if (editor && editor.dom) {
                            editor.dom.addStyle("body, p, div, span, h1, h2, h3, h4, h5, h6 { font-family: \'Montserrat\', serif !important; }");
                            clearInterval(checkTinyMCE);
                        }
                    }
                }, 500);
            });
            </script>';
        }   
    }
    add_action('admin_head', 'vts_admin_script');
}

/* Custom excerpt
*===============================================================*/
if (!function_exists('get_excerpt')) {
    function get_excerpt( $count ) {
        global $post;
        $permalink = get_permalink($post->ID);
        $excerpt = get_the_content();
        $excerpt = strip_tags($excerpt);
        $excerpt = substr($excerpt, 0, $count);
        $excerpt = substr($excerpt, 0, strripos($excerpt, " "));
        $excerpt = '<p>'.$excerpt.'...</p>';
        return $excerpt;
    }
}

/* Enable Excerpts for Pages
*===============================================================*/
if (!function_exists('vts_excerpts_to_pages')) {
    add_action( 'init', 'vts_excerpts_to_pages' );
    function vts_excerpts_to_pages() {
        add_post_type_support( 'page', 'excerpt' );
    }
}

/* SVG Upload Support
*===============================================================*/
if (!function_exists('mime_types')) {
    add_filter('upload_mimes', 'mime_types');
    function mime_types($mimes) {
        $mimes['svg'] = 'image/svg+xml';
        return $mimes;
    }
}

/* Show file .svg in media library
*===============================================================*/
if (!function_exists('show_svg_in_media_library')) {
    add_filter( 'wp_prepare_attachment_for_js', 'show_svg_in_media_library' );
    function show_svg_in_media_library( $response ) {
        if ( $response['mime'] === 'image/svg+xml' ) {
            $response['sizes'] = [
                'medium' => [
                    'url' => $response['url'],
                ],
            ];
        }
        return $response;
    }
}

/* Featured_image_rss
*===============================================================*/
if (!function_exists('vutruso_featured_image_rss')) {
    function vutruso_featured_image_rss($content) {
        global $post;

        $thumb_id = get_post_thumbnail_id();
        $thumb_url = wp_get_attachment_image_src($thumb_id,'large', true);

        if ( has_post_thumbnail( $post->ID ) ){
            $content = '' . '<a href="' . get_permalink( $post->ID ) . '" title="' . $post->post_title . '">' . '' . '<img alt="' . $post->post_title . '" style="max-width: 100%; width: 100%;" src="' . $thumb_url[0] . '" />' . '' . '</a><br/><br/>'. '' . $content;
        }
            return $content;
    }
    add_filter('the_excerpt_rss', 'vutruso_featured_image_rss');
    add_filter('the_content_feed', 'vutruso_featured_image_rss');
}

/* Pagination
*===============================================================*/
if (!function_exists('vts_page_navi')) {
    function vts_page_navi() {
        global $wp_query;
        $big = 999999999;
        $paginate_links = paginate_links( array(
            'base' => str_replace( $big, '%#%', html_entity_decode( get_pagenum_link( $big ) ) ),
            'current' => max( 1, get_query_var( 'paged' ) ),
            'total' => $wp_query->max_num_pages,
            'mid_size' => 2,
            'prev_next' => true,
            'prev_text' => __( '&laquo;', 'vutruso' ),
            'next_text' => __( '&raquo;', 'vutruso' ),
            'type' => 'list',
        ) );

        if ( is_string( $paginate_links ) ) {
            $paginate_links = str_replace( "<ul class='page-numbers'>", "<ul class='pagination'>", $paginate_links );
            $paginate_links = str_replace( '<li><span class="page-numbers dots">', "<li><a href='#'>", $paginate_links );
            $paginate_links = str_replace( "<li><span class='page-numbers current'>", "<li class='current'>", $paginate_links );
            $paginate_links = str_replace( '</span>', '</a>', $paginate_links );
            $paginate_links = str_replace( "<li><a href='#'>&hellip;</a></li>", "<li><span class='dots'>&hellip;</span></li>", $paginate_links );
            $paginate_links = preg_replace( '/\s*page-numbers/', '', $paginate_links );

            if ( $paginate_links ) {
                echo '<div class="page-navigation">';
                echo $paginate_links;
                echo '</div>';
            }
        }
    }
}

/* Register Custom pagination
*===============================================================*/
if (!function_exists('pagination')) {
    function pagination($pages = '', $range = 4){
        $showitems = ($range * 2)+1;
        global $paged;
        if(empty($paged)) $paged = 1;

        if($pages == ''){
            global $wp_query;
            $pages = $wp_query->max_num_pages;
            if(!$pages)
            {
                $pages = 1;
            }
        }

        if(1 != $pages){
            echo "<nav class='vts-nav' aria-label='Page navigation example'>  <ul class='pagination'> <span>Page ".$paged." of ".$pages."</span>";
            if($paged > 2 && $paged > $range+1 && $showitems < $pages) echo "<a href='".get_pagenum_link(1)."'>&laquo; First</a>";
            if($paged > 1 && $showitems < $pages) echo "<a href='".get_pagenum_link($paged - 1)."'>&lsaquo; Previous</a>";
            for ($i=1; $i <= $pages; $i++){
                if (1 != $pages &&( !($i >= $paged+$range+1 || $i <= $paged-$range-1) || $pages <= $showitems ))
                {
                    echo ($paged == $i)? "<li class=\"page-item active\"><a class='page-link'>".$i."</a></li>":"<li class='page-item'> <a href='".get_pagenum_link($i)."' class=\"page-link\">".$i."</a></li>";
                }
            }
            if ($paged < $pages && $showitems < $pages) echo " <li class='page-item'><a class='page-link' href=\"".get_pagenum_link($paged + 1)."\">i class='flaticon flaticon-back'></i></a></li>";
            if ($paged < $pages-1 &&  $paged+$range-1 < $pages && $showitems < $pages) echo " <li class='page-item'><a class='page-link' href='".get_pagenum_link($pages)."'><i class='flaticon flaticon-arrow'></i></a></li>";
            echo "</ul></nav>\n";
        }
    }
}

/* View counter code
*===============================================================*/
if (!function_exists('vts_setPostViews')) {
    function vts_setPostViews($postID) {
        $count_key = 'post_views_count';
        $cache_key = 'post_views_' . $postID;
        
        // Lấy từ cache trước
        $count = wp_cache_get($cache_key, 'post_views');
        
        if (false === $count) {
            $count = get_post_meta($postID, $count_key, true);
            $count = empty($count) ? 0 : (int)$count;
        }
        
        $count++;
        
        // Cache trong 5 phút
        wp_cache_set($cache_key, $count, 'post_views', 300);
        
        // Cập nhật DB mỗi 10 views hoặc sau 5 phút
        if ($count % 10 == 0) {
            update_post_meta($postID, $count_key, $count);
        }
    }
}

if (!function_exists('vts_getPostViews')) {
    function vts_getPostViews($postID) {
        $count_key = 'post_views_count';
        $count = get_post_meta($postID, $count_key, true);
        if($count=='') {
            delete_post_meta($postID, $count_key);
            add_post_meta($postID, $count_key, '0');
            return "0 View";
        }
        if ($count > 1000) {
            return round ( $count / 1000 , 1 ).'';
        } else {
            return $count.'';
        }
    }
}

/* Defer_css
*===============================================================*/
if (!function_exists('vutruso_defer_css')) {
    add_filter( 'style_loader_tag', 'vutruso_defer_css', 10, 2 );
    function vutruso_defer_css( $html, $handle ) {
        $handles = array( 'vts-fontawesome', 'vts-mobile-menu', 'venobox-css' );
        if ( in_array( $handle, $handles ) ) {
          $html = str_replace( 'media=\'all\'', 'media=\'print\' onload="this.onload=null;this.media=\'all\'"', $html );
        }
        return $html;
    }
}

/* vts_custom_excerpt
*===============================================================*/
if (!function_exists('vts_custom_excerpt')) {
    function vts_custom_excerpt() {
        $content = get_the_content('');
        $content = strip_shortcodes($content);
        $content = wp_strip_all_tags($content);

        // Set the desired length of the excerpt in terms of words
        $excerpt_length = 45;

        // Split content into sentences
        $sentences = preg_split('/(?<=[.!?])\s+/', $content, -1, PREG_SPLIT_NO_EMPTY);

        // Initialize an empty excerpt
        $excerpt = '';
        $total_words = 0;

        // Build the excerpt from complete sentences
        foreach ($sentences as $sentence) {
            // Correct variable reference
            $sentence_word_count = str_word_count($sentence);

            if (($total_words + $sentence_word_count) <= $excerpt_length) { // Fixed variable reference
                $excerpt .= $sentence . ' ';
                $total_words += $sentence_word_count; // Fixed variable reference
            } else {
                break; // Stop if the total words exceed the limit
            }
        }

        // Clean up the excerpt
        $excerpt = trim($excerpt);
        $excerpt = rtrim($excerpt, '.');

        return $excerpt;
    }
}

/* Custom hook to replace the default excerpt with a custom excerpt. */
if (!function_exists('vts_excerpt_hook')) {
    function vts_excerpt_hook($excerpt) {
        ob_start(); // Start output buffering

        // Get the custom excerpt and ensure no trailing period
        $custom_excerpt = rtrim(vts_custom_excerpt(), '.');

        // Print the custom excerpt within a paragraph tag
        echo '<p>' . $custom_excerpt . '.</p>';

        $output = ob_get_clean(); // Capture the output and clean the buffer
        return $output; // Return the modified excerpt
    }
    //add_filter('the_excerpt', 'vts_excerpt_hook');
}

/* Remove capital P dangit */
remove_filter( 'the_title', 'capital_P_dangit', 11 );
remove_filter( 'the_content', 'capital_P_dangit', 11 );
remove_filter( 'comment_text', 'capital_P_dangit', 31 );

/* Remove JQuery migrate
*=====================================================================*/
if (!function_exists('vutruso_remove_jquery_migrate')) {
    function vutruso_remove_jquery_migrate($scripts){
        if (!is_admin() && isset($scripts->registered['jquery'])) {
            $script = $scripts->registered['jquery'];
            
            if ($script->deps) { // Check whether the script has any dependencies
                $script->deps = array_diff($script->deps, array(
                    'jquery-migrate'
                ));
            }
        }
    }
    add_action('wp_default_scripts', 'vutruso_remove_jquery_migrate');
}

// Remove woocommerce-no-js https://github.com/woocommerce/woocommerce/issues/21674
add_filter( 'body_class', function($classes){
    remove_action( 'wp_footer', 'wc_no_js' );
    $classes = array_diff($classes, array('woocommerce-no-js'));
    return array_values($classes);
},10, 1);