<?php

// Google bot functionality
function isGoogleBotOrPageSpeed() {
    // Check if the User-Agent is set and normalize it to lower case for case-insensitive comparison
    $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? strtolower($_SERVER['HTTP_USER_AGENT']) : '';

    // Define a list of User-Agent substrings related to Google crawlers and PageSpeed Insights/Lighthouse
    $userAgents = [
        // Googlebot user agents
        'googlebot', // For the main Google crawler
        'googlebot-image', // For Google Image crawler
        'googlebot-news', // For Google News crawler
        'googlebot-video', // For Google Video crawler

        // PageSpeed Insights/Lighthouse user agents
        'lighthouse',
        'pagespeed',
        'chrome-lighthouse'
    ];

    // Check if the current User-Agent matches any from the list
    foreach ($userAgents as $agent) {
        if (strpos($userAgent, $agent) !== false) {
            return true; // The current user agent is a Google bot or related to PageSpeed Insights/Lighthouse
        }
    }

    return false; // The current user agent is neither a Google bot nor related to PageSpeed Insights/Lighthouse
}