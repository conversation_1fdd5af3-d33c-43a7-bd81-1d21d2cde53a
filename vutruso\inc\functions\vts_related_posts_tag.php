<?php

/* Related Posts Tags
 /*-----------------------------------------------------------------------------------*/
function vts_related_posts() {
	global $post, $wpdb;
	$backup = $post;  // backup the current object
	$tags = wp_get_post_tags($post->ID);
	$tagIDs = array();
	if ($tags) {
		$tagcount = count($tags);
		for ($i = 0; $i < $tagcount; $i++) {
			$tagIDs[$i] = $tags[$i]->term_id;
		}

		$args=array(
				'tag__in' => $tagIDs,
				'post__not_in' => array($post->ID),
				'showposts'=>8,
				'caller_get_posts'=>1
		);
		$my_query = new WP_Query($args);
		  if( $my_query->have_posts() ) { $related_post_found = true; ?>
			<h3 class="section-title">Related Posts</h3>
				<div class="related-loop row">		
		    <?php $post_count = 1; while ($my_query->have_posts()) : $my_query->the_post(); ?>
					 <div <?php echo 'class="hentry col-xs-6 col-sm-6 col-lg-3 col-module"';?> itemscope itemtype="http://schema.org/Article">
				 		<a href="<?php the_permalink(); ?>" rel="bookmark"><?php the_post_thumbnail('thumbnail', array('class' => 'entry-thumb','itemprop'=>'image')); ?></a>
						<h2 itemprop="name" class="entry-title"><a title="<?php the_title(); ?>" href="<?php the_permalink() ?>"><?php the_title(); ?></a></h2>
						<div class="entry-meta"><i class="fa fa-user"></i> <span class="vcard author"><span itemprop="author" class="fn"><?php the_author(); ?></span></span> <br/><i class="fa fa-calendar"></i> <span itemprop="dateCreated" class="entry-date updated" datetime="<?php the_time('g:i a'); ?>"><?php the_time('g:i a'); ?></span></div>
						<meta itemprop="url" content="<?php the_permalink(); ?>"/>
	                 </div>			
		    <?php $post_count++; endwhile; wp_reset_query(); ?>
				</div>		
		  <?php }
		}
		
		//show recent posts if no related found
		if(!$related_post_found){ ?>
			<h3 class="section-title">Related Posts</h3>
			<div class="related-loop row">
			<?php
			$post_count = 1;
			$posts = get_posts('numberposts=8&offset=0');
			foreach($posts as $post) { ?>
				<div <?php echo 'class="hentry col-xs-6 col-sm-6 col-lg-3 col-module"';?> itemscope itemtype="http://schema.org/Article">
					<a href="<?php the_permalink(); ?>" rel="bookmark" ><?php the_post_thumbnail('thumbnail', array('class' => 'entry-thumb','itemprop' => 'image')); ?></a>
					<h2 class="entry-title" itemprop="name"><a title="<?php the_title(); ?>" href="<?php the_permalink() ?>"><?php the_title(); ?></a></h2>
					<div class="entry-meta"><i class="fa fa-user"></i> <span class="vcard author"><span itemprop="author" class="fn"><?php the_author(); ?></span></span> <br/><i class="fa fa-calendar"></i> <span itemprop="dateCreated" class="entry-date updated" datetime="<?php the_time('g:i a'); ?>"><?php the_time('g:i a'); ?></span></div>
					<meta itemprop="url" content="<?php the_permalink(); ?>"/>
				</div>			
			<?php $post_count++; } wp_reset_query(); ?>
			</div>
			<?php 
		}
		wp_reset_query();
	}