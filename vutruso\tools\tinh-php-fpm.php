<?php
/*
 * Template Name: PHP-FPM
 */
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP-FPM Calculator - Tối ưu hóa cấu hình PHP-FPM cho VPS</title>
    <meta property="og:url" content="https://vutruso.com/php-fpm/">
    <link rel="canonical" href="https://vutruso.com/php-fpm/">
    <meta name="description" content="Công cụ tính toán và đưa ra thông số tối ưu hóa cấu hình PHP-FPM cho VPS của bạn. Tính toán pm.max_children, pm.start_servers, pm.min_spare_servers dựa trên RAM và CPU">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="PHP-FPM Calculator">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="PHP-FPM Calculator - Tối ưu hóa cấu hình PHP-FPM cho VPS">
    <meta property="og:description" content="Công cụ tính toán và đưa ra thông số tối ưu hóa cấu hình PHP-FPM cho VPS của bạn. Tính toán pm.max_children, pm.start_servers, pm.min_spare_servers dựa trên RAM và CPU">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/07/php-fpm-calculator.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/07/php-fpm-calculator.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="PHP-FPM Calculator - Vutruso">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="PHP-FPM Calculator - Tối ưu hóa cấu hình PHP-FPM cho VPS">
    <meta name="twitter:description" content="Công cụ tính toán và đưa ra thông số tối ưu hóa cấu hình PHP-FPM cho VPS của bạn. Tính toán pm.max_children, pm.start_servers, pm.min_spare_servers dựa trên RAM và CPU">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/07/php-fpm-calculator.png">
    
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "PHP-FPM Calculator",
        "url": "https://vutruso.com/php-fpm/",
        "description": "Công cụ tính toán và đưa ra thông số tối ưu hóa cấu hình PHP-FPM cho VPS của bạn. Tính toán pm.max_children, pm.start_servers, pm.min_spare_servers dựa trên RAM và CPU",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND"
        },
        "featureList": [
            "Tính toán thông số PHP-FPM tối ưu",
            "Dựa trên RAM và CPU của VPS",
            "Tạo file cấu hình hoàn chỉnh",
            "Hỗ trợ nhiều process manager",
            "Giải thích chi tiết các thông số",
            "Hoàn toàn miễn phí"
        ],
        "screenshot": "https://vutruso.com/wp-content/uploads/2025/07/php-fpm-calculator.png",
        "creator": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "alternateName": "Vũ Trụ Số",
            "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
            "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
            "sameAs": [
                "https://www.facebook.com/vutruso",
                "https://twitter.com/@vutruso",
                "https://www.pinterest.com/vutruso/",
                "https://www.instagram.com/vutruso",
                "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
                "https://www.linkedin.com/in/vutruso",
                "https://g.page/vutruso",
                "https://vutruso.business.site/",
                "https://sites.google.com/view/vutruweb",
                "https://vutruso.tumblr.com/",
                "https://ok.ru/profile/589668477610"
            ],
            "vatID": "0317358676",
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": "+***********",
                    "email": "<EMAIL>",
                    "contactOption": "TollFree",
                    "contactType": "customer support"
                }
            ]
        }
    }
    </script>


    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .php-fpm-calculator {
            padding: 15px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 0px;
            margin-bottom: 30px;
        }

        .input-group {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-group:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .input-group label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .input-group label a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px dotted #667eea;
        }

        .input-group label a:hover {
            color: #764ba2;
            border-bottom-color: #764ba2;
        }

        .input-wrapper {
            position: relative;
        }

        .input-wrapper input[type="number"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .input-wrapper input[type="number"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-wrapper input[type="range"] {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e1e8ed;
            outline: none;
            -webkit-appearance: none;
        }

        .input-wrapper input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .input-wrapper input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.2);
        }

        .input-wrapper input[type="range"]::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .results-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 0px;          
            padding-top: 0;
        }

        .results-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
            text-align: center;

        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .result-item {
            background: white;
            padding: 0px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .result-item label {
            display: block;
            font-weight: 600;
            color: #555;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .result-item input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            color: #333;
            background: #f8f9ff;
            font-size: 1.1em;
        }

        .config-section {
            background: white;
            padding: 0px;
            border-radius: 12px;
            margin-top: 0;
            padding-top: 0;
        }

        .config-section label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            font-size: 1.1em;
        }

        .config-section textarea {
            width: 100%;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            background: #f8f9ff;
            color: #333;
            line-height: 1.6;
            resize: vertical;
            min-height: 120px;
        }

        .copy-button {
            margin-top: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .copy-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .copy-button:active {
            transform: translateY(0);
        }

        .info-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            margin-left: 5px;
            cursor: help;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .results-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .php-fpm-calculator {
                padding: 10px;
            }

            .header {
                padding: 20px;    
                font-size: 12px;
            }

            .controls-section {
                padding: 0;
            }

            .memory-allocation {
                padding: 15px;
            }

            .input-group {
                padding: 5px;
            }

            .results-section {
                padding: 5px;
            }

            .result-item {
                padding: 0px;
            }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 PHP-FPM Calculator</h1>
            <p>Tối ưu hóa cấu hình PHP-FPM cho VPS của bạn</p>
        </div>

        <div class="php-fpm-calculator">
            <form id="calculator-form">
                <div class="form-grid">
                    <div class="input-group">
                        <label>🖥️ Tổng <a data-ail="43427" target="_blank" href="#">RAM</a> (GB):</label>
                        <div class="input-wrapper">
                            <input id="ram-total-val" type="number" min="1" max="512" step="1" value="4" oninput="validateInput(this)">
                            <input id="ram-total" type="range" min="1" max="512" step="1" value="4">
                        </div>
                    </div>

                    <div class="input-group">
                        <label>🔒 <a data-ail="43427" target="_blank" href="#">RAM</a> Dự trữ (GB):</label>
                        <div class="input-wrapper">
                            <input id="ram-reserved-val" type="number" min="0.25" max="512" step="0.25" value="1" oninput="validateInput(this)">
                            <input id="ram-reserved" type="range" min="0.25" max="512" step="0.25" value="1">
                        </div>
                    </div>

                    <div class="input-group">
                        <label>🛡️ <a data-ail="43427" target="_blank" href="#">RAM</a> Buffer (%):</label>
                        <div class="input-wrapper">
                            <input id="ram-buffer-val" type="number" min="0" max="100" step="1" value="10" oninput="validateInput(this)">
                            <input id="ram-buffer" type="range" min="0" max="100" step="1" value="10">
                        </div>
                    </div>

                    <div class="input-group">
                        <label>⚙️ Kích thước Process (MB):</label>
                        <div class="input-wrapper">
                            <input id="process-size-val" type="number" min="1" max="1024" step="1" value="32" oninput="validateInput(this)">
                            <input id="process-size" type="range" min="1" max="1024" step="1" value="32">
                        </div>
                    </div>
                </div>

                <div class="results-section">
                    <h3>📊 Kết quả tính toán</h3>
                    <div class="results-grid">
                        <div class="result-item">
                            <label>RAM Khả dụng (GB):</label>
                            <input id="ram-available" type="text" readonly>
                        </div>
                        <div class="result-item">
                            <label>RAM Khả dụng (MB):</label>
                            <input id="ram-available-mb" type="text" readonly>
                        </div>
                        <div class="result-item">
                            <label>pm.max_children:</label>
                            <input id="max-children" type="text" readonly>
                        </div>
                        <div class="result-item">
                            <label>pm.start_servers:</label>
                            <input id="start-servers" type="text" readonly>
                        </div>
                        <div class="result-item">
                            <label>pm.min_spare_servers:</label>
                            <input id="min-spare" type="text" readonly>
                        </div>
                        <div class="result-item">
                            <label>pm.max_spare_servers:</label>
                            <input id="max-spare" type="text" readonly>
                        </div>
                    </div>

                    <div class="config-section">
                        <label>📋 Cấu hình được tạo:</label>
                        <textarea id="copyPasteArea" rows="6" readonly></textarea>
                        <button type="button" id="buttonCopy" class="copy-button">📋 Sao chép vào Clipboard</button>
                    </div>
                </div>
            </form>
            <footer class="seo-footer" style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: center; border-top: 3px solid #4285f4;margin:10px">
              <div class="footer-content">
                <p style="margin-bottom: 10px; color: #666;"><strong>🔧 PHP-FPM Calculator</strong> giúp tối ưu hóa cấu hình PHP-FPM cho VPS của bạn</p>
                <p style="margin-bottom: 10px; color: #666;">Phát triển bởi <a style="color: #4285f4;" href="https://vutruso.com/">Vũ Trụ Số</a> để hỗ trợ cộng đồng Việt Nam.</p>
                <div class="footer-links">
                  <span>📚 Xem thêm các công cụ khác: </span>
                  <a href="https://vutruso.com/tinh-thue/">Công cụ tính thuế VAT Online</a> |
                  <a href="https://vutruso.com/dinh-dang-text/">Công cụ chuyển đổi chữ</a>
                </div>
              </div>
            </footer>
        </div>
    </div>

    <div id="notification" class="notification">
        ✅ Đã sao chép cấu hình thành công!
    </div>

    <script>
function _0x58ca(_0x7a56d6,_0x1514ab){const _0x55f469=_0x9585();return _0x58ca=function(_0x52465f,_0x1425d9){_0x52465f=_0x52465f-(0x1a6c+0x1*0x219e+-0x2*0x1dbd);let _0x4ca337=_0x55f469[_0x52465f];return _0x4ca337;},_0x58ca(_0x7a56d6,_0x1514ab);}const _0x10e593=_0x58ca;function _0x9585(){const _0x4c824f=['replace','ao\x20chép\x20th','copy','ARHWT','getElement','process-si','SRJsK','max-childr','Vui\x20lòng\x20s','ScWSs','IkhLV','qdXEd','catch','servers\x20=\x20','LdXHJ','remove','forEach','ram-buffer','RgIVE','ble','TYtpJ','PaUpb','\x0apm.max_sp','execComman','ldren\x20=\x20','notificati','DOMContent','start-serv','dgtno','wLYzt','ram-reserv','\x0apm.min_sp','ed-val','GLuHV','Loaded','-val','rea','TerEM','xJsTD','click','UGdzn','add','SjKYw','EGDzQ','hYryn','min-spare','writeText','Không\x20thể\x20','vaekR','556362GoELhO','qQMgW','hvfjA','dYkBD','sgJHO','2216mMDVka','\x20MB','select','pm.max_chi','wTgwv','zZtXv','IOdGm','ddfBI','\x0apm.start_','ciOwI','ble-mb','HLeXl','GoOeL','input','ủ\x20công.','addEventLi','classList','42816636GTbNDf','ById','buttonCopy','370XsUALX','19082GhiyNi','value','RJuDn','ram-total','gTlkf','round','qxkPH','ers','VGhOn','\x20GB','4203EHBKgI','floor','52GcNfEV','Xyvec','kzXUc','ajSXd','clipboard','onRange','rJvka','38810xEPtRP','uVoSX','ThtBF','stetu','2992wdfcyG','show','ram-availa','are_server','sao\x20chép.\x20','max-spare','setSelecti','stener','rxlpJ','60bLYqwS','s\x20=\x20','HeHkl','then','vZCdn','iHmij','beBYF','59226hDQgDh','1599719XOnyEw','copyPasteA'];_0x9585=function(){return _0x4c824f;};return _0x9585();}(function(_0x1171d7,_0x5cf826){const _0xc7f6c1=_0x58ca,_0x2b9f2e=_0x1171d7();while(!![]){try{const _0x216785=parseInt(_0xc7f6c1(0xe6))/(0xf13+0x7ad*0x4+-0x2dc6)*(-parseInt(_0xc7f6c1(0xfe))/(-0x1a95+0x6a*0x3+-0x1959*-0x1))+parseInt(_0xc7f6c1(0x99))/(-0x1d*0xa1+-0x2223+-0x3463*-0x1)*(parseInt(_0xc7f6c1(0xf3))/(-0x887+0x1*0x22d2+-0x1a47))+-parseInt(_0xc7f6c1(0x92))/(0x12a9+0xc89*-0x1+-0x61b)*(parseInt(_0xc7f6c1(0xcd))/(-0x1458+0x36*0x1+0x1428))+parseInt(_0xc7f6c1(0xe7))/(0xd21+-0xf+0x7*-0x1dd)*(parseInt(_0xc7f6c1(0xd2))/(0x9b*-0x3e+-0x8*0x2dc+0x3c72))+parseInt(_0xc7f6c1(0xf1))/(0xaf*0x3+0x150c+-0x1710)*(-parseInt(_0xc7f6c1(0xfa))/(0x1555+-0x1a8e+0x543))+-parseInt(_0xc7f6c1(0x9a))/(-0x392*0x3+0x51b+0x5a6)+parseInt(_0xc7f6c1(0xe3))/(-0x8e4*-0x3+-0x8d5*-0x4+-0x1a*0x262);if(_0x216785===_0x5cf826)break;else _0x2b9f2e['push'](_0x2b9f2e['shift']());}catch(_0x2f7353){_0x2b9f2e['push'](_0x2b9f2e['shift']());}}}(_0x9585,0x87940+0xc4ce+-0x17*-0x3b71),document[_0x10e593(0xe1)+_0x10e593(0x90)](_0x10e593(0xb6)+_0x10e593(0xbe),function(){const _0x31787c=_0x10e593,_0x23205e={'HeHkl':function(_0x1c1528,_0x2a5d5f){return _0x1c1528+_0x2a5d5f;},'IkhLV':_0x31787c(0xbf),'RJuDn':function(_0x5b77f4,_0x29bd09){return _0x5b77f4(_0x29bd09);},'PaUpb':_0x31787c(0xea),'VGhOn':function(_0x7ecc1d,_0x5f4d36){return _0x7ecc1d(_0x5f4d36);},'vZCdn':_0x31787c(0xba)+'ed','vaekR':_0x31787c(0xad),'ThtBF':function(_0x266d50,_0x491635){return _0x266d50(_0x491635);},'EGDzQ':_0x31787c(0xa1)+'ze','rxlpJ':function(_0x46c18f,_0x13ff05){return _0x46c18f>_0x13ff05;},'dYkBD':_0x31787c(0xba)+_0x31787c(0xbc),'beBYF':function(_0x3fab21,_0x234d1f){return _0x3fab21-_0x234d1f;},'RgIVE':function(_0x2aeb4a,_0xfc170a){return _0x2aeb4a/_0xfc170a;},'Xyvec':function(_0x2974f3,_0x368d8c){return _0x2974f3/_0x368d8c;},'IOdGm':function(_0x1e11f8,_0x214ca7){return _0x1e11f8*_0x214ca7;},'GoOeL':function(_0x405df9,_0x298c03){return _0x405df9/_0x298c03;},'ARHWT':function(_0x59ff91,_0x3742de){return _0x59ff91*_0x3742de;},'zZtXv':function(_0x1968d4,_0xf63875){return _0x1968d4*_0xf63875;},'wTgwv':_0x31787c(0x100)+_0x31787c(0xaf),'SjKYw':function(_0x221edc,_0x2f62a4){return _0x221edc+_0x2f62a4;},'xJsTD':_0x31787c(0xf0),'sgJHO':_0x31787c(0x100)+_0x31787c(0xdc),'UGdzn':function(_0x216168,_0x28d5c5){return _0x216168+_0x28d5c5;},'GLuHV':_0x31787c(0xd3),'ScWSs':_0x31787c(0xa3)+'en','TerEM':_0x31787c(0xb7)+_0x31787c(0xee),'hvfjA':_0x31787c(0xc9),'ddfBI':_0x31787c(0x103),'hYryn':function(_0x1f4976){return _0x1f4976();},'iHmij':_0x31787c(0x9b)+_0x31787c(0xc0),'rJvka':function(_0x3738d0){return _0x3738d0();},'qQMgW':function(_0x254a15,_0x3666cb){return _0x254a15(_0x3666cb);},'dgtno':_0x31787c(0xcb)+_0x31787c(0x102)+_0x31787c(0xa4)+_0x31787c(0x9d)+_0x31787c(0xe0),'uVoSX':_0x31787c(0x9e),'LdXHJ':function(_0x3deccc){return _0x3deccc();},'ajSXd':_0x31787c(0xff),'TYtpJ':_0x31787c(0xb5)+'on','HLeXl':function(_0x10bd09,_0x21cd74,_0xfa6ea){return _0x10bd09(_0x21cd74,_0xfa6ea);},'qxkPH':function(_0x112fc6,_0x55e59f){return _0x112fc6(_0x55e59f);},'gTlkf':_0x31787c(0xdf),'qdXEd':_0x31787c(0xe5),'wLYzt':_0x31787c(0xc3)};function _0x4365e2(_0x5947e8){const _0x1bdda6=_0x31787c;_0x5947e8[_0x1bdda6(0xe8)]=_0x5947e8[_0x1bdda6(0xe8)][_0x1bdda6(0x9c)](/[^0-9.]/g,'');}function _0x1124c6(_0x2e457c=null){const _0x47b0ff=_0x31787c;if(_0x2e457c){const _0x32d49b=document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0x94)](_0x2e457c['id'],_0x23205e[_0x47b0ff(0xa6)]))||document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x2e457c['id'][_0x47b0ff(0x9c)](_0x23205e[_0x47b0ff(0xa6)],''));if(_0x32d49b)_0x32d49b[_0x47b0ff(0xe8)]=_0x2e457c[_0x47b0ff(0xe8)];}let _0x334fbf=_0x23205e[_0x47b0ff(0xe9)](parseFloat,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xb1)])[_0x47b0ff(0xe8)])||0x1258+-0xce*-0x16+0xc*-0x301,_0x4bfcc5=_0x23205e[_0x47b0ff(0xef)](parseFloat,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0x96)])[_0x47b0ff(0xe8)])||0x159d+-0x1d4a+-0x5*-0x189,_0x4052f9=_0x23205e[_0x47b0ff(0xef)](parseFloat,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xcc)])[_0x47b0ff(0xe8)])||-0x190b+-0x1130+0x2a3b,_0x272692=_0x23205e[_0x47b0ff(0xfc)](parseFloat,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xc7)])[_0x47b0ff(0xe8)])||0x1965+-0x221e+0x8b9;_0x23205e[_0x47b0ff(0x91)](_0x4bfcc5,_0x334fbf)&&(_0x4bfcc5=_0x334fbf,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0x96)])[_0x47b0ff(0xe8)]=_0x334fbf,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xd0)])[_0x47b0ff(0xe8)]=_0x334fbf);const _0x3e8f69=_0x23205e[_0x47b0ff(0x98)](-0xf1a+-0x246e+0x3389,_0x23205e[_0x47b0ff(0xae)](_0x4052f9,-0x245*0x11+-0x21bb+-0x16*-0x34e)),_0x274853=_0x23205e[_0x47b0ff(0xf4)](Math[_0x47b0ff(0xec)](_0x23205e[_0x47b0ff(0xd8)](_0x23205e[_0x47b0ff(0xd8)](_0x23205e[_0x47b0ff(0x98)](_0x334fbf,_0x4bfcc5),_0x3e8f69),-0x1b55*-0x1+0x1228*0x1+-0x2d73)),-0xa7*0x24+0x161*0x1b+0x79*-0x1d),_0x1fb0df=Math[_0x47b0ff(0xec)](_0x23205e[_0x47b0ff(0xd8)](_0x274853,0x1dd2+-0x469*-0x2+-0x22a4)),_0x26290e=Math[_0x47b0ff(0xf2)](_0x23205e[_0x47b0ff(0xde)](_0x1fb0df,_0x272692))||-0xf1*-0x8+0x1f13+-0x269b,_0x11f2c6=Math[_0x47b0ff(0xf2)](_0x23205e[_0x47b0ff(0xd8)](_0x26290e,-0x1ab3+-0x1*0x196d+0x3420+0.25))||-0x146a+-0x5*-0x491+-0x26b,_0x5e80d0=Math[_0x47b0ff(0xf2)](_0x23205e[_0x47b0ff(0x9f)](_0x26290e,-0x1702+-0x34*-0xb4+0x2b6*-0x5+0.25))||-0x15d*0x1b+0x1c43+0x88c,_0x5ec3aa=Math[_0x47b0ff(0xf2)](_0x23205e[_0x47b0ff(0xd7)](_0x26290e,0x473*-0x5+0x11*-0xde+0x11*0x22d+0.75))||0x18a8+-0x165c+-0x24c;document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xd6)])[_0x47b0ff(0xe8)]=_0x23205e[_0x47b0ff(0xc6)](_0x274853,_0x23205e[_0x47b0ff(0xc2)]),document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xd1)])[_0x47b0ff(0xe8)]=_0x23205e[_0x47b0ff(0xc4)](_0x1fb0df,_0x23205e[_0x47b0ff(0xbd)]),document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xa5)])[_0x47b0ff(0xe8)]=_0x26290e,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xc1)])[_0x47b0ff(0xe8)]=_0x11f2c6,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xcf)])[_0x47b0ff(0xe8)]=_0x5e80d0,document[_0x47b0ff(0xa0)+_0x47b0ff(0xe4)](_0x23205e[_0x47b0ff(0xd9)])[_0x47b0ff(0xe8)]=_0x5ec3aa,_0x23205e[_0x47b0ff(0xc8)](_0x9d2f90);}function _0x9d2f90(){const _0x34f1b6=_0x31787c,_0x2d3029=_0x34f1b6(0xd5)+_0x34f1b6(0xb4)+document[_0x34f1b6(0xa0)+_0x34f1b6(0xe4)](_0x23205e[_0x34f1b6(0xa5)])[_0x34f1b6(0xe8)]+(_0x34f1b6(0xda)+_0x34f1b6(0xa9))+document[_0x34f1b6(0xa0)+_0x34f1b6(0xe4)](_0x23205e[_0x34f1b6(0xc1)])[_0x34f1b6(0xe8)]+(_0x34f1b6(0xbb)+_0x34f1b6(0x101)+_0x34f1b6(0x93))+document[_0x34f1b6(0xa0)+_0x34f1b6(0xe4)](_0x23205e[_0x34f1b6(0xcf)])[_0x34f1b6(0xe8)]+(_0x34f1b6(0xb2)+_0x34f1b6(0x101)+_0x34f1b6(0x93))+document[_0x34f1b6(0xa0)+_0x34f1b6(0xe4)](_0x23205e[_0x34f1b6(0xd9)])[_0x34f1b6(0xe8)];document[_0x34f1b6(0xa0)+_0x34f1b6(0xe4)](_0x23205e[_0x34f1b6(0x97)])[_0x34f1b6(0xe8)]=_0x2d3029;}function _0x1cd4d8(){const _0x8b4a70=_0x31787c,_0x1a4196={'ciOwI':function(_0x5785b7){const _0x18c520=_0x58ca;return _0x23205e[_0x18c520(0xf9)](_0x5785b7);},'kzXUc':function(_0x1fc8f9,_0x2f88eb){const _0x28d7c6=_0x58ca;return _0x23205e[_0x28d7c6(0xce)](_0x1fc8f9,_0x2f88eb);},'SRJsK':_0x23205e[_0x8b4a70(0xb8)]},_0x2cd5a5=document[_0x8b4a70(0xa0)+_0x8b4a70(0xe4)](_0x23205e[_0x8b4a70(0x97)]);_0x2cd5a5[_0x8b4a70(0xd4)](),_0x2cd5a5[_0x8b4a70(0x104)+_0x8b4a70(0xf8)](0x1*-0x80b+-0x27*-0xba+-0x144b,-0xd83d*-0x1+0xf47*-0xf+0x1938b);try{document[_0x8b4a70(0xb3)+'d'](_0x23205e[_0x8b4a70(0xfb)]),_0x23205e[_0x8b4a70(0xaa)](_0x1e34f9);}catch(_0xd51e4d){navigator[_0x8b4a70(0xf7)][_0x8b4a70(0xca)](_0x2cd5a5[_0x8b4a70(0xe8)])[_0x8b4a70(0x95)](()=>{const _0x3c2a74=_0x8b4a70;_0x1a4196[_0x3c2a74(0xdb)](_0x1e34f9);})[_0x8b4a70(0xa8)](()=>{const _0x128b28=_0x8b4a70;_0x1a4196[_0x128b28(0xf5)](alert,_0x1a4196[_0x128b28(0xa2)]);});}}function _0x1e34f9(){const _0x2cb6af=_0x31787c,_0x1f2260=document[_0x2cb6af(0xa0)+_0x2cb6af(0xe4)](_0x23205e[_0x2cb6af(0xb0)]);_0x1f2260[_0x2cb6af(0xe2)][_0x2cb6af(0xc5)](_0x23205e[_0x2cb6af(0xf6)]),_0x23205e[_0x2cb6af(0xdd)](setTimeout,()=>{const _0x527e5c=_0x2cb6af;_0x1f2260[_0x527e5c(0xe2)][_0x527e5c(0xab)](_0x23205e[_0x527e5c(0xf6)]);},-0x1*0x892+-0x1a11+0x2e5b);}[_0x23205e[_0x31787c(0xb1)],_0x23205e[_0x31787c(0x96)],_0x23205e[_0x31787c(0xcc)],_0x23205e[_0x31787c(0xc7)]][_0x31787c(0xac)](_0x31e6e8=>{const _0x12a5c8=_0x31787c,_0x40260e={'stetu':function(_0x34cda7,_0x24c9ef){const _0x47a2c0=_0x58ca;return _0x23205e[_0x47a2c0(0xed)](_0x34cda7,_0x24c9ef);}};document[_0x12a5c8(0xa0)+_0x12a5c8(0xe4)](_0x31e6e8)[_0x12a5c8(0xe1)+_0x12a5c8(0x90)](_0x23205e[_0x12a5c8(0xeb)],function(){const _0x11355f=_0x12a5c8;_0x40260e[_0x11355f(0xfd)](_0x1124c6,this);}),document[_0x12a5c8(0xa0)+_0x12a5c8(0xe4)](_0x23205e[_0x12a5c8(0xc4)](_0x31e6e8,_0x23205e[_0x12a5c8(0xa6)]))[_0x12a5c8(0xe1)+_0x12a5c8(0x90)](_0x23205e[_0x12a5c8(0xeb)],function(){const _0x207540=_0x12a5c8;_0x23205e[_0x207540(0xce)](_0x1124c6,this);});}),document[_0x31787c(0xa0)+_0x31787c(0xe4)](_0x23205e[_0x31787c(0xa7)])[_0x31787c(0xe1)+_0x31787c(0x90)](_0x23205e[_0x31787c(0xb9)],_0x1cd4d8),_0x23205e[_0x31787c(0xc8)](_0x1124c6);}));
    </script>
</body>
</html>