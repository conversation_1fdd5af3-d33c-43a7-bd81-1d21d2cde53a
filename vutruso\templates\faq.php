<?php
/*
 * Template Name: FAQ
 */
?>
<?php get_header(); ?>

<div class="container">
    <div class="page-normal">
        <h1 class="title" style="text-transform: uppercase;"><?php the_title(); ?></h1>
        <section class="section-spacing">

            <?php if(have_posts()) : while(have_posts()) : the_post() ?>
            <?php the_content(); ?>
            <?php endwhile; ?>
            <?php endif; ?>

            <!-- <div class="title-faq"><h3>Một số câu hỏi thường gặp</h3></div> -->
            <ul class="faq">
                <li class="q"><i class="fa fa-chevron-right"></i>Các dịch vụ mà bạn cung cấp?</li>
                <li class="a" style="display: block;">Chúng tôi cung cấp đa dạng các gói dịch vụ như:
                    <ul class="sub-ul">
                        <li><a href="https://vutruso.com/dich-vu-thiet-ke-web/">Thiết kế website</a></li>
                        <li><a href="https://vutruso.com/dich-vu-thiet-ke-web/">Bảo mật website</a></li>
                        <li><a href="https://vutruso.com/dich-vu-toi-uu-website-wordpress/">Tối ưu website</a></li>
                        <li><a href="https://vutruso.com/dich-vu-quan-tri-website/">Quản trị website</a></li>
                        <li>Dịch vụ tạo social signal</li>
                        <li>Dịch vụ tạo backlink entity</li>
                        <li><a href="https://vutruso.com/dich-vu-sua-loi-va-nang-cap-wordpress/">Dịch vụ sửa lỗi và nâng cấp website</a></li>
                        <li><a href="https://vutruso.com/dich-vu-chuyen-host-wordpress/">Dịch vụ chuyển host</a></li>
                        <li><a href="https://vutruso.com/dich-vu-sua-loi-google-search-console/">Dịch vụ sửa lỗi trong Google search console</a></li>
                        <li><a href="https://vutruso.com/dich-vu-go-bo-ma-doc-cho-wordpress/">Dịch vụ gỡ mã độc cho website</a></li>
                        <li><a href="https://vutruso.com/dich-vu-quang-cao/">Dịch vụ quảng cáo Google, Facebook</a></li>
                        <li><a href="https://vutruso.com/dich-vu-seo/">Dịch vụ SEO - đưa từ khóa của bạn lên top tìm kiếm Google</a></li>
                    </ul>
                </li>
                <li class="q"><i class="fa fa-chevron-right"></i>Thời gian làm việc như thế nào?</li>
                <li class="a">Chúng tôi làm việc từ 8h-17h30 từ thứ 2 - thứ 7</li>
                <li class="q"><i class="fa fa-chevron-right"></i>Thời gian hỗ trợ bảo hành dịch vụ như thế nào?</li>
                <li class="a">Tùy theo dịch vụ sẽ có thời gian bảo hành khác nhau, đa số các dịch vụ sẽ có thời gian bảo hành là 12 tháng.</li>

                <li class="q"><i class="fa fa-chevron-right"></i>Các kênh liên hệ hỗ trợ ?</li>
                <li class="a">
                    Bạn có thể live chat với chúng tôi qua<br />
                    <ul class="sub-ul">
                        <li>Zalo : 0868017791</li>
                        <li>Hotline 086801779</li>
                        <li>Email: <EMAIL></li>
                        <li>Facebook: fb.com/vutruso</li>
                    </ul>
                </li>
                <li class="q"><i class="fa fa-chevron-right"></i>Giá trên web đã bao gồm VAT?</li>
                <li class="a">
                    Giá trên đã bao gồm VAT
                </li>
            </ul>
        </section>
    </div>
</div>

<style type="text/css" media="screen">
ul .sub-ul li {
    padding: 4px !important;
    font-size: 14px;
    list-style-type: circle;
}

ul .sub-ul li a {
    color: #1b62ae;
}

ul .sub-ul {
    margin-left: 2%;
    margin-top: 4px;
}

.title-faq {
    height: 2.5em;
    width: 100%;
    background: #1b62ae;
    color: #fff;
    text-align: center;
    padding-top: .5em;
}

.title-faq h3 {
    font-size: 18px;
    color: #fff;
    line-height: 25px;
}

.faq i {
    margin-right: .4em;
    transition: transform 0.3s ease;
}

.faq li {
    padding: 1.25em;
    line-height: 23px;
}

.faq li.q {
    font-weight: bold;
    border-bottom: 1px #ccc solid;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.faq li.q:hover {
    background-color: #f5f5f5;
}

.faq li.q.active i {
    transform: rotate(90deg);
}

.faq li.q:nth-child(1),
.faq li.q:nth-child(5),
.faq li.q:nth-child(9) {
    background: #eee;
}

.faq li.q:nth-child(1):hover,
.faq li.q:nth-child(5):hover,
.faq li.q:nth-child(9):hover {
    background: #e0e0e0;
}

.faq li.a {
    color: #1b62ae;
    background: #fff;
    display: none;
    padding: 10px 10px 10px 21px;
    font-size: 15.5px;
    overflow: hidden;
    transition: all 0.5s ease;
}

/* Animation classes for smooth slide effect */
.faq li.a.sliding-down {
    display: block;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    animation: slideDown 0.5s ease forwards;
}

.faq li.a.sliding-up {
    animation: slideUp 0.5s ease forwards;
}

@keyframes slideDown {
    from {
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
        opacity: 0;
    }
    to {
        max-height: 500px;
        padding-top: 10px;
        padding-bottom: 10px;
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        max-height: 500px;
        padding-top: 10px;
        padding-bottom: 10px;
        opacity: 1;
    }
    to {
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
        opacity: 0;
    }
}
</style>

<script type="text/javascript">
// Vanilla JavaScript Accordion
document.addEventListener('DOMContentLoaded', function() {
    // Get all question elements
    const questions = document.querySelectorAll('li.q');
    
    questions.forEach(function(question) {
        question.addEventListener('click', function() {
            // Get the answer element (next sibling)
            const answer = this.nextElementSibling;
            const isCurrentlyOpen = answer.style.display === 'block' || answer.classList.contains('sliding-down');
            
            // Close all other answers first
            const allAnswers = document.querySelectorAll('li.a');
            const allQuestions = document.querySelectorAll('li.q');
            
            allAnswers.forEach(function(ans, index) {
                if (ans !== answer && (ans.style.display === 'block' || ans.classList.contains('sliding-down'))) {
                    // Close this answer
                    ans.classList.remove('sliding-down');
                    ans.classList.add('sliding-up');
                    allQuestions[Math.floor(index/2)].classList.remove('active');
                    
                    setTimeout(function() {
                        ans.style.display = 'none';
                        ans.classList.remove('sliding-up');
                    }, 500);
                }
            });
            
            // Toggle current answer
            if (isCurrentlyOpen) {
                // Close current answer
                answer.classList.remove('sliding-down');
                answer.classList.add('sliding-up');
                this.classList.remove('active');
                
                setTimeout(function() {
                    answer.style.display = 'none';
                    answer.classList.remove('sliding-up');
                }, 500);
            } else {
                // Open current answer
                answer.style.display = 'block';
                answer.classList.remove('sliding-up');
                answer.classList.add('sliding-down');
                this.classList.add('active');
            }
        });
    });
    
    // Set first item as open by default (if needed)
    const firstAnswer = document.querySelector('li.a');
    const firstQuestion = document.querySelector('li.q');
    if (firstAnswer && firstAnswer.style.display === 'block') {
        firstQuestion.classList.add('active');
        firstAnswer.classList.add('sliding-down');
    }
});
</script>

<?php get_footer(); ?>