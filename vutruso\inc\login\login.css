  .login * {
      margin: 0;
      padding: 0;
  }

  #loginform {
      position: relative !important;
      border-radius: 3px;
  }
  .login form {
    background-color: transparent;
    box-shadow: none;
    margin-top: 0;
    padding: 20px 15px;
    margin-left: 0;
    font-weight: 400;
    overflow: hidden;
    border-radius: 6px;
    background: #fff;
    border: 1px solid #f1f1f1;
  }

  .login form p {
      background-color: #fff;
      border-radius: 0 0 3px 3px;
      position: relative;
      min-height: 44px;
      margin-bottom: 0px;
  }

  .login form p label {
    padding: 0px 0px 0 0px;
    display: block;
    color: #ccc;
    font-size: 14px;
    float: left;
    width: 100%;
    text-align: left;
    margin-top: 7%;
  }
.forgetmenot label{
	
}
.user-pass-wrap{
	padding: 0 0 8px 0;
    display: block;
    color: #ccc;
    font-size: 14px;
    float: left;    margin-top: 7%;
}
.user-pass-wrap>label{float:left}
  .login #login_error {
      text-align: left;
  }

  #login_error strong {
      text-transform: uppercase;
  }

  #login_error br {
      line-height: 23px;
  }
#login {
    width: 320px;
    padding: 4% 0 0!important;
    margin: auto;
}
  .login form .input {
/*       position: absolute;
      top: 0;
      left: 0; */
      margin: 0;
      border-radius: 0;
      padding: 3px 0;
      height: 44px;
      background: transparent;
      border-width: 0;
      border-bottom-width: 1px;
      box-shadow: none;
      font-size: 24px;
      width: 100%;
      color: #32373c;
      outline: 0;
      transition: 50ms border-color ease-in-out;
  }

  #login form p.submit {
      margin: 0px;
      padding: 0px;
      border: none;
      clear: both;
      padding-top: 10px;
  }
.forgetmenot{clear:both;float:left}
  .login form .forgetmenot,
  .login form .submit {
      min-height: auto;
      float: none;
      padding-bottom: 10px;
      background: transparent;
  }
.login .button.wp-hide-pw {
    position: absolute;
    right: 0;
    top: 26px;
}
  #wp-submit {
      background: #02b875;
      border-color: #02b875;
      color: white;
      box-shadow: none;
      text-shadow: none;
      float: none;
      box-shadow: none;
      text-shadow: none;
      font-weight: bold;
      text-transform: uppercase;
  }

  #loginform .button-primary:hover,
  #loginform .button-primary:focus {
      background: #02c77f;
      border-color: #02c77f;
      color: white;
      box-shadow: none;
      text-shadow: none;
      font-weight: bold;
      text-transform: uppercase;
  }

  .login form .button {
      width: 100%;
      font-size: 16px;
      height: auto !important;
      line-height: 28px !important;
      padding: 6px 10px !important;
  }

  .wp-core-ui p .button {
      vertical-align: baseline;
  }

  .login form input[type=checkbox],
  input[type=radio] {
      border: 1px solid #b4b9be;
      background: #fff;
      color: #555;
      clear: none;
      cursor: pointer;
      display: inline-block;
      line-height: 0;
      height: 16px;
      margin: -4px 4px 0 0;
      outline: 0;
      padding: 0 !important;
      text-align: center;
      vertical-align: middle;
      width: 16px;
      min-width: 16px;
      -webkit-appearance: none;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
      transition: .05s border-color ease-in-out;
  }

  .login form .input:focus,
  .login form .input:active,
  .login form .input.active,
  .login form .input.has-value {
      background: #fff !important;
      z-index: 1;
      border-color: #02b875;
  }

  .login .privacy-policy-page-link {
      text-align: center;
      width: 100%;
      margin: 0 !important;
      display: none;
  }

  /*  @media screen and (min-width: 783px) {

  #login-form {
      width: 320px;
}
      #login,
      #login-form {
          display: table-cell;
          padding: 60px !important;
          vertical-align: middle;
      }

}*/


  body {
    height: 100vh;
    background-image: linear-gradient(-181deg, #181546 29%, #b24e88 100%);
    text-align: center;
    background: #1d0f5e;
  }

  .login #backtoblog a,
  .login #nav a {
      text-decoration: none;
      color: #fff !important;
  }

  .container {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
  }

  .pink-planet {
      z-index: 1000;
      width: 268px;
      height: 268px;
      border-radius: 100%;
      background: #fa87bf;
      box-shadow: 0 2px 17px 7px rgba(242, 186, 198, 0.26);
  }

  .pink-shadow {
      z-index: 500;
      position: absolute;
      border-radius: 100%;
      box-shadow: inset -40px -40px 0px #f56cb2;
      width: 268px;
      height: 268px;
  }

  .blue-planet {
      top: -115px;
      left: -554px;
      width: 68px;
      height: 68px;
      position: absolute;
      border-radius: 100%;
      background: #32C0D0;
      opacity: 0.1;
      /*box-shadow: 0 2px 17px 7px rgba(50, 192, 208, 0.42);*/
  }

  .blue-planet1 {
      bottom: -40px;
      right: -600px;
      width: 68px;
      height: 68px;
      position: absolute;
      border-radius: 100%;
      background: #32C0D0;
      opacity: 0.1;
      /*box-shadow: 0 2px 17px 7px rgba(50, 192, 208, 0.42);*/
  }

  .blue-planet:before {
      content: '';
      background: #52D0DE;
      border-radius: 100%;
      width: 12px;
      height: 12px;
      display: inline-block;
      margin: 10px 15px;
  }

  .blue-planet1:before {
      content: '';
      background: #52D0DE;
      border-radius: 100%;
      width: 12px;
      height: 12px;
      display: inline-block;
      margin: 10px 15px;
  }

  .blue-shadow {
      z-index: 10000;
      top: 0;
      right: -100px;
      width: 68px;
      height: 68px;
      position: absolute;
      border-radius: 100%;
      box-shadow: inset -10px -10px 0px #28AEBE;
  }

  .white-gradient {
      z-index: -100;
      top: 50%;
      left: -20%;
      width: 150%;
      height: 640px;
      border-radius: 100%;
      opacity: 0.18;
      position: fixed;
      box-shadow: inset 0 1px 100px 100px rgba(255, 255, 255, 0.5);
  }

  .orbit {
      top: -65px;
      right: -95px;
      width: 399px;
      height: 399px;
      position: absolute;
      border-radius: 100%;
      opacity: 0.1;
      border: 3px solid #FFFFFF;
  }

  .circle {
      z-index: 10000;
      background: #FAA3DC;
      opacity: 0.5;
      position: absolute;
      border-radius: 100%;
  }

  #circle-1 {
      width: 76px;
      height: 76px;
      margin-top: 160px;
      margin-left: 45px;
  }

  #circle-2 {
      width: 50px;
      height: 50px;
      margin-top: 65px;
      margin-left: 130px;
      opacity: 0.6 !important;
  }

  #circle-3 {
      width: 12px;
      height: 12px;
      margin-top: 60px;
      margin-left: 200px;
      opacity: 1.0 !important;
  }

  #circle-4 {
      width: 24px;
      height: 24px;
      margin-top: 40px;
      margin-left: 90px;
      opacity: 1.0 !important;
  }

  #circle-5 {
      width: 12px;
      height: 12px;
      margin-top: 70px;
      margin-left: 45px;
      opacity: 1.0 !important;
  }

  #circle-6 {
      width: 24px;
      height: 24px;
      margin-top: 155px;
      margin-left: 190px;
      opacity: 0.46 !important;
  }

  #circle-7 {
      width: 24px;
      height: 24px;
      margin-top: 220px;
      margin-left: 150px;
      opacity: 0.3 !important;
  }

  .star-1 {
      z-index: 20000;
      width: 6px;
      height: 6px;
      position: absolute;
      margin-top: -290px;
      margin-left: -90px;
  }

  .star-2 {
      z-index: 20000;
      width: 6px;
      height: 6px;
      position: absolute;
      margin-top: -190px;
      margin-left: 370px;
  }

  .star-3 {
      z-index: 20000;
      width: 10px;
      height: 10px;
      position: absolute;
      margin-top: -150px;
      margin-left: -570px;
  }

  .star-1 {
      animation: glitter 4.5s linear 0s infinite normal;
      -webkit-animation: glitter 4.5s linear 0s infinite normal;
      -moz-animation: glitter 4.5s linear 0s infinite normal;
      -ms-animation: glitter 4.5s linear 0s infinite normal;
      -o-animation: glitter 4.5s linear 0s infinite normal;
  }

  .star-2 {
      animation: glitter 7s linear 0s infinite normal;
      -webkit-animation: glitter 7s linear 0s infinite normal;
      -moz-animation: glitter 7s linear 0s infinite normal;
      -ms-animation: glitter 7s linear 0s infinite normal;
      -o-animation: glitter 7s linear 0s infinite normal;
  }

  .star-3 {
      animation: glitter 7s linear 0s infinite normal;
      -webkit-animation: glitter 7s linear 0s infinite normal;
      -moz-animation: glitter 7s linear 0s infinite normal;
      -ms-animation: glitter 7s linear 0s infinite normal;
      -o-animation: glitter 7s linear 0s infinite normal;
  }

  .star-1:before,
  .star-1:after,
  .star-2:before,
  .star-2:after,
  .star-3:before,
  .star-3:after {
      content: "";
      position: absolute;
      background-color: white;
      display: block;
      left: 0;
      width: 141.4213%;
      top: 0;
      bottom: 0;
      border-radius: 5%;
      transform: rotate(66.66deg) skewX(45deg);
  }

  /* the same but +90deg to rotate */
  .star-1:after,
  .star-2:after,
  .star-3:after {
      transform: rotate(156.66deg) skew(45deg);
  }

  @-webkit-keyframes glitter {
      0% {
          -webkit-transform: scale(1);
          opacity: 1;
      }

      25% {
          -webkit-transform: scale(0.5);
          opacity: 0;
      }

      50% {
          -webkit-transform: scale(1);
          opacity: 1;
      }

      75% {
          -webkit-transform: scale(0.5);
          opacity: 0;
      }

      100% {
          -webkit-transform: scale(1);
          opacity: 1;
      }
  }

  @-moz-keyframes glitter {
      0% {
          -moz-transform: scale(1);
          opacity: 1;
      }

      25% {
          -moz-transform: scale(0.5);
          opacity: 0;
      }

      50% {
          -moz-transform: scale(1);
          opacity: 1;
      }

      75% {
          -moz-transform: scale(0.5);
          opacity: 0;
      }

      100% {
          -moz-transform: scale(1);
          opacity: 1;
      }
  }

  .small-stars {
      /*width: 600px;*/
      /*height: 100vh;*/
      display: inline-block;
  }

  .small-star {
      z-index: 10000;
      background: white;
      opacity: 0.5;
      position: fixed;
      border-radius: 100%;
      width: 8px;
      height: 8px;
  }

  #small-star-1 {
      margin-top: 50px;
      margin-left: 100px;
  }

  #small-star-2 {
      margin-top: 45px;
      margin-left: 500px;
  }

  #small-star-3 {
      margin-top: 350px;
      margin-left: 90px;
  }

  #small-star-4 {
      margin-top: 420px;
      margin-left: 0;
  }

  #small-star-5 {
      margin-top: 440px;
      margin-left: 520px;
  }

  #small-star-6 {
      margin-top: 350px;
      margin-left: 630px;
  }

 #rememberme{
    float: left;
    clear: both;
    margin-top: 3px;
    margin-right: 8px;
  
 }

