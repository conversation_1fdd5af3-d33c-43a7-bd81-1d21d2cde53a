.dt-sc-pricing-table .innerContainer {
  max-width: 86%;
}

.one-half {
  float: left;
  margin-left: 2.564102564102564%;
  width: 48.717948717948715%;
}

section.lead {
  background: url(https://vutruso.com/wp-content/themes/vutruso/img/web-bg/hero-background-pattern.png), linear-gradient(35deg, #3145b5 15%, #2db2fb 90%);
  padding: 140px 0;
  background-repeat: no-repeat;
  background-size: cover;
}

.lead-text h1 {
  color: #fff;
  font-size: 50px;
  font-size: 3rem;
  font-family: 'Baloo Chettan 2';
  line-height: 52px;
}

.lead-text h1 span {
  font-size: 42px;
  font-size: 6.2rem;
}

.lead-text p {
  font-size: 20px;
  color: #fff;
  font-weight: 400;
  padding-top: 2%;
}

.lead-text p span.clear {
  display: block;
}

.lead-button {
  background-color: #EBB000;
  border-radius: 6px;
  display: inline-block;
  position: relative;
  margin-top: 2%;
  margin-bottom: 1%;
  opacity: 1
}

.lead-button a {
  color: #fff;
  text-decoration: none;
  padding: 16px 62px 16px 98px;
  display: block;
  font-size: 20px;
  text-transform: uppercase;
  opacity: 1
}

.lead-button a:before {
  background-image: url(<?php echo get_template_directory_uri();
  ?>/img/icon/tool-button.png);
  background-size: 32px;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  top: 16px;
  left: 36px;
  position: absolute;
  content: "";
}

.lead-text .rating {
  display: none;
}

.lead-picture {
  text-align: center;
}

.lead-button:active,
.lead-button:hover,
.lead-button:focus {
  background-color: #e0a801;
}

@media only screen and (max-width:860px) {
  .lead-picture {
    display: none;
  }

  .lead-text h1,
  .lead-text h1 span {
    font-size: 36px;
    font-size: 2.6rem;
    text-align: center
  }

  .lead-text p {
    font-size: 1rem;
  }

  .lead-text p span.clear {
    display: inline;
  }

  .one-half {
    width: 100%;
  }

  section.lead {
    padding: 69px 0 44px 0;
  }
}


.align_center {
  text-align: center;
}

.last {
  margin-right: 0px;
}

.home_services p,
.process-step p {
  font-size: 14px;
  line-height: 24px;
  font-weight: 300;
  color: #656565;
}

.section_wrapper {
  background-color: #EBF7FF;
  max-width: 100%;
  overflow: hidden;
  padding-bottom: 31px;
  padding-top: 31px;
}

h2.title {
  margin-bottom: 3px;
  display: block;
  clear: both;
  font-size: 28px;
  line-height: 38px;
  text-align: center;
  font-weight: 400;
  color: #454545;
  font-family: 'Baloo Chettan 2';
}

.lead-heading h2 {
  font-family: 'Baloo Chettan 2';
}

h3.subtitle {
  margin: 0 0 50px 0;
  display: block;
  clear: both;
  font-size: 16px;
  line-height: 28px;
  text-align: center;
  font-weight: 300;
  color: #656565;
}

.separator_wrapper {
  margin-bottom: 10px;
}

.separator_first_circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  border: 3px solid #3498db;
  -moz-border-radius: 7px;
  -webkit-border-radius: 7px;
  border-radius: 7px;
}

.separator_second_circle {
  float: left;
  width: 4px;
  height: 4px;
  background-color: #3498db;
  border: 2px solid #fff;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

.separator_wrapper::after,
.separator_wrapper::before {
  background-color: #3498db;
  display: inline-block;
  vertical-align: middle;
  content: "";
  width: 70px;
  height: 1px;
  margin-top: -6px;
}

.home_services {
  margin-bottom: 35px;
}

.home_services h4 {
  font-size: 16px;
  margin-bottom: 5px;
}

span.circle_icons i {
  margin: 5px 25px 77px 0;
  font-size: 46px;
  float: left;
  color: #3498db;
  transition: all .50s ease-in-out;
  -moz-transition: all .50s ease-in-out;
  -webkit-transition: all .50s ease-in-out;
}

.animated {
  -webkit-animation-duration: 1s;
  -moz-animation-duration: 1s;
  -o-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  -moz-animation-name: fadeInLeft;
  -o-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

.animated.fadeInRight {
  -webkit-animation-name: fadeInRight;
  -moz-animation-name: fadeInRight;
  -o-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

.animated.bounceIn {
  -webkit-animation-name: bounceIn;
  -moz-animation-name: bounceIn;
  -o-animation-name: bounceIn;
  animation-name: bounceIn;
}

[class*=" icon-"]:before {
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*! CSS Used keyframes */
@-webkit-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
  }
}

@-moz-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -moz-transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    -moz-transform: translateX(0);
  }
}

@-o-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -o-transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    -o-transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
  }
}

@-moz-keyframes fadeInRight {
  0% {
    opacity: 0;
    -moz-transform: translateX(20px);
  }

  100% {
    opacity: 1;
    -moz-transform: translateX(0);
  }
}

@-o-keyframes fadeInRight {
  0% {
    opacity: 0;
    -o-transform: translateX(20px);
  }

  100% {
    opacity: 1;
    -o-transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@-webkit-keyframes bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(.3);
  }

  50% {
    opacity: 1;
    -webkit-transform: scale(1.05);
  }

  70% {
    -webkit-transform: scale(.9);
  }

  100% {
    -webkit-transform: scale(1);
  }
}

@-moz-keyframes bounceIn {
  0% {
    opacity: 0;
    -moz-transform: scale(.3);
  }

  50% {
    opacity: 1;
    -moz-transform: scale(1.05);
  }

  70% {
    -moz-transform: scale(.9);
  }

  100% {
    -moz-transform: scale(1);
  }
}

@-o-keyframes bounceIn {
  0% {
    opacity: 0;
    -o-transform: scale(.3);
  }

  50% {
    opacity: 1;
    -o-transform: scale(1.05);
  }

  70% {
    -o-transform: scale(.9);
  }

  100% {
    -o-transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    transform: scale(1);
  }
}


.tabs_wrapper ul .active {
  background-image: linear-gradient(to right top, #b0d55e, #b4d461, #b7d465, #bbd368, #bed36c, #c2d369, #c5d267, #c9d264, #cfd159, #d5d04e, #dccf43, #e3cd36);
}

.tab_content h2 {
  font-size: 1.4em
}

.tab_content li {
  display: list-item;
  text-align: -webkit-match-parent;
  list-style: circle;
  margin-left: 20px;
  line-height: 27px;
  font-size: 15px;
}

.tab_content p {
  font-size: 16px;
  line-height: 24px;
  font-weight: 300;
  color: #656565;
  margin-bottom: 1%
}

.toi-uu {
  padding: 2% 0px;
}

.tabs_wrapper {
  width: 100%;
  text-align: left;
  margin: 0 auto;
  background: transparent;
  padding-top: 4%;
  clear: both;
  margin-bottom: 1%;
}

ul.tabs {
  display: inline-block;
  vertical-align: top;
  position: relative;
  z-index: 10;
  margin: 25px 0 0;
  padding: 0;
  width: 23%;
  min-width: 175px;
  list-style: none;
  -ms-transition: all .3s ease;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
  border-bottom: none !important;
}

ul.tabs li {
  cursor: pointer;
  line-height: 31px;
  color: white;
  text-align: left;
  font-weight: bold;
  -ms-transition: all .3s ease;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
  background-color: transparent;
  background-image: linear-gradient(90deg, #2855a7 0%, #3498db 100%);
  float: left;
  list-style: none;
  clear: both;
  padding: 5px 20px;
  width: 88%;
  margin: 4px;
  border-radius: 4px;
}

.tab_container {
  display: inline-block;
  vertical-align: top;
  position: relative;
  z-index: 20;
  left: -2%;
  width: 76%;
  min-width: 10px;
  text-align: left;
  background: white;
  border-radius: 12px;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.5);
}

.tab_drawer_heading {
  display: none;
}

.tab_content {
  padding: 20px;
  height: 100%;
  display: none;
}

#content ul.tabs li {
  float: left;
  list-style: none;
  padding: 10px 20px;
  clear: both;
  background-color: #f1f1f1;
  margin: 0 0 10px;
  border-radius: 10px 10px 10px 10px;
  width: 74%
}

@media screen and (max-width: 768px) {
  .tab_container {
    left: 0;
    width: 100%
  }

  ul.tabs li {
    float: left;
    clear: inherit;
    width: 100%;
    width: 47%;
  }

  #content ul.tabs {
    width: 100% !important;
    padding-bottom: 4%;
  }

  #content ul.tabs li {
    margin: 3px;
    width: auto !important;
    clear: none;
  }

  ul.tabs {
    width: 100%;
  }

  .innerContainer {
    padding-right: 8px;
    padding-left: 8px;

  }

  .tab_container {
    margin-top: 8px;
  }

  .lead-heading h2 {
    font-size: 16px;
    text-transform: uppercase;
    clear: both;
    max-width: 90%;
    margin: 0 auto;
    text-align: center;
  }

  .lead-button {
    margin-top: 4%;
    margin-bottom: 2%;

  }

}


.wpcf7-url {
  border-top: none;
  border-left: none;
  border-right: none
}

.quote-form-outer {
  float: right;
  background: #315893;
  border-radius: 3px;
  padding: 54px 20px 10px;
  color: #fff;
}

.quote-form-outer h5 {
  width: 100%;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  text-transform: uppercase;
  margin: 0 0 14px 0;
}

.quote-form-outer p {
  width: 100%;
  text-align: center;
  font-size: 15px;
  line-height: 23px;
  color: rgba(255, 255, 255, .7);
  padding: 0 16px;
  margin: 0 0 19px 0;
}

.quote-form {
  width: 100%;
  float: left;
}

.quote-btm-txt {
  width: 100%;
  float: left;
  font-size: 15px;
  color: #fff;
  line-height: 1;
  margin: 20px 0 0 0;
}

.quote-btm-txt span {
  font-size: 19px;
  font-weight: 500;
  color: #28ac6d;
  display: inline-block;
  vertical-align: middle;
  margin: -2px 0 0 0;
}

.quoteform2 .quote-form-outer {
  width: 100%;
  background: #315893;
  border: 2px solid #28ac6d;
  padding: 12px 0 0 0;
}

.quoteform2 .quote-form-outer h5 {
  color: #28ac6d;
}

.quoteform2 .quote-form-outer p {
  color: #4c4c4c;
}

.quoteform2 .quote-form {
  background: #f7f7f7;
  padding: 0 30px;
  margin: 4px 0 0 0;
}

.quote-btm-txt {
  color: #404040;
}

.quote-form .gform_wrapper ul.gform_fields li.gfield {
  width: 100%;
  float: left;
  padding: 0;
  margin: 0 0 8px 0 !important;
}

.quote-form .ginput_container {
  width: 100%;
  float: left;
  margin: 0 !important;
  padding: 5px
}

.quote-form .ginput_container input {
  width: 100% !important;
  float: left;
  font-size: 15px !important;
  font-weight: 300;
  background: none !important;
  padding: 0 5px !important;
  color: #fff;
  height: 39px;
  border-bottom: 1px solid rgba(255, 255, 255, .2);
  min-height: inherit;
  margin: 0 !important;
}

.quote-form .gform_wrapper {
  width: 100%;
  float: left;
  margin: 0 !important;
}

.quote-form .gform_body {
  width: 100%;
  float: left;
}

.gform_wrapper .wpcf7-response-output {
  clear: both
}

.gform_wrapper .wpcf7-validation-errors,
.gform_wrapper .wpcf7-acceptance-missing {
  color: black;
}

.quote-form .gform_footer {
  width: 100%;
  float: left;
  text-align: center;
}

.quote-form .gform_footer .gform_button {
  width: 100% !important;
  height: 55px;
  min-height: inherit !important;
  padding: 0;
  float: none;
  display: inline-block;
  vertical-align: top;
  text-align: center;
  background: #28ac6d;
  border: 1px solid #28ac6d;
  border-radius: 2px;
  font-size: 17px !important;
  font-weight: 600;
  text-transform: uppercase;
  color: #fff;
  margin: 12px 0 0 0 !important;
}

.quote-form .gform_footer .gform_button:hover {
  background: #52a07b;
  ;
  color: #28ac6d;
}

.quoteform2 .quote-form .ginput_container input {
  color: rgba(0, 0, 0, .5) !important;
  border-bottom: 1px solid #c6c6c6;
}

.gform_wrapper {
  margin: 16px 0;
  max-width: 100%;
}

.gform_wrapper form {
  text-align: left;
  max-width: 100%;
  margin: 0 auto;
}

.gform_wrapper *,
.gform_wrapper :after,
.gform_wrapper :before {
  box-sizing: border-box !important;
}

.gform_wrapper input:not([type=radio]):not([type=checkbox]):not([type=submit]):not([type=button]):not([type=image]):not([type=file]) {
  font-size: inherit;
  font-family: inherit;
  padding: 5px 4px;
  letter-spacing: normal;
}

.gform_wrapper ul.gform_fields {
  margin: 0 !important;
  list-style-type: none;
  display: block;
}

.gform_wrapper ul {
  text-indent: 0;
}

.gform_wrapper form li,
.gform_wrapper li {
  margin-left: 0 !important;
  list-style: none !important;
  overflow: visible;
}

.gform_wrapper ul li.gfield {
  clear: both;
}

.gform_wrapper ul li:after,
.gform_wrapper ul li:before,
.gform_wrapper ul.gform_fields {
  padding: 0;
  margin: 0;
  overflow: visible;
}

.gform_wrapper label.gfield_label {
  font-weight: 700;
  font-size: inherit;
}

.gform_wrapper .top_label .gfield_label {
  display: -moz-inline-stack;
  display: inline-block;
  line-height: 1.3;
  clear: both;
}

body .gform_wrapper .top_label div.ginput_container {
  margin-top: 8px;
}

.gform_wrapper .hidden_label .gfield_label {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

.gform_wrapper li.hidden_label input {
  margin-top: 12px;
}

.gform_wrapper input.medium {
  width: 100%;
}

.gform_wrapper .gfield_required {
  color: #790000;
  margin-left: 4px;
}

.gform_wrapper .gform_footer {
  padding: 0px 0 10px;
  margin: 0;
  clear: both;
  width: 100%;
}

.gform_wrapper .gform_footer input.button,
.gform_wrapper .gform_footer input[type=submit] {
  font-size: 1em;
  width: 100%;
  margin: 0 0 16px;
}

.gform_wrapper .gform_hidden,
.gform_wrapper input.gform_hidden,
.gform_wrapper input[type=hidden] {
  display: none !important;
  max-height: 1px !important;
  overflow: hidden;
}

body .gform_wrapper ul li.gfield {
  margin-top: 16px;
  padding-top: 0;
}

.gform_wrapper.gf_browser_unknown ul li:after,
.gform_wrapper.gf_browser_unknown ul li:before {
  content: none;
}

.wpcf7 form .wpcf7-response-output {
  margin: 2em .5em 1em;
  padding: .2em 1em;
  border: 2px solid #00a0d2;
  clear: both;
}


@media screen and (max-width:767px) {
  .btn {
    font-size: 15px;
    padding: 13px 25px 11px;
  }

  .quote-form-outer {
    width: 45%;
    padding: 25px 15px 25px;
  }

  .quote-form-outer h5 {
    font-size: 22px;
  }

  .quote-form-outer p {
    font-size: 14px;
    line-height: 22px;
    padding: 0;
  }

  .quote-btm-txt span {
    font-size: 18px;
  }

  .quoteform2 {
    width: 45%;
  }

  .quoteform2 .quote-form-outer {
    padding: 12px 0 0 0;
  }

  .quoteform2 .quote-form {
    margin: 0;
    padding: 28px 17px 25px;
  }

  .quote-form-outer p {
    font-size: 13px;
  }

  .gform_wrapper .ginput_container span:not(.ginput_price) {
    margin-bottom: 0 !important;
  }

  .quote-form .gform_footer .gform_button {
    width: 100% !important;
    height: 50px;
    margin: 5px 0 0 0 !important;
  }
}

@media screen and (max-width:639px) {
  .btn {
    font-size: 13px;
    padding: 12px 20px 10px;
  }

  .quote-form-outer {
    width: 100%;
    padding: 25px;
  }

  .quoteform2 {
    width: 100%;
  }
}

@media screen and (max-width:413px) {
  .btn {
    font-size: 12px;
    padding: 11px 15px 8px;
  }

  .quote-form-outer h5 {
    font-size: 20px;
    margin: 0 0 10px 0;
  }

  .quote-form-outer p {
    font-size: 13px;
    line-height: 20px;
    margin: 0 0 15px 0;
  }

  .quote-form-outer {
    padding: 20px;
  }

  .inner-video {
    padding: 25px 0;
  }

  .inner-video-box {
    height: 200px;
  }

  .video-caption h4 {
    font-size: 16px;
    line-height: 22px;
  }

  .video-caption p {
    line-height: 22px;
  }

  .quote-form-outer p {
    margin: 0 0 10px 0;
  }
}

@media only screen and (max-width:641px) {
  .gform_wrapper input:not([type=radio]):not([type=checkbox]):not([type=image]):not([type=file]) {
    line-height: 2;
    min-height: 2rem;
  }

  .gform_wrapper .ginput_container span:not(.ginput_price) {
    margin-bottom: 8px;
    display: block;
  }
}

@media only screen and (min-width:641px) {
  .gform_wrapper .gform_body {
    width: 100%;
  }

  .gform_wrapper .top_label input.medium {
    width: calc(50% - 8px);
  }

  .gform_wrapper .gform_footer input.button,
  .gform_wrapper .gform_footer input[type=submit] {
    display: -moz-inline-stack;
    display: inline-block;
  }

  .gform_wrapper .gform_footer input.button,
  .gform_wrapper .gform_footer input[type=submit] {
    font-size: 1em;
    width: auto;
    margin: 0 16px 0 0;
  }

  .gform_wrapper ul.gform_fields li.gfield {
    padding-right: 16px;
  }
}


.accordion {
  margin: 2rem auto;
}

.accordion-item {
  background-color: #fff;
  color: #111;
  margin: 1rem 0;
  border-radius: 0.5rem;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.25);
}

.accordion-item-header {
  padding: 0.5rem 3rem 0.5rem 1rem;
  min-height: 3.5rem;
  line-height: 1.25rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.accordion-item-header::after {
  content: "\002B";
  font-size: 2rem;
  position: absolute;
  right: 1rem;
}

.accordion-item-header.active::after {
  content: "\2212";
}

.accordion-item-body {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}

.accordion-item-body-content {
  padding: 1rem;
  line-height: 1.5rem;
  border-top: 1px solid;
  border-image: linear-gradient(to right, transparent, #34495e, transparent) 1;
}


.clearfix::before,
.wrap::before {
  content: " ";
  display: table;
}

.clearfix::after,
.wrap::after {
  clear: both;
  content: " ";
  display: table;
}

.wrap {
  margin: 0 auto;
  max-width: 1140px;
}

.process {
  padding: 23px 0;
  background-color: #EBF7FF;
  background-image: url(https://vutruso.com/wp-content/themes/vutruso/img/web-bg/sekcja3_bg.jpg);
}

.process-step .icon img {
  max-width: 200px;
}

.lead-heading {
  text-align: center;
  margin: 0 auto;
  width: 100%;
  clear: both;
  display: block;
  margin-bottom: 2%;
}

.lead-heading h2 {
  font-size: 4.2rem;
}

.lead-heading p {
  margin-bottom: 0;
}

.process-step {
  text-align: center
}

.process-step h4 {
  font-size: 18px;
  padding-bottom: 14px;
}

::-webkit-input-placeholder {
  color: inherit;
  opacity: .54;
}

.process-container::before,
.process-container::after {
  box-sizing: inherit;
}

.one-third {
  float: left;
  margin-left: 2.564102564102564%;
  width: 31.623931623931625%;
}

.first {
  clear: both;
  margin-left: 0;
}

@media only screen and (max-width:1340px) {
  .wrap {
    max-width: 1140px;
  }
}

@media only screen and (max-width:1200px) {
  .wrap {
    max-width: 960px;
  }
}

@media only screen and (max-width:1023px) {
  .wrap {
    max-width: 800px;
  }
}

@media only screen and (max-width:860px) {
  .wrap {
    padding-left: 5%;
    padding-right: 5%;
  }
}

@media only screen and (max-width:1340px) {
  .wrap {
    max-width: 1140px;
  }
}

@media only screen and (max-width:1200px) {
  .wrap {
    max-width: 960px;
  }
}

@media only screen and (max-width:1023px) {
  .wrap {
    max-width: 800px;
  }
}

@media only screen and (max-width:860px) {
  .wrap {
    padding-left: 5%;
    padding-right: 5%;
  }

  .one-third {
    margin: 0;
    width: 100%;
  }
}


.dt-sc-pricing-table {
  float: left;
  line-height: 1;
  margin-bottom: 20px;
  padding: 0;
  width: 100%;
  margin-left: -3px;
  margin-top: 3%
}

.dt-sc-pricing-table.type2 table {
  border-left: none;
}

.dt-sc-pricing-table.type2 td {
  line-height: 28px;
  padding: 16px 15px;
  width: 155px;
  background-color: #fff;
}

.dt-sc-pricing-table.type2 td:first-child {
  font-size: 13px;
  padding-left: 0;
  text-align: left;
  font-weight: 500;
  text-transform: none;
}

.dt-sc-pricing-table.type2 td:last-child {
  border-right: none;
}

.dt-sc-pricing-table table {
  border-collapse: separate;
  border-spacing: 0;
  border-color: #dfdfdf;
  border-style: solid;
  border-width: 1px 0 0 1px;
  clear: both;
  margin-bottom: 20px;
  width: 100%;
  margin: 0px 0;
  padding: 0;
}

.dt-sc-pricing-table td {
  background: #fff;
  border-color: #dfdfdf;
  border-style: solid;
  border-width: 0 1px 1px 0;
  line-height: normal;
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  padding: 12px 10px;
}

.dt-sc-pricing-table img,
#primary img {
  height: auto;
  max-width: 100%;
}

.dt-sc-pricing-table img.size-full {
  max-width: 99.6%;
  height: auto;
}

.dt-sc-fullwidth-section {
  clear: both;
  float: left;
  margin: 0;
  padding: 0;
  width: 100%;
  margin-top: 3%;
}

.dt-sc-fullwidth-section h2 {
  font-size: 22px;
  margin-bottom: 2px;
  text-align: center;
  text-transform: capitalize;
}

.dt-sc-pricing-table.type2 table.pricing-table {
  background: #fff none repeat scroll 0 0;
  border: 5px solid #d1d1d1;
  border-radius: 0;
  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
  table-layout: fixed;
  width: 100%;
}

.dt-sc-pricing-table.type2 table.pricing-table .table-separator td {
  background: #e1e1e1;
  border-top: 1px solid #464646;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
}

.dt-sc-pricing-table.type2 table.pricing-table .table-header td,
.dt-sc-pricing-table.type2 table.pricing-table .table-content td {
  border-left: 1px solid #464646;
  border-top: 1px solid #464646;
  padding: 20px 6px 12px 6px;
  line-height: 18px;
}

.dt-sc-pricing-table.type2 table.pricing-table td.last {
  border-right: 1px solid #464646;
}

.pricing-table .title-row-1 .first-col h3 {
  color: #d47b09;
  font-size: 1.2em;
  font-weight: 600;
  padding: 15px;
  margin: 0px;
}

.pricing-table .title-row-1 .first-col p.packages {
  line-height: 38px;
  font-size: 1em;
  color: #233d87 !important;
  font-weight: 800;
  padding: 0
}

.pricing-table .order p.packages {
  font-size: 1.8em;
  color: #000 !important;
}

.pricing-table .order p.packages-fine-print {
  font-size: 0.9em;
  color: #000 !important;
  font-style: italic;
  line-height: 8pt;
  font-weight: 400;
  text-transform: none;
}

.pricing-table .table-header p {
  color: #fff !important;
}

.pricing-table .table-header {
  border-left: 1px solid #464646;
  border-top: 1px solid #464646;
  color: #000 !important;
}

.pricing-table .table-content {
  border-left: 1px solid #797979;
  border-top: 1px solid #797979;
}

.pricing-table .table-content td {
  font-size: 11px !important;
}

.pricing-table p {
  font-size: 1.0em;
  line-height: 15pt;
}

.pricing-table p.desc {
  font-size: 14px;
  margin: -1px 0 !important;
  white-space: nowrap;
  font-weight: 500;
}

.pricing-table p.actual-price {
  font-size: 23px;
  margin: 10px 0px 5px 0px !important;
  white-space: nowrap;
  font-weight: 700;
  font-style: italic;
}

.pricing-table p.note-price {
  font-size: 15px;
  margin: 0px 0px 0px 0px !important;
  white-space: nowrap;
  font-weight: 600;
}

.pricing-table p.gst-price {
  font-size: 12px;
  padding: 5px 0px;
  border-top: 1px dotted #cccccc;
  margin: 20px 0px 0px 0px;
  white-space: nowrap;
}

.pricing-table .table-header .plan1 {
  background: #f8981d !important;
  color: #fff !important;
}

.pricing-table .table-header .plan1 div.head:after {
  content: "";
  position: absolute;
  height: 48px;
  width: 48px;
  margin-left: -11px;
}

.pricing-table .table-header .plan1 div.head {
  position: relative;
}

.one-plans .table-header .plan1 {
  background: #f8981d !important;
  color: #fff !important;
}

.one-plans .table-header .plan1 div.head:before {
  background: none repeat scroll 0 0 #f8981d;
}

.one-plans .table-header .plan1 div.head:after {
  margin-top: -281px;
}

.one-plans .table-header.title-row-1 td {
  width: 40%;
}

.one-plans .table-header td.plan1 {
  width: 50%;
}

.one-plans .table-header .plan1 .actual-price {
  padding-top: 18px;
}

@media only screen and (min-width : 480px) {
  .dt-sc-pricing-table {
    margin-left: 0px;
    width: 100%;
  }
}

@media only screen and (min-width : 768px) {
  .pricing-table .table-content td {
    font-size: 15px !important;
  }

  .pricing-table p {
    font-size: 11pt;
    line-height: 15pt;
  }
}

@media only screen and (min-width:960px) and (max-width:1240px) {
  .dt-sc-pricing-table img {
    height: auto;
    max-width: 100%;
  }

  .dt-sc-pricing-table td {
    padding: 10px 5px;
    font-size: 12px;
  }
}

@media only screen and (min-width:768px) and (max-width:959px) {
  .dt-sc-pricing-table img {
    height: auto;
  }

  .dt-sc-pricing-table td {
    font-size: 12px;
  }
}

@media only screen and (max-width: 767px) {
  .dt-sc-pricing-table.type2 table {
    display: block;
    overflow-x: auto;
    position: relative;
    width: auto;
  }
}

@media only screen and (max-width: 479px) {
  .dt-sc-pricing-table td {
    font-size: 8px;
    padding: 8px 0;
  }

  .dt-sc-pricing-table.type2 td {
    font-size: 13px;
  }
}


.align-items-center {
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
}

.justify-content-center {
  -webkit-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.btn {
  display: inline-block;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: .25rem;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, .125);
  border-radius: .25rem;
}

.card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem;
}

.card-body h4 {
  padding-bottom: 12%;
}

.text-center {
  text-align: center;
}

.btnAnimated {
  display: inline-block;
  position: relative;
  z-index: 1;
  font-size: 16px;
  color: #fff;
  outline: none;
  cursor: pointer;
  overflow: hidden;
  line-height: 42px;
  text-align: center;
  padding: 0 20px 0 45px;
  background: #ef323a;
  -webkit-border-radius: 28px;
  -moz-border-radius: 28px;
  -ms-border-radius: 28px;
  border-radius: 28px;
  -webkit-transition: all .3s ease-in-out;
  -o-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

.btnAnimated:hover {
  text-decoration: none;
}

.btnAnimated:before {
  position: absolute;
  display: block;
  content: '';
  width: 20px;
  height: 20px;
  top: 11px;
  left: 19px;
  z-index: -1;
  pointer-events: none;
  background-color: unset;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(1, 42, 94, 1);
  box-shadow: 0 0 0 0 rgba(1, 42, 94, 1);
}

.btnAnimated:after {
  position: absolute;
  display: block;
  content: '';
  width: 22px;
  height: 22px;
  top: 10px;
  left: 18px;
  z-index: -1;
  pointer-events: none;
  background-color: transparent;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 5px #0c49c4;
  box-shadow: inset 0 0 0 5px #0c49c4;
}

.btnAnimated:hover:before {
  -webkit-transition: all .5s ease-in-out .2s;
  -o-transition: all .5s ease-in-out .2s;
  transition: all .5s ease-in-out .2s;
  -webkit-box-shadow: 0 0 0 30px rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 30px rgba(0, 0, 0, 0);
}

.yellow-btn-default {
  background: #0c49c4;
  border-radius: 100px;
  -webkit-border-radius: 100px;
  font-size: 15px;
  text-transform: uppercase;
  color: #fff !important;
  line-height: 49px;
}

.yellow-btn-default:before {
  -webkit-box-shadow: 0 0 0 0 #fff;
  box-shadow: 0 0 0 0 #fff;
  top: 13px;
}

.yellow-btn-default:after {
  -webkit-box-shadow: inset 0 0 0 6px #fff;
  box-shadow: inset 0 0 0 6px #fff;
  top: 13px;
}

.yellow-btn-default:hover {
  background: #110F0F;
  color: #fff;
}

.small-title {
  font-size: 18px;
}

.spaceTop75 {
  padding-top: 75px;
}

.border_bottom {
  border-bottom: 2px solid #0c49c4;
  padding-bottom: 8px;
  display: inline-block;
  font-size: 28px
}

.bold {
  font-weight: 600;
}

.circle-bg {
  background: url(https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/bottom-circle.png) 105% 190% no-repeat;
  background-color: rgba(195, 205, 225, 0.08);
}

.columns2 .card {
  height: auto;
  margin-bottom: 35px;
  padding-top: 30px;
  display: inline-block;
  width: 100%;
}

.rounded-corner {
  border: 0;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.common-section {
  background-repeat: no-repeat !important;
  clear: both;
}

.circle-bg p {
  font-size: 16px;
  line-height: 27px;
}

.rounded-corner .card-text {
  font-size: 15px;
  line-height: 21px;
}

.lead p.small {
  font-size: 16px
}

@media screen and (min-width: 576px) and (max-width: 767px) {
  .card {
    -ms-flex: 1 0 44% !important;
    flex: 2 1 44% !important;
    margin-bottom: 25px !important;
  }

  .card-columns {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 2;
  }
}

@media (min-width:768px) {
  .card-columns {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2;
    -webkit-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
  }
}

@media (max-width:1024px) {
  .card-columns {
    padding-top: 32px;
  }

  .spaceTop75 {
    padding-top: 30px;
  }
}

@media (max-width: 768px) {
  .common-section p {
    font-size: 16px;
    line-height: 27px;
  }

  .card-body {
    padding: 20px;
  }

  .spaceTop75 {
    padding-top: 30px;
  }

  .yellow-btn-default {
    margin-bottom: 30px;
    margin-top: 15px !important;
  }

  .first-col {
    width: 200px;
  }

  .card {
    padding: 30px 0px;
  }

  .card-columns {
    display: flex;
    gap: 10px;
  }

  ul.tabs li {
    padding: 5px 11px;
    width: 47.78%;

  }

  ul.tabs li:last-child {
    width: 98%;
  }
}

@media (max-width: 767px) {
  .yellow-btn-default {
    margin-bottom: 30px;
  }
}

@media (max-width: 640px) {
  .spaceTop75 {
    padding-top: 30px;
  }
}

@media (max-width: 480px) {
  .spaceTop75 {
    padding-top: 30px;
  }
}