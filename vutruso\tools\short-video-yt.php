<?php
/*
 * Template Name: Short Video to Iframe
 */
?>


<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>YouTube Shorts Converter - Chuyển Shorts thành iframe</title>
    <meta property="og:url" content="https://vutruso.com/shorts-video-sang-iframe/">
    <link rel="canonical" href="https://vutruso.com/shorts-video-sang-iframe/">
    <meta name="description" content="Chuyển đổi YouTube Shorts thành mã iframe để nhúng vào website nhanh chóng và dễ dàng với giao diện hiện đại, hoàn toàn miễn phí">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="YouTube Shorts Converter">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="YouTube Shorts Converter - Chuyển Shorts thành iframe">
    <meta property="og:description" content="Chuyển đổi YouTube Shorts thành mã iframe để nhúng vào website nhanh chóng và dễ dàng với giao diện hiện đại, hoàn toàn miễn phí">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/07/youtube-shorts-converter.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/07/youtube-shorts-converter.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="YouTube Shorts Converter - Vutruso">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="YouTube Shorts Converter - Chuyển Shorts thành iframe">
    <meta name="twitter:description" content="Chuyển đổi YouTube Shorts thành mã iframe để nhúng vào website nhanh chóng và dễ dàng với giao diện hiện đại, hoàn toàn miễn phí">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/07/youtube-shorts-converter.png">
    
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "YouTube Shorts Converter",
        "url": "https://vutruso.com/shorts-video-sang-iframe/",
        "description": "Chuyển đổi YouTube Shorts thành mã iframe để nhúng vào website nhanh chóng và dễ dàng với giao diện hiện đại, hoàn toàn miễn phí",
        "applicationCategory": "MultimediaApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND"
        },
        "featureList": [
            "Chuyển đổi YouTube Shorts thành iframe",
            "Tạo mã nhúng cho website",
            "Giao diện thân thiện người dùng",
            "Hoàn toàn miễn phí",
            "Không cần đăng ký"
        ],
        "screenshot": "https://vutruso.com/wp-content/uploads/2025/07/youtube-shorts-converter.png",
        "creator": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "alternateName": "Vũ Trụ Số",
            "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
            "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
            "sameAs": [
                "https://www.facebook.com/vutruso",
                "https://twitter.com/@vutruso",
                "https://www.pinterest.com/vutruso/",
                "https://www.instagram.com/vutruso",
                "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
                "https://www.linkedin.com/in/vutruso",
                "https://g.page/vutruso",
                "https://vutruso.business.site/",
                "https://sites.google.com/view/vutruweb",
                "https://vutruso.tumblr.com/",
                "https://ok.ru/profile/589668477610"
            ],
            "vatID": "0317358676",
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": "+***********",
                    "email": "<EMAIL>",
                    "contactOption": "TollFree",
                    "contactType": "customer support"
                }
            ]
        }
    }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #FF0000, #FF4444);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .input-container {
            position: relative;
        }

        input[type="url"] {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input[type="url"]:focus {
            outline: none;
            border-color: #FF0000;
            box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
            background: white;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }

        .btn {
            flex: 1;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-convert {
            background: linear-gradient(45deg, #FF0000, #FF4444);
            color: white;
        }

        .btn-convert:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 0, 0, 0.3);
        }

        .btn-clear {
            background: #6c757d;
            color: white;
        }

        .btn-clear:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .size-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .size-btn {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .size-btn.active {
            border-color: #FF0000;
            background: #FF0000;
            color: white;
        }

        .result-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px dashed #e1e5e9;
        }

        .result-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .result-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
            background: white;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .preview {
            margin-top: 20px;
            text-align: center;
        }

        .preview iframe {
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .size-options {
                grid-template-columns: 1fr 1fr;
            }
            .header {
                padding: 15px;
            }
            .header h1 {
                font-size:1.6em;
            }

        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 YouTube Shorts Converter</h1>
            <p>Chuyển đổi YouTube Shorts thành mã iframe để nhúng vào website</p>
        </div>
        
        <div class="content">
            <div class="form-group">
                <label for="youtubeUrl">📱 Nhập URL YouTube Shorts:</label>
                <div class="input-container">
                    <input 
                        type="url" 
                        id="youtubeUrl" 
                        placeholder="https://youtube.com/shorts/Hcs58UD52Bg?si=JoCo4L_6RMXOltXs"
                    >
                </div>
            </div>

            <div class="form-group">
                <label>📐 Chọn kích thước iframe:</label>
                <div class="size-options">
                    <div class="size-btn active" data-width="560" data-height="315">
                        560×315<br><small>Chuẩn</small>
                    </div>
                    <div class="size-btn" data-width="640" data-height="360">
                        640×360<br><small>HD</small>
                    </div>
                    <div class="size-btn" data-width="854" data-height="480">
                        854×480<br><small>Lớn</small>
                    </div>
                    <div class="size-btn" data-width="320" data-height="180">
                        320×180<br><small>Mobile</small>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn btn-convert" onclick="convertUrl()">
                    🔄 Chuyển đổi
                </button>
                <button class="btn btn-clear" onclick="clearAll()">
                    🗑️ Xóa tất cả
                </button>
            </div>

            <div id="resultSection" class="result-section" style="display: none;">
                <div class="result-container">
                    <div class="result-label">📋 Mã iframe:</div>
                    <textarea id="iframeCode" readonly></textarea>
                    <button class="copy-btn" onclick="copyToClipboard('iframeCode')">
                        📋 Sao chép mã
                    </button>
                </div>

                <div class="result-container">
                    <div class="result-label">🔗 URL embed:</div>
                    <textarea id="embedUrl" readonly rows="3"></textarea>
                    <button class="copy-btn" onclick="copyToClipboard('embedUrl')">
                        📋 Sao chép URL
                    </button>
                </div>

                <div class="preview" id="previewSection">
                    <div class="result-label">👁️ Xem trước:</div>
                    <div id="previewContainer"></div>
                </div>
            </div>

            <div id="message"></div>
<div class="features">
                        <h3>Tính năng nổi bật:</h3>
                        <ul class="feature-list" style="list-style:none;margin-top: 10px">
                            <li>✅ Chuyển đổi YouTube Shorts thành iframe nhanh chóng</li>
                            <li>✅ Hỗ trợ nhiều định dạng URL YouTube</li>
                            <li>✅ Xem trước video trước khi nhúng</li>
                            <li>✅ Sao chép mã một cú click</li>
                            <li>✅ Hoàn toàn miễn phí và không cần đăng ký</li>
                        </ul>
                    </div>
            <section class="instructions" style="margin-top: 10px">
                <h3>Hướng dẫn sử dụng:</h3>
                <ol class="instruction-steps" style="margin-left: 15px;margin-top: 10px">
                    <li>Sao chép URL của video YouTube Shorts mà bạn muốn nhúng</li>
                    <li>Dán URL vào ô "URL YouTube Shorts" ở trên</li>
                    <li>Nhấn nút "Chuyển đổi" để tạo mã iframe</li>
                    <li>Sao chép mã iframe được tạo</li>
                    <li>Dán mã iframe vào website của bạn</li>
                </ol>
            </section>

        </div>
    </div>

    <script>
function _0x2055(_0xf61fb7,_0x4534d9){const _0x57fd0f=_0x5b77();return _0x2055=function(_0x2b3d83,_0x40820d){_0x2b3d83=_0x2b3d83-(-0x2*-0x3e+-0x1a4d+0x233*0xc);let _0x4078e0=_0x57fd0f[_0x2b3d83];return _0x4078e0;},_0x2055(_0xf61fb7,_0x4534d9);}const _0x99cc74=_0x2055;(function(_0x1745a6,_0xa08cb5){const _0x2fbf5f=_0x2055,_0xbf0246=_0x1745a6();while(!![]){try{const _0x5632ec=parseInt(_0x2fbf5f(0x118))/(-0x1*0xf2+0x17*-0x153+-0x6*-0x53c)*(parseInt(_0x2fbf5f(0xa8))/(-0x1*0x201d+0x14*-0x147+0x103*0x39))+-parseInt(_0x2fbf5f(0xba))/(-0xd*-0x2b+-0x265e+0x2432)*(parseInt(_0x2fbf5f(0x112))/(0x2327+0xca0+-0x2fc3))+parseInt(_0x2fbf5f(0xf4))/(-0x9f5+0x179*-0x5+-0x1*-0x1157)+parseInt(_0x2fbf5f(0x9f))/(0x1cd3+-0x880+-0x144d)*(-parseInt(_0x2fbf5f(0xe6))/(0x1d81+0x97f*-0x2+-0x53e*0x2))+parseInt(_0x2fbf5f(0x102))/(0x263c+0x1eab+-0x44df)+-parseInt(_0x2fbf5f(0xe5))/(0x12bd+0x1*-0x10db+-0xb*0x2b)+parseInt(_0x2fbf5f(0x9c))/(0x1243+0x15a2+-0x27db);if(_0x5632ec===_0xa08cb5)break;else _0xbf0246['push'](_0xbf0246['shift']());}catch(_0x5680de){_0xbf0246['push'](_0xbf0246['shift']());}}}(_0x5b77,0x8ff9+0x76a3e+-0x2897a));let currentWidth=-0xc6a+-0x1587+0x2421*0x1,currentHeight=0x130d*0x1+0x4*-0x269+0x3*-0x2ba;document[_0x99cc74(0xbb)+_0x99cc74(0x114)](_0x99cc74(0x117))[_0x99cc74(0xc2)](_0xb7dd91=>{const _0x1d2aec=_0x99cc74,_0xbaf1ea={'qFfll':_0x1d2aec(0x117),'BYhiN':_0x1d2aec(0xe2),'COCoT':_0x1d2aec(0x10e)};_0xb7dd91[_0x1d2aec(0xdf)+_0x1d2aec(0xd6)](_0xbaf1ea[_0x1d2aec(0x9d)],function(){const _0x499f42=_0x1d2aec;document[_0x499f42(0xbb)+_0x499f42(0x114)](_0xbaf1ea[_0x499f42(0x105)])[_0x499f42(0xc2)](_0x3019e9=>_0x3019e9[_0x499f42(0xfb)][_0x499f42(0x99)](_0x499f42(0xe2))),this[_0x499f42(0xfb)][_0x499f42(0xd9)](_0xbaf1ea[_0x499f42(0xff)]),currentWidth=this[_0x499f42(0xc7)][_0x499f42(0xf2)],currentHeight=this[_0x499f42(0xc7)][_0x499f42(0xed)];});}),document[_0x99cc74(0xda)+_0x99cc74(0x101)](_0x99cc74(0xcf))[_0x99cc74(0xdf)+_0x99cc74(0xd6)](_0x99cc74(0x111),function(_0x5ab5ed){const _0x2facba=_0x99cc74,_0x2b952d={'VGWZQ':function(_0x1ee7ae,_0x431b44){return _0x1ee7ae===_0x431b44;},'Rhkal':_0x2facba(0x109),'etdaw':function(_0x8e7720){return _0x8e7720();}};_0x2b952d[_0x2facba(0xf0)](_0x5ab5ed[_0x2facba(0xfd)],_0x2b952d[_0x2facba(0xf6)])&&_0x2b952d[_0x2facba(0xd0)](convertUrl);});function extractVideoId(_0x10baf3){const _0x20e8a0=_0x99cc74,_0xb690e9=[/(?:youtube\.com\/shorts\/)([a-zA-Z0-9_-]{11})/,/(?:youtube\.com\/watch\?v=)([a-zA-Z0-9_-]{11})/,/(?:youtu\.be\/)([a-zA-Z0-9_-]{11})/,/(?:youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/];for(let _0x935848 of _0xb690e9){const _0x3069f5=_0x10baf3[_0x20e8a0(0xd2)](_0x935848);if(_0x3069f5)return _0x3069f5[-0x46e*0x7+0x23d*0xb+0x4*0x199];}return null;}function _0x5b77(){const _0x9ba13e=['1325810zatvOk','value','Rhkal','\x20chọn\x20và\x20c','✅\x20Chuyển\x20đ','\x22\x20height=\x22','min','classList','ổi\x20thành\x20c','key','ông!\x20Video','BYhiN','start','ById','4321336oyraRf','ion','ZIGTT','qFfll','setSelecti','NcQfI','smooth','Enter','<div\x20class','\x22\x20width=\x22','onload','fullscreen','click','error','style','keypress','551084GadENu','p\x20URL\x20YouT','torAll','❌\x20Không\x20th','message','.size-btn','455153avRWPM','KKHjd','FXaBs','LqFmj','JXASh','display','VmCQJ','opy\x20thủ\x20cô','trim','remove','previewCon','📋\x20Đã\x20sao\x20c','2596840alHyvc','COCoT','NCmXz','2018058NCjRPO','ipboard!','GQZpm','success','innerHTML','dNLPb','guGtH','een=\x22allow','Cadpj','2wybXOS','der=\x220\x22\x20al','com/embed/','UHUoY','aCxNg','resultSect','Vui\x20lòng\x20n','JaGlw','<iframe\x20ti','.\x20Vui\x20lòng','ykahV','ng.','tainer','SJIKX','iframeCode','i\x20lòng\x20nhậ','copy','\x20ID:\x20','3jBPMei','querySelec','</div>','\x20hoặc\x20vide','focus','lowfullscr','\x22></iframe','hép\x20vào\x20cl','forEach','scrollInto','block','\x22\x20framebor','tGOhj','dataset','ube\x20Shorts','uaqHo','DFRCi','uTube!','minxb','NlXkX','YpuQl','youtubeUrl','etdaw','OvfAf','match','o\x20hợp\x20lệ.','select','w.youtube.','stener','URL\x20không\x20','embedUrl','add','getElement','hập\x20URL\x20Yo','<iframe\x20sr','BXzLm','be\x20video\x20p','addEventLi','uRUGO','layer\x22\x20src','active','execComman','PYGvA','3178512IyDAKm','14vkhjhV','PrsQM','View','ewTSr','none','https://ww','c=\x22','height','ể\x20sao\x20chép','hợp\x20lệ!\x20Vu','VGWZQ','onRange','width','tle=\x22YouTu'];_0x5b77=function(){return _0x9ba13e;};return _0x5b77();}function convertUrl(){const _0x3b8ad7=_0x99cc74,_0x612c71={'JXASh':_0x3b8ad7(0xcf),'BXzLm':_0x3b8ad7(0x116),'UHUoY':function(_0x519b7e,_0x371486,_0x3a9147){return _0x519b7e(_0x371486,_0x3a9147);},'guGtH':_0x3b8ad7(0xae)+_0x3b8ad7(0xdb)+_0x3b8ad7(0xcb),'tGOhj':_0x3b8ad7(0x10f),'NCmXz':function(_0x360b65,_0x5e58a8){return _0x360b65(_0x5e58a8);},'SJIKX':function(_0x533fbb,_0x793354,_0xa4227f){return _0x533fbb(_0x793354,_0xa4227f);},'YpuQl':_0x3b8ad7(0xd7)+_0x3b8ad7(0xef)+_0x3b8ad7(0xb7)+_0x3b8ad7(0x113)+_0x3b8ad7(0xc8)+_0x3b8ad7(0xbd)+_0x3b8ad7(0xd3),'GQZpm':_0x3b8ad7(0xd8),'JaGlw':_0x3b8ad7(0xb6),'minxb':_0x3b8ad7(0xad)+_0x3b8ad7(0x103),'uaqHo':_0x3b8ad7(0xc4),'LqFmj':_0x3b8ad7(0x9a)+_0x3b8ad7(0xb4),'ykahV':_0x3b8ad7(0xa2),'PYGvA':_0x3b8ad7(0x108),'aCxNg':_0x3b8ad7(0x100)},_0x321354=document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0x94)]),_0x1338bd=_0x321354[_0x3b8ad7(0xf5)][_0x3b8ad7(0x98)](),_0x37acf6=document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0xdd)]);if(!_0x1338bd){_0x612c71[_0x3b8ad7(0xab)](showMessage,_0x612c71[_0x3b8ad7(0xa5)],_0x612c71[_0x3b8ad7(0xc6)]);return;}const _0x39bfa1=_0x612c71[_0x3b8ad7(0x9e)](extractVideoId,_0x1338bd);if(!_0x39bfa1){_0x612c71[_0x3b8ad7(0xb5)](showMessage,_0x612c71[_0x3b8ad7(0xce)],_0x612c71[_0x3b8ad7(0xc6)]);return;}const _0x4962bf=_0x3b8ad7(0xeb)+_0x3b8ad7(0xd5)+_0x3b8ad7(0xaa)+_0x39bfa1,_0x50522c=_0x3b8ad7(0xb0)+_0x3b8ad7(0xf3)+_0x3b8ad7(0xde)+_0x3b8ad7(0xe1)+'=\x22'+_0x4962bf+_0x3b8ad7(0x10b)+currentWidth+_0x3b8ad7(0xf9)+currentHeight+(_0x3b8ad7(0xc5)+_0x3b8ad7(0xa9)+_0x3b8ad7(0xbf)+_0x3b8ad7(0xa6)+_0x3b8ad7(0x10d)+_0x3b8ad7(0xc0)+'>');document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0xa1)])[_0x3b8ad7(0xf5)]=_0x4962bf,document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0xaf)])[_0x3b8ad7(0xf5)]=_0x50522c,document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0xcc)])[_0x3b8ad7(0x110)][_0x3b8ad7(0x95)]=_0x612c71[_0x3b8ad7(0xc9)];const _0x438b2a=document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0x93)]);_0x438b2a[_0x3b8ad7(0xa3)]=_0x3b8ad7(0xdc)+_0x3b8ad7(0xec)+_0x4962bf+_0x3b8ad7(0x10b)+Math[_0x3b8ad7(0xfa)](currentWidth,-0x17*-0x7f+-0x1e58*-0x1+-0x2769)+_0x3b8ad7(0xf9)+Math[_0x3b8ad7(0xfa)](currentHeight,0x49*0x3b+-0xb97*-0x1+-0x1ada)+(_0x3b8ad7(0xc5)+_0x3b8ad7(0xa9)+_0x3b8ad7(0xbf)+_0x3b8ad7(0xa6)+_0x3b8ad7(0x10d)+_0x3b8ad7(0xc0)+'>'),_0x612c71[_0x3b8ad7(0xab)](showMessage,_0x3b8ad7(0xf8)+_0x3b8ad7(0xfc)+_0x3b8ad7(0xfe)+_0x3b8ad7(0xb9)+_0x39bfa1,_0x612c71[_0x3b8ad7(0xb2)]),document[_0x3b8ad7(0xda)+_0x3b8ad7(0x101)](_0x612c71[_0x3b8ad7(0xcc)])[_0x3b8ad7(0xc3)+_0x3b8ad7(0xe8)]({'behavior':_0x612c71[_0x3b8ad7(0xe4)],'block':_0x612c71[_0x3b8ad7(0xac)]});}function clearAll(){const _0x2143f0=_0x99cc74,_0x264cbd={'PrsQM':_0x2143f0(0xcf),'VmCQJ':_0x2143f0(0xad)+_0x2143f0(0x103),'DFRCi':_0x2143f0(0xea),'FXaBs':_0x2143f0(0x116)};document[_0x2143f0(0xda)+_0x2143f0(0x101)](_0x264cbd[_0x2143f0(0xe7)])[_0x2143f0(0xf5)]='',document[_0x2143f0(0xda)+_0x2143f0(0x101)](_0x264cbd[_0x2143f0(0x96)])[_0x2143f0(0x110)][_0x2143f0(0x95)]=_0x264cbd[_0x2143f0(0xca)],document[_0x2143f0(0xda)+_0x2143f0(0x101)](_0x264cbd[_0x2143f0(0x11a)])[_0x2143f0(0xa3)]='',document[_0x2143f0(0xda)+_0x2143f0(0x101)](_0x264cbd[_0x2143f0(0xe7)])[_0x2143f0(0xbe)]();}function copyToClipboard(_0x130189){const _0xe50f25=_0x99cc74,_0x1bdbc8={'NcQfI':_0xe50f25(0xb8),'KKHjd':function(_0x4d62b4,_0x4e85ce,_0x3fd06b){return _0x4d62b4(_0x4e85ce,_0x3fd06b);},'NlXkX':_0xe50f25(0x9b)+_0xe50f25(0xc1)+_0xe50f25(0xa0),'ewTSr':_0xe50f25(0xa2),'uRUGO':_0xe50f25(0x115)+_0xe50f25(0xee)+_0xe50f25(0xb1)+_0xe50f25(0xf7)+_0xe50f25(0x97)+_0xe50f25(0xb3),'OvfAf':_0xe50f25(0x10f)},_0x7611b3=document[_0xe50f25(0xda)+_0xe50f25(0x101)](_0x130189);_0x7611b3[_0xe50f25(0xd4)](),_0x7611b3[_0xe50f25(0x106)+_0xe50f25(0xf1)](-0x1d*0x5b+-0x1ddd+-0x3*-0xd64,-0x2ced3+-0x1*-0x21a30+0x2*0x11da1);try{document[_0xe50f25(0xe3)+'d'](_0x1bdbc8[_0xe50f25(0x107)]),_0x1bdbc8[_0xe50f25(0x119)](showMessage,_0x1bdbc8[_0xe50f25(0xcd)],_0x1bdbc8[_0xe50f25(0xe9)]);}catch(_0x585a28){_0x1bdbc8[_0xe50f25(0x119)](showMessage,_0x1bdbc8[_0xe50f25(0xe0)],_0x1bdbc8[_0xe50f25(0xd1)]);}}function showMessage(_0x43d273,_0x56210c){const _0x51a8cd=_0x99cc74,_0x1d052e={'ZIGTT':_0x51a8cd(0x116),'Cadpj':function(_0x560135,_0x5115a8,_0x31f403){return _0x560135(_0x5115a8,_0x31f403);}},_0x1878e0=document[_0x51a8cd(0xda)+_0x51a8cd(0x101)](_0x1d052e[_0x51a8cd(0x104)]);_0x1878e0[_0x51a8cd(0xa3)]=_0x51a8cd(0x10a)+'=\x22'+_0x56210c+'\x22>'+_0x43d273+_0x51a8cd(0xbc),_0x1d052e[_0x51a8cd(0xa7)](setTimeout,()=>{const _0x543859=_0x51a8cd;_0x1878e0[_0x543859(0xa3)]='';},-0x1e14+0x7*-0x1fb+0x1*0x3f79);}window[_0x99cc74(0x10c)]=function(){const _0x240435=_0x99cc74,_0x34a923={'dNLPb':_0x240435(0xcf)};document[_0x240435(0xda)+_0x240435(0x101)](_0x34a923[_0x240435(0xa4)])[_0x240435(0xbe)]();};
    </script>
</body>
</html>