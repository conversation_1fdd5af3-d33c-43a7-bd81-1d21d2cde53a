<?php

function vts_fix_performance_issues() {
    // 1. <PERSON><PERSON>i <PERSON> database queries
    function optimize_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Tránh SELECT * 
            $query->set('fields', 'ids');
            
            // G<PERSON><PERSON><PERSON> hạn post revisions
            if ($query->is_post_type_archive()) {
                $query->set('no_found_rows', true);
            }
        }
    }
    add_action('pre_get_posts', 'optimize_queries');

    // 2. Fix memory leaks
    function fix_memory_issues() {
        // Giới hạn post revisions
        if (!defined('WP_POST_REVISIONS')) {
            define('WP_POST_REVISIONS', 5);
        }
        
        // Tăng memory limit nếu cần
        if (WP_DEBUG && !defined('WP_MEMORY_LIMIT')) {
            define('WP_MEMORY_LIMIT', '256M');
        }
    }

    // 3. Optimize assets loading
    function optimize_assets() {
        // Remove unnecessary scripts
        wp_dequeue_script('wp-embed');
        remove_action('wp_head', 'wp_generator');
        
        // Defer non-critical CSS/JS
        add_filter('style_loader_tag', function($tag) {
            return str_replace(' href', ' defer href', $tag);
        });
    }
    add_action('wp_enqueue_scripts', 'optimize_assets', 999);
}