
document.addEventListener('DOMContentLoaded', function() {
    // Header scroll effect with throttling for better performance
    let ticking = false;
    function updateHeaderOnScroll() {
      const header = document.getElementById('header');
      const scrollY = window.scrollY;
      
      if (scrollY > 10) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
      ticking = false;
    }

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateHeaderOnScroll);
        ticking = true;
      }
    }

    window.addEventListener('scroll', requestTick, { passive: true });

    // Scroll Progress Bar
    function updateScrollProgress() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrollProgress = (scrollTop / scrollHeight) * 100;
        
        let progressBar = document.getElementById('scroll-progress');
        if (!progressBar) {
            // Create progress bar if it doesn't exist
            progressBar = document.createElement('div');
            progressBar.id = 'scroll-progress';
            const headerHeight = document.querySelector('.header').offsetHeight;
            progressBar.style.cssText = `
                position: fixed;
                top: ${headerHeight}px;
                left: 0;
                width: ${scrollProgress}%;
                height: 1.5px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                z-index: 999;
                transition: width 0.1s ease;
                pointer-events: none;
            `;
            document.body.appendChild(progressBar);
        } else {
            progressBar.style.width = scrollProgress + '%';
            // Update position based on current header height
            const headerHeight = document.querySelector('.header').offsetHeight;
            progressBar.style.top = headerHeight + 'px';
        }
    }


function updateMobileMenuPosition() {
    if (navLinks) {
        const header = document.querySelector('.header');
        const headerHeight = header.offsetHeight;
        navLinks.style.top = headerHeight + 'px';
    }
}

// Cập nhật vị trí menu mỗi khi scroll
function handleScroll() {
    // Always update mobile menu position when scrolling
    updateMobileMenuPosition();
}

    // Navigation Active State
    function updateActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');
        const scrollPos = window.scrollY + 100; // Offset for header
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                // Remove active class from all nav links
                navLinks.forEach(link => {
                    link.classList.remove('active');
                });
                
                // Add active class to current section's nav link
                const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }
            }
        });
    }

    // Combined scroll handler for better performance
    let scrollTicking = false;
    function handleScroll() {
        if (!scrollTicking) {
            requestAnimationFrame(() => {
                updateScrollProgress();
                updateActiveNavigation();
                // Update mobile menu position if it's open
                if (isMenuOpen && navLinks) {
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    navLinks.style.top = headerHeight + 'px';
                }
                scrollTicking = false;
            });
            scrollTicking = true;
        }
    }

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Update menu position on resize
    window.addEventListener('resize', function() {
        if (isMenuOpen && navLinks) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            navLinks.style.top = headerHeight + 'px';
        }
    }, { passive: true });

    // Ultra-optimized Mobile menu toggle for minimal INP
    let isMenuOpen = false;
    const mobileToggle = document.getElementById('mobileToggle');
    const navLinks = document.getElementById('navLinks');
    const toggleIcon = mobileToggle.querySelector('i');
    
    // Pre-cache class names and styles
    const ACTIVE_CLASS = 'active';
    const BARS_CLASS = 'fa-bars';
    const TIMES_CLASS = 'fa-times';
    
    // Immediate visual feedback function
    function provideFeedback() {
      mobileToggle.style.transform = 'scale(0.95)';
      requestAnimationFrame(() => {
        mobileToggle.style.transform = '';
      });
    }
    
    // Ultra-fast toggle function with minimal DOM operations
    function toggleMobileMenu() {
      isMenuOpen = !isMenuOpen;
      
      // Use requestAnimationFrame for smooth updates
      requestAnimationFrame(() => {
        // Get current header height dynamically
        const headerHeight = document.querySelector('.header').offsetHeight;
        navLinks.style.top = headerHeight + 'px';
        
        if (isMenuOpen) {
          // Opening menu - direct style manipulation
          navLinks.style.cssText = `top: ${headerHeight}px; transform: translateY(0); visibility: visible;`;
          navLinks.classList.add(ACTIVE_CLASS);
          toggleIcon.style.cssText = 'transform: rotate(90deg);';
          toggleIcon.className = 'fas ' + TIMES_CLASS;
        } else {
          // Closing menu
          navLinks.style.cssText = `top: ${headerHeight}px; transform: translateY(-120%); visibility: hidden;`;
          navLinks.classList.remove(ACTIVE_CLASS);
          toggleIcon.style.cssText = 'transform: rotate(0deg);';
          toggleIcon.className = 'fas ' + BARS_CLASS;
        }
      });
    }
    
    // Multiple event listeners for maximum responsiveness
    mobileToggle.addEventListener('touchstart', function(e) {
      e.preventDefault();
      provideFeedback();
      toggleMobileMenu();
    }, { passive: false, capture: true });
    
    mobileToggle.addEventListener('mousedown', function(e) {
      e.preventDefault();
      provideFeedback();
      toggleMobileMenu();
    }, { passive: false, capture: true });
    
    // Fallback click handler
    mobileToggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
    }, { passive: false, capture: true });

    // Ultra-optimized close mobile menu when clicking on a link
    function closeMobileMenu() {
      if (!isMenuOpen) return;
      
      isMenuOpen = false;
      
      // Use requestAnimationFrame for consistent performance
      requestAnimationFrame(() => {
        const headerHeight = document.querySelector('.header').offsetHeight;
        navLinks.style.cssText = `top: ${headerHeight}px; transform: translateY(-100%); visibility: hidden;`;
        navLinks.classList.remove(ACTIVE_CLASS);
        toggleIcon.style.cssText = 'transform: rotate(0deg);';
        toggleIcon.className = 'fas ' + BARS_CLASS;
      });
    }
    
    // Use event delegation for better performance
    navLinks.addEventListener('click', function(e) {
      if (e.target.classList.contains('nav-link')) {
        closeMobileMenu();
      }
    }, { passive: true });

    // FAQ functionality
    document.querySelectorAll('.faq-question').forEach(button => {
      button.addEventListener('click', function() {
        const faqItem = this.parentElement;
        const isActive = faqItem.classList.contains('active');
        
        // Close all other FAQ items
        document.querySelectorAll('.faq-item').forEach(item => {
          item.classList.remove('active');
        });
        
        // Toggle current item
        if (!isActive) {
          faqItem.classList.add('active');
        }
      });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          const headerHeight = document.querySelector('.header').offsetHeight;
          const targetPosition = target.offsetTop - headerHeight - 20;
          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      });
    });

    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, observerOptions);

    // Observe all elements with animation classes
    document.querySelectorAll('.fade-up, .scale-up').forEach(el => {
      observer.observe(el);
    });

    // Add loading animation
    window.addEventListener('load', function() {
      document.body.classList.add('loaded');
    });

    // Add click tracking (analytics ready)
    document.querySelectorAll('.btn-primary, .btn-secondary, .btn-white, .header-cta').forEach(button => {
      button.addEventListener('click', function(e) {
        // Analytics tracking code would go here
        console.log('CTA clicked:', this.textContent.trim());
      });
    });

    // Mobile menu optimizations already integrated above

    // Critical mobile menu optimization - preload elements
    if (mobileToggle && navLinks) {
      // Force layout calculation once
      mobileToggle.offsetHeight;
      navLinks.offsetHeight;
    }

    // Exit Intent Popup functionality
    let exitIntentTriggered = false;
    let mouseLeaveTimer;

    function showExitPopup() {
        if (!exitIntentTriggered) {
            exitIntentTriggered = true;
            const popup = document.getElementById('exit-popup');
            if (popup) {
                popup.style.display = 'flex';
            }
        }
    }

    function closeExitPopup() {
        const popup = document.getElementById('exit-popup');
        if (popup) {
            popup.style.display = 'none';
        }
    }

    // Make closeExitPopup globally available
    window.closeExitPopup = closeExitPopup;

    // Exit intent detection
    document.addEventListener('mouseleave', function(e) {
        if (e.clientY <= 0 && !exitIntentTriggered) {
            clearTimeout(mouseLeaveTimer);
            mouseLeaveTimer = setTimeout(showExitPopup, 100);
        }
    });

    document.addEventListener('mouseenter', function() {
        clearTimeout(mouseLeaveTimer);
    });

    // Close popup when clicking outside
    document.getElementById('exit-popup')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeExitPopup();
        }
    });

    // Show mobile sticky CTA on mobile devices
    function checkMobileStickyCTA() {
        const mobileStickyCTA = document.querySelector('.mobile-sticky-cta');
        if (mobileStickyCTA && window.innerWidth <= 768) {
            mobileStickyCTA.style.display = 'block';
        } else if (mobileStickyCTA) {
            mobileStickyCTA.style.display = 'none';
        }
    }

    // Check on load and resize
    checkMobileStickyCTA();
    window.addEventListener('resize', checkMobileStickyCTA);
});