<svg width="1400" height="550" viewBox="0 0 1400 550" version="1.1" xmlns="http://www.w3.org/2000/svg"><title>wordpresshosting</title><defs><linearGradient x1="76.325%" y1="32.585%" x2="0%" y2="50.767%" id="a"><stop stop-color="#0B78FF" offset="0%"/><stop stop-color="#0B78FF" stop-opacity="0" offset="100%"/></linearGradient><linearGradient x1="64.536%" y1="48.368%" x2="92.445%" y2="54.23%" id="c"><stop stop-color="#02E8FD" stop-opacity=".427" offset="0%"/><stop stop-color="#01CCFB" stop-opacity="0" offset="100%"/></linearGradient><linearGradient x1="94.534%" y1="54.563%" x2="-19.989%" y2="54.756%" id="b"><stop stop-color="#01E4FC" stop-opacity="0" offset="0%"/><stop stop-color="#02EBFC" offset="48.2%"/><stop stop-color="#01E2FB" stop-opacity="0" offset="100%"/></linearGradient><filter x="-198.3%" y="-155.9%" width="510%" height="415.6%" filterUnits="objectBoundingBox" id="d"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="88.016%" y1="64.974%" x2="3.987%" y2="32.45%" id="e"><stop stop-color="#00F6FF" stop-opacity="0" offset="0%"/><stop stop-color="#02CDE7" offset="100%"/></linearGradient><filter x="-198.3%" y="-167.8%" width="510%" height="435.7%" filterUnits="objectBoundingBox" id="f"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="88.016%" y1="66.218%" x2="3.987%" y2="30.993%" id="g"><stop stop-color="#00F6FF" stop-opacity="0" offset="0%"/><stop stop-color="#02CDE7" offset="100%"/></linearGradient><filter x="-198.3%" y="-167.8%" width="510%" height="435.7%" filterUnits="objectBoundingBox" id="h"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-198.3%" y="-155.9%" width="510%" height="415.6%" filterUnits="objectBoundingBox" id="i"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="90.697%" y1="64.078%" x2="4.061%" y2="35.658%" id="j"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="12.009%" y1="37.713%" x2="92.331%" y2="39.73%" id="k"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0D7E" offset="66.066%"/><stop stop-color="#1A22BE" offset="100%"/></linearGradient><linearGradient x1="111.214%" y1="36.006%" x2=".786%" y2="16.233%" id="l"><stop stop-color="#0F0856" offset="0%"/><stop stop-color="#0B0F89" offset="100%"/></linearGradient><linearGradient x1="90.697%" y1="71.223%" x2="4.061%" y2="28.38%" id="m"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="90.697%" y1="65.863%" x2="4.061%" y2="33.84%" id="n"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="106.318%" y1="36.37%" x2="-33.549%" y2="59.164%" id="o"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><linearGradient x1="100%" y1="48.175%" x2="12.888%" y2="52.529%" id="p"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0F89" offset="100%"/></linearGradient><linearGradient x1="90.697%" y1="63.929%" x2="4.061%" y2="35.81%" id="q"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="184.449%" y1="38.873%" x2="29.026%" y2="52.395%" id="r"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0F89" offset="100%"/></linearGradient><linearGradient x1="106.318%" y1="36.515%" x2="-33.549%" y2="59.067%" id="s"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><linearGradient x1="12.009%" y1="34.08%" x2="92.331%" y2="36.694%" id="t"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0D7E" offset="66.066%"/><stop stop-color="#1A22BE" offset="100%"/></linearGradient><linearGradient x1="22.21%" y1="60.633%" x2="64.261%" y2="22.072%" id="u"><stop stop-color="#7CF1FF" offset="0%"/><stop stop-color="#01B8FF" offset="100%"/></linearGradient><linearGradient x1="50%" y1="0%" x2="50%" y2="116.858%" id="v"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="0%" x2="50%" y2="116.858%" id="w"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="22.836%" x2="50%" y2="147.903%" id="x"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="34.798%" x2="50%" y2="147.903%" id="y"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="64.293%" x2="50%" y2="147.903%" id="z"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="47.628%" y1="19.483%" x2="57.258%" y2="84.356%" id="B"><stop stop-color="#02E8FD" stop-opacity="0" offset="0%"/><stop stop-color="#02E5FD" stop-opacity=".385" offset="30.491%"/><stop stop-color="#01CCFB" stop-opacity="0" offset="100%"/></linearGradient><linearGradient x1="57.884%" y1="15.43%" x2="50%" y2="81.49%" id="A"><stop stop-color="#01E4FC" stop-opacity="0" offset="0%"/><stop stop-color="#02EBFC" offset="23.649%"/><stop stop-color="#01E2FB" stop-opacity="0" offset="100%"/></linearGradient><filter x="-198.3%" y="-155.9%" width="510%" height="415.6%" filterUnits="objectBoundingBox" id="C"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="106.318%" y1="36.897%" x2="-33.549%" y2="58.811%" id="D"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><linearGradient x1="63.569%" y1="90.616%" x2="34.683%" y2="8.624%" id="E"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="182.646%" y1="242.274%" x2="34.683%" y2="8.624%" id="F"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><filter x="-198.3%" y="-155.9%" width="510%" height="415.6%" filterUnits="objectBoundingBox" id="G"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="22.226%" y1="63.486%" x2="112.162%" y2="23.849%" id="H"><stop stop-color="#00F6FF" stop-opacity="0" offset="0%"/><stop stop-color="#02CDE7" offset="100%"/></linearGradient><filter x="-198.3%" y="-167.8%" width="510%" height="435.7%" filterUnits="objectBoundingBox" id="I"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-198.3%" y="-167.8%" width="510%" height="435.7%" filterUnits="objectBoundingBox" id="J"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="98.052%" y1="46.678%" x2="22.576%" y2="53.57%" id="L"><stop stop-color="#02E8FD" stop-opacity="0" offset="0%"/><stop stop-color="#02E5FD" stop-opacity=".385" offset="37.787%"/><stop stop-color="#01CCFB" stop-opacity="0" offset="100%"/><stop stop-color="#01CCFB" stop-opacity="0" offset="100%"/></linearGradient><linearGradient x1="98.776%" y1="47.049%" x2="16.932%" y2="55.328%" id="K"><stop stop-color="#01E4FC" stop-opacity="0" offset="0%"/><stop stop-color="#02EBFC" offset="35.116%"/><stop stop-color="#01E2FB" stop-opacity="0" offset="100%"/></linearGradient><linearGradient x1="77.753%" y1="40.356%" x2="19.615%" y2="56.225%" id="M"><stop stop-color="#11D1FF" offset="0%"/><stop stop-color="#4FDCFF" stop-opacity="0" offset="100%"/></linearGradient><filter x="-198.3%" y="-155.9%" width="510%" height="415.6%" filterUnits="objectBoundingBox" id="N"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-198.3%" y="-155.9%" width="510%" height="415.6%" filterUnits="objectBoundingBox" id="O"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-198.3%" y="-167.8%" width="510%" height="435.7%" filterUnits="objectBoundingBox" id="P"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="50%" y1="4.674%" x2="50%" y2="123.773%" id="Q"><stop stop-color="#04F1FC" stop-opacity="0" offset="0%"/><stop stop-color="#03E1F9" offset="100%"/></linearGradient><linearGradient x1="170.246%" y1="21.945%" x2="-47.697%" y2="68.197%" id="R"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#486BD7" offset="100%"/></linearGradient><linearGradient x1="170.246%" y1="22.029%" x2="-47.697%" y2="68.142%" id="S"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#486BD7" offset="100%"/></linearGradient><linearGradient x1="92.296%" y1="5.408%" x2="-106.22%" y2="196.022%" id="T"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#486BD7" offset="100%"/></linearGradient><linearGradient x1="27.942%" y1="78.363%" x2="61.32%" y2="-24.499%" id="U"><stop stop-color="#7CF1FF" offset="0%"/><stop stop-color="#01B8FF" offset="100%"/></linearGradient><linearGradient x1="-21.272%" y1="35.073%" x2="112.942%" y2="35.073%" id="V"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0D7E" offset="63.736%"/><stop stop-color="#1A22BE" offset="100%"/></linearGradient><linearGradient x1="55.628%" y1="36.006%" x2="42.612%" y2="19.538%" id="W"><stop stop-color="#0F0856" offset="0%"/><stop stop-color="#0B0F89" offset="100%"/></linearGradient><linearGradient x1="106.318%" y1="36.287%" x2="-33.549%" y2="59.22%" id="X"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><linearGradient x1="100%" y1="48.163%" x2="12.888%" y2="52.544%" id="Y"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0F89" offset="100%"/></linearGradient><linearGradient x1="90.697%" y1="64.015%" x2="4.061%" y2="35.723%" id="Z"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="184.449%" y1="38.873%" x2="29.026%" y2="52.395%" id="aa"><stop stop-color="#1A22BE" offset="0%"/><stop stop-color="#0B0F89" offset="100%"/></linearGradient><linearGradient x1="50%" y1="0%" x2="50%" y2="116.858%" id="ab"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="0%" x2="50%" y2="116.858%" id="ac"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="80.197%" x2="50%" y2="-196.544%" id="ad"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="54.449%" y1="10.679%" x2="43.4%" y2="76.439%" id="ae"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><linearGradient x1="106.318%" y1="45.966%" x2="-33.549%" y2="52.712%" id="af"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><linearGradient x1="90.697%" y1="79.47%" x2="4.061%" y2="19.978%" id="ag"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="68.222%" y1="90.616%" x2="29.43%" y2="8.624%" id="ah"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="68.222%" y1="90.616%" x2="29.43%" y2="8.624%" id="ai"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="31.023%" y1="78.363%" x2="59.738%" y2="-24.499%" id="aj"><stop stop-color="#7CF1FF" offset="0%"/><stop stop-color="#01B8FF" offset="100%"/></linearGradient><linearGradient x1="50%" y1="0%" x2="50%" y2="116.858%" id="ak"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="0%" x2="50%" y2="116.858%" id="al"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="22.836%" x2="50%" y2="147.903%" id="am"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="34.798%" x2="50%" y2="147.903%" id="an"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><linearGradient x1="50%" y1="64.293%" x2="50%" y2="147.903%" id="ao"><stop stop-color="#6BE9FF" stop-opacity="0" offset="0%"/><stop stop-color="#01F3FA" offset="100%"/></linearGradient><filter x="-121.2%" y="-99.2%" width="346.3%" height="302.3%" filterUnits="objectBoundingBox" id="ap"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><linearGradient x1="90.697%" y1="63.535%" x2="4.061%" y2="36.212%" id="aq"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="182.646%" y1="242.274%" x2="34.683%" y2="8.624%" id="ar"><stop stop-color="#00C2FA" offset="0%"/><stop stop-color="#0B78FF" offset="100%"/></linearGradient><linearGradient x1="68.778%" y1="10.679%" x2="22.143%" y2="76.439%" id="as"><stop stop-color="#188EFF" offset="0%"/><stop stop-color="#1B37D6" offset="100%"/></linearGradient><filter x="-121.2%" y="-99.2%" width="346.3%" height="302.3%" filterUnits="objectBoundingBox" id="at"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-121.2%" y="-99.2%" width="346.3%" height="302.3%" filterUnits="objectBoundingBox" id="au"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-121.2%" y="-99.2%" width="346.3%" height="302.3%" filterUnits="objectBoundingBox" id="av"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-121.2%" y="-99.2%" width="346.3%" height="302.3%" filterUnits="objectBoundingBox" id="aw"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter><filter x="-121.2%" y="-99.2%" width="346.3%" height="302.3%" filterUnits="objectBoundingBox" id="ax"><feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.918817935 0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M491.96 464.04l-61.31-35.555-60.444 35.054 60.445-35.054 61.309 35.555-60.446 35.054 60.446-35.054.431.25-.431-.25 61.308-35.555.432.25-.432-.25 61.309-35.555.432.25-.432-.25-61.309 35.555-61.308-35.555-61.31 35.555-61.308-35.555-60.445 35.054 60.445-35.054 61.309 35.555 61.309-35.555-61.31-35.555-61.308 35.555-61.308-35.555 61.308-35.555 61.309 35.555 61.309-35.555-61.31-35.555 61.31-35.555 61.308 35.555 61.309-35.555 61.309 35.555-61.309 35.555-61.309-35.555-61.308 35.555-61.31-35.555-61.308 35.555 61.309 35.555-61.309 35.555-61.308-35.555-60.446 35.054 60.446-35.054-61.31-35.555 61.31-35.555 61.308 35.555 61.309-35.555-61.309-35.555 61.309-35.555 61.309 35.555-61.31 35.555-61.308-35.555-61.308 35.555 61.308 35.555-61.308 35.555-61.31-35.555-60.444 35.054 60.445-35.054-61.309-35.555-60.445 35.054 60.445-35.054-61.309-35.555-60.445 35.054 60.445-35.054 61.31 35.555 61.308-35.555 61.309 35.555 61.308-35.555-61.308-35.555-61.31 35.555 61.31 35.555-61.31 35.555-61.308-35.555 61.309-35.555-61.309-35.555-61.309 35.555-62.172-36.056 61.309-35.555 62.172 36.056 61.309-35.555 61.309 35.555 61.308-35.555-61.308-35.555-61.31 35.555-62.171-36.056 61.308-35.555-.431-.25.431.25 61.309-35.555 62.172 36.056-61.308 35.555-62.173-36.056 61.309-35.555-.432-.25.432.25 61.309-35.555-.432-.25.432.25-61.309 35.555 62.172 36.056 61.309-35.555 61.309 35.555 62.172-36.056 61.309 35.555.863-.5-.863.5-61.31-35.555-62.171 36.056 61.308 35.555L491.96 179.6l61.308 35.555 61.309-35.555-61.309-35.555L491.96 179.6l-61.31-35.555-61.308 35.555-61.308-35.555-61.31 35.555-62.171-36.056-61.31 35.555 62.173 36.056 61.309-35.555 61.309 35.555 61.308-35.555 61.309 35.555L491.96 179.6l-61.31-35.555-61.308 35.555 61.309 35.555L491.96 179.6l61.308 35.555-61.308 35.555-61.31-35.555-61.308 35.555-61.308-35.555-61.31 35.555-61.308-35.555-61.309 35.555-62.172-36.056L1.49 249.708l60.445-35.054-.432-.25.432.25 61.309-35.555-.432-.25.432.25 61.309-35.555-.432-.25.432.25 61.308-35.555 62.173 36.056 61.308-35.555 61.309 35.555 61.309-35.555-61.31-35.555 62.173-36.056 61.309 35.555.863-.5-.863.5-61.309-35.555-62.172 36.056-62.172-36.056L430.65.823l-.432-.25.432.25.863-.5-.863.5 62.172 36.056.864-.5-.864.5L430.651.823l-62.172 36.056 62.172 36.056-61.309 35.555 61.309 35.555 61.309-35.555 61.308 35.555 62.173-36.056 61.308 35.555.864-.5-.864.5-61.308-35.555-62.173 36.056 61.309 35.555-61.309 35.555 61.309 35.555-61.309 35.555-61.308-35.555 61.308-35.555 61.309 35.555 61.309-35.555-61.309-35.555 62.172-36.056 61.309 35.555.863-.5-.863.5-61.309-35.555-62.172 36.056 61.309 35.555 62.172-36.056 61.309 35.555.863-.5-.863.5-61.31-35.555-62.171 36.056 61.308 35.555 62.173-36.056 61.308 35.555-62.172 36.056.432.25-.432-.25-61.309 35.555.432.25-.432-.25-61.308 35.555.431.25-.431-.25-61.309 35.555-61.309-35.555 61.309-35.555 61.309 35.555 61.308-35.555-61.308-35.555-61.309 35.555 61.309 35.555-61.309 35.555-61.309-35.555-61.308 35.555-61.31-35.555 61.31-35.555 61.308 35.555 61.309-35.555-61.309-35.555-61.308 35.555 61.308 35.555-61.308 35.555 61.308 35.555-61.308 35.555zm122.617-213.33l61.309-35.555 61.308 35.555-61.308 35.555 61.308 35.555 61.31-35.555-61.31-35.555 62.173-36.056 61.308 35.555.864-.5-.864.5.432.25-.432-.25-62.172 36.056-61.309-35.555-61.308 35.555-61.309-35.555zM.195 249.959l430.888 249.886L.195 249.96z" stroke="url(#a)" stroke-width=".722" opacity=".4" transform="translate(538 50)"/><g fill-rule="nonzero"><path d="M210.476 61.305L113.81 5.45a35.138 35.138 0 0 0-35.06-.058L1.153 49.894 7.57 61.082l77.6-44.501a22.238 22.238 0 0 1 22.187.036l96.665 55.854 6.454-11.166z" stroke="url(#b)" stroke-width="1.444" fill="url(#c)" transform="translate(922.715 141.395)"/><g filter="url(#d)" transform="translate(1018.728 146.083)"><path fill="#00F6FF" d="M2.509 5.913L0 4.463l2.509-1.447 2.509 1.448z"/><path fill="#03E2F9" d="M5.018 4.464L2.509 3.016V.119l2.509 1.448z"/><path fill="#69E0FF" d="M2.509 3.016L0 4.464V1.567L2.509.119z"/></g><path fill="url(#e)" d="M25.275 1.544v2.897l-22.55 12.69v-2.897L.216 12.785 22.766.096z" transform="translate(968.215 157.256)"/><g filter="url(#f)" transform="translate(968.215 168.41)"><path fill="#00F6FF" d="M5.234 4.529L2.725 5.977V3.08l2.509-1.448z"/><path fill="#03E2F9" d="M2.725 5.977L.216 4.529V1.632L2.725 3.08z"/><path fill="#69E0FF" d="M2.725 3.08L.216 1.632 2.725.183l2.509 1.449z"/></g><path fill="url(#g)" d="M24.822 16.124l-2.509 1.448L.047 4.39l2.509-1.448V.045l22.266 13.182z" transform="translate(1061.775 167.064)"/><g filter="url(#h)" transform="translate(1061.775 167.064)"><path fill="#00F6FF" d="M2.556 5.839L.047 4.39l2.509-1.448L5.065 4.39z"/><path fill="#03E2F9" d="M.047 4.39V1.494L2.556.045v2.897z"/><path fill="#69E0FF" d="M2.49 3.019V.045l2.575 1.487v2.973z"/></g><g filter="url(#i)" transform="translate(1095.016 186.144)"><path fill="#00F6FF" d="M2.509 5.913L0 4.463l2.509-1.447 2.509 1.448z"/><path fill="#03E2F9" d="M5.018 4.464L2.509 3.016V.119l2.509 1.448z"/><path fill="#69E0FF" d="M2.509 3.016L0 4.464V1.567L2.509.119z"/></g><path d="M928.513 213.89l-.72-1.25 85.882-49.416a12.274 12.274 0 0 1 12.268.015l94.71 54.794-.724 1.25-94.71-54.795a10.83 10.83 0 0 0-10.824-.013l-85.882 49.415z" fill="#01E5FB"/><path d="M927.716 224.247l-.72-1.25 89.493-51.494a5.054 5.054 0 0 1 5.051.006l98.316 56.88-.723 1.25-98.316-56.88a3.61 3.61 0 0 0-3.608-.004l-89.493 51.492z" fill="#01E5FB"/></g><path d="M896.935 152.98l124.377 73.89c1.252.733 1.774 2.554 1.166 4.065-.247.614-.657 1.11-1.166 1.408l-123.307 72.273c-3.242 1.9-7.027 1.9-10.27 0l-125.982-72.983c-1.252-.733-1.774-2.553-1.166-4.065.246-.614.657-1.11 1.166-1.408l124.912-73.18c3.242-1.901 7.027-1.901 10.27 0z" fill="#0D054C" opacity=".3"/><path d="M887.61 165.174l-105.334 61.71c-.357.21-.652.565-.83 1.011-.45 1.116-.064 2.458.829 2.982l106.238 61.544c2.622 1.536 5.674 1.536 8.295 0l103.98-60.945c.358-.21.652-.565.832-1.01.448-1.116.063-2.459-.833-2.984l-104.883-62.309c-2.62-1.536-5.672-1.535-8.294 0zm8.66-.623l104.884 62.31c1.217.713 1.712 2.44 1.136 3.874-.238.59-.636 1.07-1.136 1.364l-103.98 60.945c-2.847 1.668-6.179 1.668-9.024 0L781.91 231.5c-1.217-.713-1.712-2.44-1.136-3.874.237-.59.636-1.071 1.136-1.364l105.334-61.71c2.847-1.67 6.179-1.67 9.025 0z" fill="#01E1FA" fill-rule="nonzero"/><path d="M135.331 24.195l87.635 52.062c.882.517 1.25 1.8.821 2.864a2 2 0 0 1-.821.992l-86.88 50.922c-2.285 1.34-4.952 1.34-7.236 0L40.084 79.613c-.882-.517-1.25-1.8-.821-2.864a2 2 0 0 1 .821-.992l88.011-51.562c2.285-1.34 4.952-1.34 7.236 0z" fill="url(#j)" transform="translate(760.195 151.182)"/><path d="M94.906 38.606l63.142 7.967v29.754c.092.415.065.87-.102 1.286-.15.374-.4.676-.71.857l-75.041 43.983c-1.973 1.157-4.277 1.157-6.25 0L.904 78.47c-.474-.278-.776-.81-.848-1.395H.04V46.573h63.195l12.711-7.45c1.973-1.157 4.277-1.157 6.25 0l12.71-.517z" fill="url(#k)" transform="translate(813.289 151.675)"/><path d="M828.028 217.066a1.88 1.88 0 0 1 1.055 1.688v10.64a.627.627 0 0 1-.902.564l-1.959-.957a1.88 1.88 0 0 1-1.055-1.69v-10.64a.627.627 0 0 1 .902-.563l1.959.958zM821.972 213.73a1.88 1.88 0 0 1 1.055 1.69v10.64a.627.627 0 0 1-.902.563l-1.959-.957a1.88 1.88 0 0 1-1.055-1.69v-10.64a.627.627 0 0 1 .902-.563l1.959.957z" fill="#0B0F89"/><path d="M94.536 46.115h63.143v29.754c.091.415.065.87-.103 1.286-.15.374-.4.676-.71.857l-75.04 43.983c-1.974 1.157-4.278 1.157-6.25 0v-83.33c1.972-1.157 4.276-1.157 6.25 0l12.71 7.45z" fill="url(#l)" transform="translate(813.289 151.675)"/><path d="M927.194 232.625l30.758-18.093c.284-.168.627-.026.766.316a.808.808 0 0 1 .059.303v2.815c0 .79-.375 1.512-.965 1.86l-30.758 18.092c-.284.167-.627.026-.766-.317a.808.808 0 0 1-.058-.302v-2.816c0-.79.374-1.511.964-1.858z" fill="#0B0F89"/><path d="M115.43 81.954l30.757-18.093c.285-.167.628-.026.767.317a.808.808 0 0 1 .058.302v2.816c0 .79-.374 1.511-.964 1.858l-30.759 18.093c-.284.168-.627.026-.766-.316a.808.808 0 0 1-.058-.303v-2.815c0-.79.374-1.512.964-1.86z" fill="url(#m)" transform="translate(813.289 151.675)"/><path d="M115.43 90.717l30.757-18.093c.285-.167.628-.025.767.317a.808.808 0 0 1 .058.303v-.372c0 .79-.374 1.512-.964 1.859l-30.759 18.093c-.284.167-.627.025-.766-.317a.808.808 0 0 1-.058-.302v.371c0-.79.374-1.512.964-1.859z" fill="url(#n)" transform="translate(813.289 151.675)"/><path d="M895.484 152.849l75.041 43.983c.762.446 1.08 1.554.71 2.474-.15.374-.4.676-.71.857l-75.041 43.983c-1.973 1.157-4.277 1.157-6.25 0l-75.041-43.983c-.762-.446-1.08-1.554-.71-2.474.15-.374.4-.676.71-.857l75.041-43.983c1.973-1.157 4.277-1.157 6.25 0z" fill="#0B0F89"/><path d="M82.136 1.132l72.795 43.246c.732.43 1.038 1.495.682 2.38a1.66 1.66 0 0 1-.682.823l-72.168 42.3c-1.898 1.112-4.113 1.112-6.01 0L3.017 47.165c-.733-.43-1.038-1.495-.683-2.38.145-.359.385-.65.683-.824l73.108-42.83c1.897-1.113 4.113-1.113 6.01 0z" fill="url(#o)" transform="translate(813.289 151.675)"/><path d="M894.428 165.92c-1.31-.767-2.83-.767-4.138 0l-54.066 31.689a.886.886 0 0 0-.359.44c-.202.504-.028 1.11.36 1.337l54.065 31.689c1.309.767 2.829.767 4.138 0l54.066-31.689a.886.886 0 0 0 .359-.44c.202-.504.028-1.11-.36-1.337l-54.065-31.689z" stroke="#00BDFA" stroke-width=".722"/><path d="M80.813 21.842l41.86 24.412c.426.248.603.863.397 1.373a.96.96 0 0 1-.396.476l-41.86 24.412a3.397 3.397 0 0 1-3.487 0l-41.86-24.412c-.426-.248-.603-.863-.397-1.373a.96.96 0 0 1 .396-.476l41.86-24.412a3.397 3.397 0 0 1 3.487 0z" fill="url(#p)" transform="translate(813.289 151.675)"/><path d="M80.813 19.452l41.86 24.412c.426.248.603.863.397 1.373a.96.96 0 0 1-.396.476l-41.86 24.412a3.397 3.397 0 0 1-3.487 0l-41.86-24.412c-.426-.248-.603-.863-.397-1.373a.96.96 0 0 1 .396-.476l41.86-24.412a3.397 3.397 0 0 1 3.487 0z" fill="url(#q)" transform="translate(813.289 151.675)"/><path d="M93.632 56.388l1.597.808c.159.093.225.322.148.513a.358.358 0 0 1-.148.178l-15.635 9.118c-.41.24-.89.24-1.302 0l-1.597-.932c-.16-.093-.225-.198-.148-.39a.358.358 0 0 1 .148-.177l15.635-9.118c.41-.24.89-.24 1.302 0z" fill="url(#r)" transform="translate(813.289 151.675)"/><g><path d="M94.906 38.606l63.142 7.967v29.754c.092.415.065.87-.102 1.286-.15.374-.4.676-.71.857l-75.041 43.983c-1.973 1.157-4.277 1.157-6.25 0L.904 78.47c-.474-.278-.776-.81-.848-1.395H.04V46.573h63.195l12.711-7.45c1.973-1.157 4.277-1.157 6.25 0l12.71-.517z" fill="url(#k)" transform="translate(813.289 103.681)"/><path d="M828.028 169.072a1.88 1.88 0 0 1 1.055 1.688v10.64a.627.627 0 0 1-.902.564l-1.959-.957a1.88 1.88 0 0 1-1.055-1.69v-10.64a.627.627 0 0 1 .902-.563l1.959.958zM821.972 165.736a1.88 1.88 0 0 1 1.055 1.69v10.64a.627.627 0 0 1-.902.563l-1.959-.957a1.88 1.88 0 0 1-1.055-1.69v-10.64a.627.627 0 0 1 .902-.563l1.959.957z" fill="#0B0F89"/><path d="M94.536 46.115h63.143v29.754c.091.415.065.87-.103 1.286-.15.374-.4.676-.71.857l-75.04 43.983c-1.974 1.157-4.278 1.157-6.25 0v-83.33c1.972-1.157 4.276-1.157 6.25 0l12.71 7.45z" fill="url(#l)" transform="translate(813.289 103.681)"/><path d="M927.194 184.63l30.758-18.092c.284-.168.627-.026.766.316a.808.808 0 0 1 .059.303v2.815c0 .79-.375 1.512-.965 1.86l-30.758 18.092c-.284.167-.627.026-.766-.317a.808.808 0 0 1-.058-.302v-2.816c0-.79.374-1.511.964-1.858z" fill="#0B0F89"/><path d="M115.43 81.954l30.757-18.093c.285-.167.628-.026.767.317a.808.808 0 0 1 .058.302v2.816c0 .79-.374 1.511-.964 1.858l-30.759 18.093c-.284.168-.627.026-.766-.316a.808.808 0 0 1-.058-.303v-2.815c0-.79.374-1.512.964-1.86z" fill="url(#m)" transform="translate(813.289 103.681)"/><path d="M115.43 90.717l30.757-18.093c.285-.167.628-.025.767.317a.808.808 0 0 1 .058.303v-.372c0 .79-.374 1.512-.964 1.859l-30.759 18.093c-.284.167-.627.025-.766-.317a.808.808 0 0 1-.058-.302v.371c0-.79.374-1.512.964-1.859z" fill="url(#n)" transform="translate(813.289 103.681)"/><path d="M895.484 104.855l75.041 43.983c.762.446 1.08 1.554.71 2.474-.15.374-.4.676-.71.857l-75.041 43.983c-1.973 1.157-4.277 1.157-6.25 0l-75.041-43.983c-.762-.446-1.08-1.554-.71-2.474.15-.374.4-.676.71-.857l75.041-43.983c1.973-1.157 4.277-1.157 6.25 0z" fill="#0B0F89"/><path d="M82.136 1.132l72.795 43.246c.732.43 1.038 1.495.682 2.38a1.66 1.66 0 0 1-.682.823l-72.168 42.3c-1.898 1.112-4.113 1.112-6.01 0L3.017 47.165c-.733-.43-1.038-1.495-.683-2.38.145-.359.385-.65.683-.824l73.108-42.83c1.897-1.113 4.113-1.113 6.01 0z" fill="url(#o)" transform="translate(813.289 103.681)"/><path d="M894.245 118.237c-1.196-.7-2.576-.7-3.772 0l-54.066 31.69a.53.53 0 0 0-.207.263c-.138.344-.02.758.207.89l54.066 31.69c1.196.7 2.576.7 3.772 0l54.066-31.69a.53.53 0 0 0 .207-.263c.138-.344.02-.758-.207-.89l-54.066-31.69zm.365-.622l54.066 31.688c.549.322.778 1.12.511 1.783-.108.27-.288.487-.511.618l-54.066 31.688c-1.421.834-3.08.834-4.502 0l-54.066-31.688c-.55-.322-.778-1.12-.512-1.783.109-.27.289-.487.512-.618l54.066-31.688c1.421-.834 3.08-.834 4.502 0z" fill="#00BDFA" fill-rule="nonzero"/><path d="M80.813 21.842l41.86 24.412c.426.248.603.863.397 1.373a.96.96 0 0 1-.396.476l-41.86 24.412a3.397 3.397 0 0 1-3.487 0l-41.86-24.412c-.426-.248-.603-.863-.397-1.373a.96.96 0 0 1 .396-.476l41.86-24.412a3.397 3.397 0 0 1 3.487 0z" fill="url(#p)" transform="translate(813.289 103.681)"/><path d="M80.813 19.452l41.86 24.412c.426.248.603.863.397 1.373a.96.96 0 0 1-.396.476l-41.86 24.412a3.397 3.397 0 0 1-3.487 0l-41.86-24.412c-.426-.248-.603-.863-.397-1.373a.96.96 0 0 1 .396-.476l41.86-24.412a3.397 3.397 0 0 1 3.487 0z" fill="url(#q)" transform="translate(813.289 103.681)"/><path d="M93.632 56.388l1.597.808c.159.093.225.322.148.513a.358.358 0 0 1-.148.178l-15.635 9.118c-.41.24-.89.24-1.302 0l-1.597-.932c-.16-.093-.225-.198-.148-.39a.358.358 0 0 1 .148-.177l15.635-9.118c.41-.24.89-.24 1.302 0z" fill="url(#r)" transform="translate(813.289 103.681)"/></g><g><path d="M94.906 38.487l63.142 7.967v29.755c.092.414.065.869-.102 1.285-.15.374-.4.676-.71.857l-75.041 43.983c-1.973 1.157-4.277 1.157-6.25 0L.904 78.351c-.474-.277-.776-.81-.848-1.395H.04V46.454h63.195l12.711-7.45c1.973-1.157 4.277-1.157 6.25 0l12.71-.517z" fill="url(#k)" transform="translate(813.289 56)"/><path d="M828.028 121.272a1.88 1.88 0 0 1 1.055 1.689v10.64a.627.627 0 0 1-.902.563l-1.959-.957a1.88 1.88 0 0 1-1.055-1.689v-10.64a.627.627 0 0 1 .902-.563l1.959.957zM821.972 117.937a1.88 1.88 0 0 1 1.055 1.688v10.641a.627.627 0 0 1-.902.563l-1.959-.957a1.88 1.88 0 0 1-1.055-1.689v-10.64a.627.627 0 0 1 .902-.563l1.959.957z" fill="#0B0F89"/><path d="M94.536 45.996h63.143V75.75c.091.414.065.869-.103 1.285-.15.374-.4.676-.71.857l-75.04 43.983c-1.974 1.157-4.278 1.157-6.25 0v-83.33c1.972-1.157 4.276-1.157 6.25 0l12.71 7.45z" fill="url(#l)" transform="translate(813.289 56)"/><path d="M927.194 136.83l30.758-18.092c.284-.167.627-.026.766.317a.808.808 0 0 1 .059.302v2.816c0 .79-.375 1.511-.965 1.858l-30.758 18.093c-.284.168-.627.026-.766-.316a.808.808 0 0 1-.058-.303v-2.815c0-.79.374-1.512.964-1.86z" fill="#0B0F89"/><path d="M115.43 81.835l30.757-18.093c.285-.167.628-.025.767.317a.808.808 0 0 1 .058.303v2.815c0 .79-.374 1.511-.964 1.858L115.289 87.13c-.284.167-.627.025-.766-.317a.808.808 0 0 1-.058-.303v-2.815c0-.79.374-1.512.964-1.859z" fill="url(#m)" transform="translate(813.289 56)"/><path d="M115.43 90.599l30.757-18.094c.285-.167.628-.025.767.317a.808.808 0 0 1 .058.303v-.372c0 .79-.374 1.512-.964 1.86l-30.759 18.092c-.284.168-.627.026-.766-.317a.808.808 0 0 1-.058-.302v.371c0-.79.374-1.511.964-1.858z" fill="url(#n)" transform="translate(813.289 56)"/><path d="M895.484 57.055l75.041 43.983c.762.447 1.08 1.554.71 2.474-.15.374-.4.676-.71.857l-75.041 43.983c-1.973 1.157-4.277 1.157-6.25 0l-75.041-43.983c-.762-.446-1.08-1.554-.71-2.474.15-.374.4-.675.71-.857l75.041-43.983c1.973-1.157 4.277-1.157 6.25 0z" fill="#0B0F89"/><path d="M82.136 1.013l72.795 43.246c.732.43 1.038 1.495.682 2.38a1.66 1.66 0 0 1-.682.824L82.763 89.762c-1.898 1.112-4.113 1.112-6.01 0L3.017 47.047c-.733-.43-1.038-1.495-.683-2.38.145-.359.385-.649.683-.824l73.108-42.83c1.897-1.112 4.113-1.112 6.01 0z" fill="url(#o)" transform="translate(813.289 56)"/><path d="M894.245 70.438c-1.196-.702-2.576-.702-3.772 0l-54.066 31.688a.53.53 0 0 0-.207.264c-.138.345-.02.758.207.89l54.066 31.69c1.196.7 2.576.7 3.772 0l54.066-31.69a.53.53 0 0 0 .207-.263c.138-.344.02-.758-.207-.89l-54.066-31.69zm.365-.623l54.066 31.689c.549.321.778 1.12.511 1.782-.108.27-.288.487-.511.618l-54.066 31.689c-1.421.833-3.08.833-4.502 0l-54.066-31.69c-.55-.321-.778-1.12-.512-1.782.109-.27.289-.487.512-.617l54.066-31.69c1.421-.832 3.08-.832 4.502 0z" fill="#00BDFA" fill-rule="nonzero"/><path d="M80.718 20.051l41.86 24.412c.425.248.602.862.396 1.373a.96.96 0 0 1-.396.476l-41.86 24.411a3.397 3.397 0 0 1-3.487 0l-41.86-24.411c-.425-.248-.603-.863-.396-1.374a.96.96 0 0 1 .396-.475L77.23 20.05a3.397 3.397 0 0 1 3.487 0z" fill="url(#s)" style="mix-blend-mode:multiply" opacity=".3" transform="translate(813.289 56)"/><path d="M33.51 26.365v.234h-1.594V25.1a.588.588 0 0 1 0-.22v-3.061h1.25L39.11 7.42c.21-.508 1.054-.81 1.885-.674l5.189.851c.537.088.962.345 1.113.674l7.289 12.886v3.117c0 .245-.153.484-.44.662l-10.251 6.391c-.598.373-1.58.373-2.195 0l-8.19-4.962zM3.264 22.837H3.26v-2.889c-.753.056-1.487-.236-1.7-.7L.102 16.072c-.056-.122-.038-.236-.048-.369-.007-.088-.007-1.14 0-3.154l18.918-8.924c.386-.24.955-.335 1.493-.246l15.424-.047v3.22c0 .11.007.222-.039.334l-3.186 7.717c-.136.329-.548.586-1.082.674L5.127 19.65h.525v2.06l11.253-1.86c.533-.088 1.107.006 1.504.247l19.04 11.535v-1.358h2.39v3.187h-.012c.006.244-.14.489-.44.676l-3.73 2.325c-.386.24-.955.335-1.492.247L8.445 32.49c-.537-.088-.961-.345-1.112-.674l-4.01-8.73a.624.624 0 0 1-.058-.25z" fill="url(#t)" transform="translate(864.774 79.478)"/><path d="M41.7 28.14l-9.324-5.649c-.398-.24-.56-.592-.424-.92L39.11 4.233c.21-.509 1.054-.81 1.885-.675l5.189.852c.537.088.962.345 1.113.674l7.23 15.744c.151.329.006.68-.38.92L43.895 28.14c-.598.373-1.58.373-2.195 0zm-7.535 5.382l-25.72-4.219c-.537-.088-.961-.345-1.112-.674l-4.01-8.73c-.233-.509.246-1.031 1.07-1.168l12.512-2.068c.533-.088 1.107.006 1.504.247l20.948 12.691c.614.373.628.976.03 1.349l-3.73 2.325c-.386.24-.955.335-1.492.247zM1.56 16.062L.102 12.884c-.15-.329-.006-.68.38-.92L18.973.438c.386-.241.955-.335 1.493-.247l14.26 2.339c.832.136 1.335.66 1.125 1.168l-3.186 7.717c-.136.33-.548.586-1.082.674L3.477 16.736c-.824.136-1.682-.166-1.916-.675z" fill="url(#u)" fill-rule="nonzero" transform="translate(864.774 79.478)"/></g><g opacity=".5" fill-rule="nonzero"><path d="M183.342 68.818c.282 51.842.282 78.117 0 78.827-.173.433-.46.783-.814.993L96.47 199.559c-2.262 1.34-4.904 1.34-7.167 0L1.38 148.137c-.873-.517-1.238-1.8-.814-2.864.115-.289.115-25.773 0-76.455h182.776z" fill="url(#v)" transform="translate(799.691 82.458)"/><path d="M89.304 68.818V199.56L1.38 148.137c-.873-.517-1.238-1.8-.814-2.864.115-.289.115-25.773 0-76.455h88.738z" fill="url(#w)" transform="translate(799.691 82.458)"/><path fill="url(#x)" d="M0 0l11.155 6.373V158.54L0 152.166z" transform="translate(808.797 82.458)"/><path fill="url(#y)" d="M29.518 17.679l11.155 6.373v152.167l-11.155-6.374z" transform="translate(808.797 82.458)"/><path fill="url(#z)" d="M58.999 35.206l11.155 6.373v152.167l-11.155-6.374z" transform="translate(808.797 82.458)"/><path fill="url(#x)" d="M0 0l11.155 6.373V158.54L0 152.166z" transform="matrix(-1 0 0 1 974.3 83.64)"/><path fill="url(#y)" d="M29.518 17.679l11.155 6.373v152.167l-11.155-6.374z" transform="matrix(-1 0 0 1 974.3 83.64)"/><path fill="url(#z)" d="M58.999 35.206l11.155 6.373v152.167l-11.155-6.374z" transform="matrix(-1 0 0 1 974.3 83.64)"/></g><g><path d="M92.28 10.99l56.06 33.406a2.315 2.315 0 0 1-.035 3.998L1.387 132.497l5.617 9.809 146.918-84.103a13.62 13.62 0 0 0 4.933-4.846c3.851-6.461 1.735-14.82-4.727-18.67L98.068 1.28l-5.788 9.71z" stroke="url(#A)" stroke-width="1.444" fill="url(#B)" fill-rule="nonzero" transform="translate(1118.516 251.907)"/><g filter="url(#C)" transform="translate(1246.944 313.662)"><path fill="url(#D)" d="M2.509 5.794L0 4.345l2.509-1.448 2.509 1.448z" transform="translate(0 .119)"/><path fill="url(#E)" d="M5.018 4.345L2.509 2.897V0l2.509 1.448z" transform="translate(0 .119)"/><path fill="url(#F)" d="M2.509 2.897L0 4.345V1.448L2.509 0z" transform="translate(0 .119)"/></g><g filter="url(#G)" transform="translate(1201.527 339.952)"><path fill="url(#D)" d="M2.509 5.794L0 4.345l2.509-1.448 2.509 1.448z" transform="translate(0 .119)"/><path fill="url(#E)" d="M5.018 4.345L2.509 2.897V0l2.509 1.448z" transform="translate(0 .119)"/><path fill="url(#F)" d="M2.509 2.897L0 4.345V1.448L2.509 0z" transform="translate(0 .119)"/></g><g fill-rule="nonzero"><path fill="url(#H)" d="M.163 16.079V13.182L22.429 0v2.897l2.509 1.448L2.672 17.527.814 16.455l-.651-.376z" transform="translate(1214.188 320.95)"/><g filter="url(#I)" transform="translate(1234.108 320.95)"><path fill="#00F6FF" d="M2.509 5.794L0 4.345l2.509-1.448 2.509 1.448z"/><path fill="#03E2F9" d="M5.018 4.345L2.509 2.897V0l2.509 1.448z"/><path fill="#69E0FF" d="M2.509 2.897L0 4.345V1.448L2.509 0z"/></g></g><g fill-rule="nonzero"><path fill="url(#g)" d="M24.822 16.124l-2.509 1.448L.047 4.39l2.509-1.448V.045l22.266 13.182z" transform="translate(1243.296 274.214)"/><g filter="url(#J)" transform="translate(1243.296 274.214)"><path fill="#00F6FF" d="M2.556 5.839L.047 4.39l2.509-1.448L5.065 4.39z"/><path fill="#03E2F9" d="M.047 4.39V1.494L2.556.045v2.897z"/><path fill="#69E0FF" d="M2.556 2.942V.045l2.509 1.449V4.39z"/></g></g><path d="M1129.597 225.296l.722-1.25 124.407 71.87a2.165 2.165 0 0 1-.005 3.754l-114.13 65.48-.718-1.253 114.129-65.48a.722.722 0 0 0 .002-1.25l-124.407-71.87z" fill="#01E5FB" fill-rule="nonzero"/></g><g><path d="M925.751 399.09l109.875-63.878 155.338 90.294-109.906 63.89c-10.895 6.335-27.376 7.023-36.812 1.537L923.11 420.492c-9.437-5.489-8.255-15.07 2.64-21.402z" fill="#0D054C" opacity=".3"/><path d="M948.08 401.467l90.667-52.711 128.183 74.51-90.693 52.721c-8.99 5.227-22.59 5.795-30.377 1.268l-99.96-58.127c-7.786-4.53-6.811-12.436 2.18-17.661z" fill="#0D054C" opacity=".3"/><path d="M1165.856 423.266l-127.464-74.093-90.487 52.606c-8.77 5.096-9.703 12.66-2.179 17.037l99.96 58.127c7.669 4.459 21.131 3.896 30.014-1.268l90.156-52.41zm-218.314-22.111l90.85-52.817 128.9 74.928-91.229 53.033c-9.098 5.29-22.836 5.863-30.74 1.268l-99.96-58.127c-8.05-4.682-7.031-12.933 2.18-18.285z" fill="#01E1FA" fill-rule="nonzero"/></g><g fill-rule="nonzero"><path d="M2.266 160.323l17.222 4.76L269.375 16.378a18.253 18.253 0 0 1 18.393-.161L407.226 84.5c3.895 2.227 6.786.951 8.095-2.658 1.21-3.336.457-7.308-1.694-8.538L294.17 5.021a31.153 31.153 0 0 0-31.39.274L2.265 160.323z" stroke="url(#K)" stroke-width="1.444" fill="url(#L)" transform="translate(668.939 348.49)"/><path d="M41.964 160.716l-.723-1.249 235.238-136.26a5.776 5.776 0 0 1 5.701-.05l73.943 41.088-.701 1.262-73.943-41.089a4.332 4.332 0 0 0-4.276.038L41.964 160.716z" fill="url(#M)" transform="translate(668.939 348.49)"/><g filter="url(#N)" transform="translate(875.312 390.014)"><path fill="#00F6FF" d="M2.509 5.913L0 4.463l2.509-1.447 2.509 1.448z"/><path fill="#03E2F9" d="M5.018 4.464L2.509 3.016V.119l2.509 1.448z"/><path fill="#69E0FF" d="M2.509 3.016L0 4.464V1.567L2.509.119z"/></g><g filter="url(#O)" transform="translate(982.48 370.36)"><path fill="#00F6FF" d="M2.509 5.913L0 4.463l2.509-1.447 2.509 1.448z"/><path fill="#03E2F9" d="M5.018 4.464L2.509 3.016V.119l2.509 1.448z"/><path fill="#69E0FF" d="M2.509 3.016L0 4.464V1.567L2.509.119z"/></g><path fill="url(#H)" d="M.163 16.079V13.182L22.429 0v2.897l2.509 1.448L2.672 17.527.814 16.455l-.651-.376z" transform="translate(890.782 369.484)"/><g filter="url(#P)" transform="translate(910.701 369.484)"><path fill="#00F6FF" d="M2.509 5.794L0 4.345l2.509-1.448 2.509 1.448z"/><path fill="#03E2F9" d="M5.018 4.345L2.509 2.897V0l2.509 1.448z"/><path fill="#69E0FF" d="M2.509 2.897L0 4.345V1.448L2.509 0z"/></g></g><g fill-rule="nonzero"><g transform="translate(729.029 307.169)"><ellipse fill="#03E1F9" opacity=".15" cx="7.968" cy="34.257" rx="7.66" ry="7.659"/><ellipse fill="#03E1F9" cx="7.968" cy="34.257" rx="1.594" ry="1.593"/><ellipse fill="#03E1F9" opacity=".3" cx="7.968" cy="34.257" rx="4.461" ry="4.46"/><path fill="url(#Q)" d="M7.171.398h1.594v33.461H7.171z"/></g><g transform="translate(864.151 415.949)"><ellipse fill="#03E1F9" opacity=".15" cx="7.968" cy="34.257" rx="7.66" ry="7.659"/><ellipse fill="#03E1F9" cx="7.968" cy="34.257" rx="1.594" ry="1.593"/><ellipse fill="#03E1F9" opacity=".3" cx="7.968" cy="34.257" rx="4.461" ry="4.46"/><path fill="url(#Q)" d="M7.171.398h1.594v33.461H7.171z"/></g><g transform="translate(1038.25 97.403)"><ellipse fill="#03E1F9" opacity=".15" cx="7.968" cy="34.257" rx="7.66" ry="7.659"/><ellipse fill="#03E1F9" cx="7.968" cy="34.257" rx="1.594" ry="1.593"/><ellipse fill="#03E1F9" opacity=".3" cx="7.968" cy="34.257" rx="4.461" ry="4.46"/><path fill="url(#Q)" d="M7.171.398h1.594v33.461H7.171z"/></g><g transform="translate(777.54 97.976)"><ellipse fill="#03E1F9" opacity=".15" cx="7.968" cy="34.257" rx="7.66" ry="7.659"/><ellipse fill="#03E1F9" cx="7.968" cy="34.257" rx="1.594" ry="1.593"/><ellipse fill="#03E1F9" opacity=".3" cx="7.968" cy="34.257" rx="4.461" ry="4.46"/><path fill="url(#Q)" d="M7.171.398h1.594v33.461H7.171z"/></g><g transform="translate(960.899 283.048)"><ellipse fill="#03E1F9" opacity=".15" cx="7.968" cy="34.257" rx="7.66" ry="7.659"/><ellipse fill="#03E1F9" cx="7.968" cy="34.257" rx="1.594" ry="1.593"/><ellipse fill="#03E1F9" opacity=".3" cx="7.968" cy="34.257" rx="4.461" ry="4.46"/><path fill="url(#Q)" d="M7.171.398h1.594v33.461H7.171z"/></g></g><g><path d="M1063.747 471.303l-105.7-61.312c-1.091-.633-1.625-1.478-1.632-2.365l-.004-1.967h.008c.057.837.591 1.631 1.628 2.233l105.7 61.311c2.425 1.407 6.66 1.23 9.46-.394v2.1c-2.8 1.625-7.035 1.801-9.46.394z" fill="#5C7AD7"/><path d="M1142.329 428.812c1.42-.828 2.193-1.872 2.297-2.885l.001 1.681c.013.137.014.273 0 .41v.033c-.11 1.009-.885 2.047-2.298 2.872l-69.13 39.993v-2.111l69.13-39.993z" fill="#5C7AD7"/><path d="M2.32 134.188l28.282-16.442 114.54 66.53-28.16 16.57c-2.805 1.63-7.047 1.808-9.476.396L1.64 139.697c-2.429-1.413-2.125-3.879.68-5.509z" fill="url(#R)" transform="translate(956.411 268.128)"/><path d="M1003.002 424.887l22.283-12.954 31.504 18.312-22.29 12.957c-2.21 1.285-5.552 1.424-7.465.312l-24.567-14.286c-1.914-1.113-1.674-3.057.535-4.34z" fill="#0B0F89"/><path d="M1020.251 443.303c.464.266.466.696.006.962l-18.961-10.901c.461-.266 1.21-.264 1.673.003l17.282 9.936z" fill="#4C6BCB"/><path fill="#5C7AD7" d="M1133.76 419.171l-89.51-51.89 1.223-.71 89.511 51.891z"/><path d="M185.908 160.526l-40.987 23.814-114.515-66.524L71.4 94c1.533-.89 3.494-1.346 5.354-1.346 1.533 0 2.998.31 4.095.942l.015.01 8.164 4.742-1.222.71 89.395 51.939 1.222-.71 8.165 4.743c1.099.638 1.638 1.493 1.638 2.388 0 1.08-.786 2.22-2.318 3.11z" fill="url(#S)" transform="translate(956.411 268.128)"/><path d="M1125.782 432.017a1.411 1.411 0 0 1-.71.183c-.204 0-.398-.042-.544-.13l-5.254-3.152c-.321-.193-.28-.53.09-.752l5.035-3.02c.202-.122.462-.184.708-.184.205 0 .4.042.545.13l5.254 3.152c.322.193.28.53-.09.752l-5.034 3.02zm-7.24-4.343a1.411 1.411 0 0 1-.709.183c-.203 0-.398-.042-.544-.13l-5.255-3.152c-.32-.193-.28-.53.09-.751l5.035-3.021c.203-.122.463-.184.709-.184.204 0 .399.042.544.13l5.254 3.152c.322.193.282.53-.09.752l-5.034 3.02zm-7.239-3.862a1.417 1.417 0 0 1-.71.185c-.203 0-.398-.043-.543-.13l-5.255-3.152c-.321-.193-.28-.53.09-.752l5.034-3.02c.203-.122.463-.185.71-.185.203 0 .398.044.544.131l5.254 3.151c.321.193.281.53-.09.752l-5.034 3.02zm-7.24-4.342a1.413 1.413 0 0 1-.708.184c-.204 0-.4-.043-.545-.13l-5.254-3.152c-.321-.193-.281-.53.09-.752l5.035-3.02c.202-.122.462-.185.708-.185.204 0 .399.044.544.13l5.255 3.153c.32.193.28.53-.09.751l-5.034 3.021zm28.56 8.201a1.434 1.434 0 0 1-.72.186c-.205 0-.403-.043-.55-.131l-5.32-3.191c-.325-.195-.284-.536.091-.762l2.535-1.52c.205-.123.468-.186.717-.186.207 0 .404.043.552.132l5.32 3.19c.325.195.283.537-.092.762l-2.534 1.52zm-7.24-3.86a1.433 1.433 0 0 1-.718.186c-.207 0-.404-.043-.551-.132l-5.32-3.191c-.325-.195-.284-.536.091-.762l2.534-1.52a1.43 1.43 0 0 1 .718-.186c.207 0 .404.043.551.131l5.32 3.192c.325.195.284.536-.091.76l-2.535 1.522zm-6.758-4.344a1.432 1.432 0 0 1-.717.187c-.207 0-.404-.044-.551-.132l-5.32-3.192c-.325-.195-.284-.535.092-.76l2.533-1.521c.206-.123.469-.186.718-.186.207 0 .404.043.551.131l5.32 3.192c.325.195.284.535-.091.76l-2.535 1.521zm-7.239-4.343a1.423 1.423 0 0 1-.718.187c-.206 0-.404-.044-.55-.133l-5.32-3.19c-.326-.195-.284-.536.091-.761l2.535-1.52a1.43 1.43 0 0 1 .717-.187c.206 0 .404.044.55.131l5.32 3.192c.325.195.285.536-.09.76l-2.535 1.52zm-14.562.003a1.413 1.413 0 0 1-.709.184c-.204 0-.4-.043-.545-.13l-5.254-3.153c-.321-.192-.28-.529.09-.752l5.035-3.02c.203-.122.463-.184.709-.184.204 0 .398.043.544.13l5.254 3.153c.322.192.28.529-.09.752l-5.034 3.02zm-7.24-3.86a1.411 1.411 0 0 1-.709.183c-.203 0-.398-.042-.544-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.184.709-.184.204 0 .4.043.545.13l5.254 3.152c.321.193.28.53-.09.752l-5.035 3.02zm-6.757-4.344a1.412 1.412 0 0 1-.709.184c-.203 0-.398-.043-.543-.13l-5.255-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.184.709-.184.204 0 .4.043.544.13l5.255 3.152c.321.192.281.53-.09.752l-5.035 3.02zm-7.239-4.344a1.406 1.406 0 0 1-.708.185c-.205 0-.4-.043-.545-.13l-5.254-3.152c-.322-.193-.282-.53.09-.753l5.034-3.02c.203-.121.462-.184.71-.184.203 0 .398.043.543.13l5.254 3.153c.322.193.282.529-.09.752l-5.034 3.02zm-7.35-3.987a1.413 1.413 0 0 1-.709.183c-.204 0-.399-.042-.544-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.184.71-.184.203 0 .397.042.544.13l5.254 3.152c.32.193.28.53-.09.752l-5.035 3.02zm-7.129-4.216a1.412 1.412 0 0 1-.709.184 1.08 1.08 0 0 1-.544-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.184.709-.184.204 0 .399.043.544.13l5.255 3.152c.321.193.28.529-.09.752l-5.035 3.02zm-7.239-3.86a1.417 1.417 0 0 1-.71.184c-.203 0-.398-.043-.543-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.034-3.02c.203-.122.463-.185.71-.185.203 0 .398.044.544.13l5.254 3.152c.321.194.281.53-.09.752l-5.034 3.02zm-7.24-4.343a1.413 1.413 0 0 1-.708.184c-.204 0-.4-.043-.545-.13l-5.254-3.153c-.321-.192-.281-.529.09-.752l5.034-3.02c.203-.122.463-.184.71-.184.203 0 .398.043.543.13l5.255 3.153c.321.192.28.529-.09.752l-5.034 3.02zm-6.756-4.343a1.413 1.413 0 0 1-.709.184c-.204 0-.4-.043-.545-.13l-5.254-3.153c-.321-.193-.281-.529.09-.752l5.035-3.02c.203-.122.462-.184.709-.184.203 0 .398.043.543.13l5.255 3.153c.321.192.281.529-.09.752l-5.034 3.02zm-7.298-3.862a1.441 1.441 0 0 1-.716.185c-.207 0-.404-.043-.55-.13l-8.56-5.082c-.324-.192-.284-.529.092-.752l5.09-3.02c.204-.123.466-.185.716-.185.205 0 .402.043.55.13l8.559 5.081c.324.193.283.53-.092.752l-5.089 3.021zm71.57 33.296a1.429 1.429 0 0 1-.717.186c-.206 0-.403-.043-.55-.131l-5.321-3.192c-.325-.195-.284-.535.091-.76l2.535-1.52a1.43 1.43 0 0 1 .717-.188c.207 0 .404.044.552.133l5.32 3.19c.324.196.283.536-.092.761l-2.535 1.521zm-7.239-4.343a1.433 1.433 0 0 1-.717.186c-.207 0-.404-.043-.551-.131l-5.32-3.192c-.325-.195-.285-.536.09-.762l2.535-1.52a1.43 1.43 0 0 1 .718-.186c.207 0 .404.043.551.132l5.32 3.191c.325.196.284.536-.091.762l-2.535 1.52zm-7.24-3.861a1.432 1.432 0 0 1-.717.187c-.207 0-.404-.044-.551-.133l-5.32-3.19c-.325-.195-.284-.537.091-.761l2.535-1.521a1.43 1.43 0 0 1 .717-.186c.207 0 .404.043.552.131l5.319 3.191c.325.195.285.536-.09.762l-2.535 1.52zm-7.239-4.343a1.43 1.43 0 0 1-.717.186c-.207 0-.404-.043-.552-.131l-5.319-3.191c-.325-.195-.284-.536.091-.762l2.534-1.52c.206-.123.469-.186.718-.186.206 0 .404.043.55.132l5.321 3.19c.325.195.284.537-.091.762l-2.535 1.52zm-6.757-4.343a1.429 1.429 0 0 1-.717.186 1.09 1.09 0 0 1-.552-.131l-5.32-3.191c-.324-.195-.283-.536.092-.762l2.535-1.52a1.43 1.43 0 0 1 .717-.186c.206 0 .404.043.552.132l5.32 3.19c.324.196.283.537-.092.761l-2.535 1.521zm-7.239-3.861a1.435 1.435 0 0 1-.718.187c-.206 0-.403-.044-.55-.132l-5.32-3.192c-.325-.195-.285-.535.09-.76l2.535-1.521c.205-.123.469-.186.718-.186.207 0 .404.043.551.131l5.32 3.192c.325.195.283.535-.092.761l-2.534 1.52zm-7.24-4.343a1.432 1.432 0 0 1-.717.187c-.207 0-.404-.044-.551-.132l-5.32-3.191c-.325-.195-.284-.536.091-.761l2.535-1.52c.205-.124.468-.187.717-.187.207 0 .404.043.551.131l5.32 3.192c.325.195.284.535-.091.76l-2.534 1.52zm-7.239-4.342a1.431 1.431 0 0 1-.717.186c-.207 0-.405-.043-.552-.132l-5.32-3.191c-.325-.195-.283-.536.092-.761l2.534-1.52c.206-.124.468-.187.718-.187.206 0 .404.044.55.132l5.32 3.19c.326.196.285.537-.09.762l-2.535 1.52zm-7.24-3.86a1.43 1.43 0 0 1-.717.185c-.207 0-.404-.043-.551-.131l-5.32-3.192c-.325-.195-.284-.536.092-.761l2.534-1.52c.205-.124.468-.187.718-.187.206 0 .403.043.551.132l5.32 3.191c.324.196.283.536-.092.762l-2.534 1.52zm-6.784-4.333a1.54 1.54 0 0 1-.725.175c-.208 0-.407-.041-.556-.124l-8.656-4.807c-.328-.182-.286-.5.093-.712l2.559-1.42c.207-.116.472-.176.724-.176.208 0 .408.041.557.124l8.655 4.807c.329.183.287.501-.092.712l-2.559 1.421zm-17.912 6.265a1.416 1.416 0 0 1-.71.183c-.203 0-.398-.042-.544-.13l-5.254-3.152c-.321-.193-.281-.53.09-.751l5.034-3.021c.203-.122.463-.184.71-.184.203 0 .398.042.543.13l5.254 3.152c.322.193.282.53-.09.752l-5.033 3.02zm7.238 4.343a1.411 1.411 0 0 1-.708.183c-.204 0-.399-.042-.544-.13l-5.255-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.184.71-.184.203 0 .398.042.544.13l5.254 3.152c.322.193.28.53-.09.752l-5.035 3.02zm7.24 4.342a1.414 1.414 0 0 1-.709.185c-.204 0-.398-.044-.544-.13l-5.255-3.153c-.32-.192-.28-.53.09-.751l5.035-3.02c.203-.122.463-.185.709-.185.204 0 .398.043.544.13l5.255 3.152c.321.194.28.53-.09.752l-5.035 3.02zm7.24 3.86a1.414 1.414 0 0 1-.71.185c-.203 0-.398-.043-.544-.13l-5.254-3.153c-.321-.193-.281-.529.09-.75l5.035-3.022c.202-.121.462-.183.708-.183.204 0 .399.043.545.13l5.254 3.152c.322.192.28.53-.09.752l-5.034 3.02zm7.24 4.344a1.413 1.413 0 0 1-.71.184c-.204 0-.399-.042-.544-.13l-5.254-3.152c-.322-.193-.282-.53.09-.752l5.034-3.02c.203-.122.462-.184.71-.184.203 0 .398.042.543.13l5.254 3.152c.322.193.282.53-.09.752l-5.034 3.02zm13.995 8.203a1.412 1.412 0 0 1-.709.185 1.08 1.08 0 0 1-.544-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.185.709-.185.204 0 .399.044.544.13l5.255 3.152c.321.193.28.53-.09.752l-5.035 3.02zm7.24 4.343a1.414 1.414 0 0 1-.71.185c-.203 0-.398-.043-.544-.13l-5.254-3.153c-.321-.193-.28-.53.09-.751l5.035-3.02c.202-.123.463-.185.708-.185.205 0 .399.043.545.13l5.254 3.152c.322.193.28.53-.09.752l-5.034 3.02zm7.24 3.862a1.413 1.413 0 0 1-.71.183c-.204 0-.398-.042-.544-.13l-5.254-3.152c-.322-.193-.281-.53.09-.752l5.035-3.02c.202-.122.462-.184.708-.184.204 0 .399.042.544.13l5.254 3.152c.322.193.282.53-.09.752l-5.034 3.02zm7.239 4.343a1.416 1.416 0 0 1-.71.183c-.203 0-.398-.042-.543-.13l-5.255-3.152c-.321-.192-.281-.529.09-.752l5.034-3.02c.203-.121.463-.184.709-.184.204 0 .4.043.544.13l5.255 3.153c.321.193.281.529-.09.751l-5.034 3.02zm7.239 4.342a1.414 1.414 0 0 1-.71.185 1.08 1.08 0 0 1-.543-.13l-5.255-3.152c-.32-.194-.28-.53.09-.752l5.035-3.02c.202-.122.463-.185.709-.185.204 0 .399.043.544.13l5.254 3.152c.322.193.282.53-.09.752l-5.034 3.02zm6.757 3.861a1.413 1.413 0 0 1-.71.184c-.203 0-.398-.042-.544-.13l-5.254-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.202-.122.463-.184.708-.184.205 0 .399.042.545.13l5.254 3.152c.322.193.281.53-.09.752l-5.034 3.02zm7.24 4.343a1.413 1.413 0 0 1-.71.184c-.204 0-.398-.042-.544-.13l-5.254-3.152c-.322-.193-.28-.53.09-.752l5.035-3.02c.202-.122.462-.184.708-.184.204 0 .399.042.544.13l5.255 3.152c.32.193.28.53-.09.752l-5.035 3.02zm10.56 5.791a1.44 1.44 0 0 1-.717.184c-.207 0-.404-.042-.55-.13l-8.56-5.08c-.324-.194-.284-.53.091-.753l5.09-3.021c.205-.122.467-.185.717-.185.205 0 .402.044.55.13l8.559 5.082c.324.193.283.53-.092.752l-5.089 3.021zm-98.454-48.736c-.205.12-.467.18-.717.18-.206 0-.402-.04-.55-.127l-10.493-6.12c-.324-.19-.283-.52.091-.738l5.087-2.967c.204-.12.467-.181.716-.181.206 0 .403.042.55.127l10.493 6.121c.325.19.283.52-.091.739l-5.086 2.966zm91.216 53.083a1.46 1.46 0 0 1-.716.18c-.206 0-.402-.042-.549-.127l-10.493-6.12c-.325-.19-.285-.52.09-.739l5.087-2.967a1.46 1.46 0 0 1 .716-.18c.206 0 .403.042.55.128l10.493 6.12c.325.189.283.52-.091.738l-5.087 2.967zm-83.922-48.744a1.404 1.404 0 0 1-.709.185c-.204 0-.399-.043-.544-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.185.71-.185.203 0 .397.044.544.131l5.254 3.151c.32.193.28.53-.09.752l-5.035 3.02zm7.24 3.86a1.414 1.414 0 0 1-.709.185c-.204 0-.399-.043-.544-.13l-5.255-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.204-.122.463-.184.709-.184.204 0 .4.043.544.13l5.255 3.152c.321.193.281.53-.09.752l-5.034 3.02zm7.24 4.345a1.413 1.413 0 0 1-.71.184c-.204 0-.399-.043-.544-.13l-5.254-3.153c-.322-.193-.282-.529.09-.752l5.034-3.02c.202-.122.462-.184.708-.184.205 0 .4.043.545.13l5.254 3.152c.322.193.282.53-.09.752l-5.034 3.02zm6.935 4.147a1.411 1.411 0 0 1-.71.184c-.203 0-.398-.042-.543-.13l-5.254-3.152c-.322-.193-.282-.53.09-.752l5.034-3.02c.202-.122.462-.184.708-.184.205 0 .4.042.545.13l5.254 3.152c.322.193.282.53-.09.751l-5.034 3.021zm7.06 4.056a1.414 1.414 0 0 1-.708.184c-.205 0-.399-.043-.545-.13l-5.254-3.152c-.322-.193-.28-.529.09-.752l5.034-3.02c.203-.121.463-.184.71-.184.204 0 .398.043.544.13l5.254 3.152c.32.192.28.53-.09.752l-5.035 3.02zm7.24 4.344a1.413 1.413 0 0 1-.709.183c-.204 0-.399-.042-.544-.13l-5.255-3.152c-.321-.193-.281-.53.09-.751l5.035-3.022c.203-.121.462-.183.709-.183.203 0 .398.042.544.13l5.255 3.152c.32.193.28.53-.09.752l-5.035 3.02zm7.24 4.343a1.413 1.413 0 0 1-.71.183c-.204 0-.398-.042-.544-.13l-5.254-3.152c-.322-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.184.71-.184.203 0 .398.042.543.13l5.254 3.152c.322.193.282.53-.09.752l-5.033 3.02zm7.238 3.86a1.411 1.411 0 0 1-.709.184c-.203 0-.398-.043-.543-.13l-5.255-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.185.709-.185.204 0 .4.044.545.131l5.254 3.152c.321.193.281.53-.09.751l-5.035 3.021zm7.24 4.342a1.412 1.412 0 0 1-.709.185 1.08 1.08 0 0 1-.544-.13l-5.255-3.152c-.321-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.185.709-.185.204 0 .399.044.544.131l5.255 3.152c.321.192.28.53-.09.751l-5.035 3.02zm7.24 3.862a1.413 1.413 0 0 1-.71.184c-.203 0-.398-.043-.544-.13l-5.254-3.153c-.322-.193-.28-.529.09-.752l5.035-3.02c.202-.122.462-.184.708-.184.204 0 .399.042.544.13l5.255 3.152c.321.193.281.53-.09.752l-5.034 3.02zm6.757 4.343a1.413 1.413 0 0 1-.71.184c-.203 0-.398-.043-.544-.13l-5.254-3.153c-.321-.193-.281-.529.09-.752l5.034-3.02c.203-.122.463-.184.71-.184.203 0 .398.043.543.13l5.255 3.153c.321.192.28.529-.09.752l-5.034 3.02zm-82.054-39.565a1.45 1.45 0 0 1-.71.178c-.204 0-.4-.041-.545-.126l-13.933-8.1c-.322-.187-.281-.513.09-.729l5.04-2.93c.204-.119.464-.179.71-.179.205 0 .4.042.546.126l13.932 8.1c.322.187.282.514-.09.73l-5.04 2.93zm7.245 4.338a1.411 1.411 0 0 1-.708.183c-.204 0-.399-.042-.545-.13l-5.254-3.152c-.32-.193-.28-.53.09-.752l5.034-3.02c.203-.122.463-.184.71-.184.204 0 .398.042.544.13l5.254 3.152c.321.193.281.53-.09.752l-5.035 3.02zm7.24 3.86a1.406 1.406 0 0 1-.709.184c-.204 0-.398-.043-.544-.13l-5.254-3.152c-.322-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.185.709-.185.204 0 .398.044.544.13l5.255 3.153c.321.193.28.53-.09.751l-5.035 3.02zm6.757 4.342a1.414 1.414 0 0 1-.709.185c-.204 0-.399-.043-.544-.13l-5.254-3.152c-.322-.194-.282-.53.09-.752l5.034-3.02c.203-.122.462-.185.709-.185.204 0 .398.044.544.13l5.255 3.152c.32.193.28.53-.09.752l-5.035 3.02zm7.24 4.344a1.413 1.413 0 0 1-.709.184c-.204 0-.4-.042-.545-.13l-5.254-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.204-.122.463-.184.71-.184.203 0 .398.042.543.13l5.255 3.152c.321.193.281.529-.09.752l-5.034 3.02zm7.24 3.86a1.415 1.415 0 0 1-.71.185c-.204 0-.399-.043-.544-.13l-5.254-3.153c-.322-.193-.282-.529.09-.752l5.034-3.02c.202-.122.462-.184.708-.184.205 0 .4.042.545.13l5.254 3.152c.322.193.281.53-.09.752l-5.034 3.02zm7.238 4.343a1.412 1.412 0 0 1-.709.185 1.08 1.08 0 0 1-.544-.131l-5.255-3.151c-.32-.193-.28-.53.09-.752l5.035-3.02c.203-.122.463-.185.709-.185.204 0 .4.043.544.13l5.255 3.152c.321.193.28.53-.09.752l-5.035 3.02zm7.24 3.86a1.414 1.414 0 0 1-.709.185c-.204 0-.398-.043-.545-.13l-5.254-3.153c-.32-.192-.28-.529.09-.75l5.035-3.02c.202-.123.462-.185.709-.185.203 0 .398.043.544.13l5.255 3.152c.32.192.28.529-.09.752l-5.035 3.02zm7.24 4.345a1.413 1.413 0 0 1-.71.183c-.203 0-.398-.042-.544-.13l-5.254-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.184.71-.184.203 0 .398.042.543.13l5.254 3.152c.322.193.282.53-.09.752l-5.033 3.02zm6.756 4.343a1.416 1.416 0 0 1-.71.183c-.203 0-.398-.042-.543-.13l-5.254-3.152c-.322-.192-.282-.529.09-.752l5.034-3.02c.203-.121.462-.184.708-.184.205 0 .4.043.545.13l5.254 3.153c.322.192.282.529-.09.752l-5.034 3.02zm7.24 3.86a1.412 1.412 0 0 1-.71.184c-.203 0-.398-.043-.543-.13l-5.255-3.152c-.321-.193-.281-.53.09-.752l5.034-3.02c.203-.122.463-.185.709-.185.204 0 .4.044.545.131l5.254 3.151c.321.193.28.53-.09.752l-5.035 3.02zm15.932 9.174a1.448 1.448 0 0 1-.707.179c-.205 0-.4-.042-.544-.127l-13.95-8.109c-.321-.186-.28-.512.09-.727l5.029-2.924c.202-.117.462-.178.708-.178.204 0 .398.042.544.126l13.95 8.11c.32.186.28.512-.091.727l-5.029 2.923zm-37.237-13.512a1.48 1.48 0 0 1-.718.179c-.206 0-.404-.042-.55-.127l-34.126-19.67c-.325-.187-.284-.514.091-.73l5.1-2.94c.205-.119.468-.179.717-.179.207 0 .404.041.552.126l34.124 19.67c.326.188.285.515-.09.732l-5.1 2.939zm-44.333-25.582a1.414 1.414 0 0 1-.71.185 1.08 1.08 0 0 1-.543-.13l-5.255-3.152c-.32-.193-.28-.53.09-.752l5.035-3.02c.202-.122.463-.185.708-.185.205 0 .399.044.545.13l5.254 3.153c.322.192.281.53-.09.751l-5.034 3.02zm8.574 5.123a1.39 1.39 0 0 1-.703.185c-.203 0-.396-.043-.54-.131l-6.755-4.1c-.319-.194-.279-.532.09-.755l4.996-3.032c.201-.122.458-.186.703-.186.202 0 .396.044.54.132l6.754 4.099c.32.194.28.532-.089.755l-4.996 3.033zm51.755 30.105a1.413 1.413 0 0 1-.709.184c-.204 0-.4-.042-.544-.13l-5.255-3.152c-.321-.193-.281-.529.09-.752l5.035-3.02c.203-.122.462-.184.709-.184.203 0 .398.043.543.13l5.255 3.152c.321.193.281.53-.09.752l-5.034 3.02zm-7.2-4.343a1.39 1.39 0 0 1-.703.184c-.202 0-.395-.043-.54-.13l-6.754-4.101c-.319-.193-.279-.531.09-.755l4.995-3.033a1.39 1.39 0 0 1 .704-.185c.202 0 .396.043.54.13l6.754 4.101c.32.194.279.532-.09.756l-4.995 3.033zm-74.847-43.431a1.414 1.414 0 0 1-.709.184c-.204 0-.4-.043-.544-.13l-5.255-3.152c-.321-.193-.281-.53.09-.752l5.035-3.02c.203-.122.462-.184.709-.184.203 0 .398.043.544.13l5.254 3.152c.321.192.281.53-.09.752l-5.034 3.02zm7.24 4.343a1.416 1.416 0 0 1-.71.184c-.204 0-.399-.042-.544-.13l-5.254-3.152c-.322-.193-.282-.53.09-.751l5.034-3.022c.203-.121.462-.183.71-.183.203 0 .397.042.543.13l5.254 3.152c.322.193.282.53-.09.752l-5.034 3.02zm7.238 3.861a1.411 1.411 0 0 1-.709.184c-.203 0-.398-.043-.543-.13l-5.256-3.153c-.32-.192-.28-.529.09-.752l5.035-3.02c.203-.122.463-.184.709-.184.204 0 .399.043.544.13l5.255 3.153c.321.192.281.529-.09.752l-5.035 3.02zm82.026 47.78a1.468 1.468 0 0 1-.712.178c-.205 0-.4-.042-.547-.126l-12.465-7.166c-.323-.185-.283-.51.09-.724l5.055-2.906c.204-.117.464-.177.712-.177.205 0 .4.041.546.125l12.466 7.166c.322.186.282.51-.09.724l-5.055 2.906zm-39.355-47.585a1.416 1.416 0 0 1-.71.184c-.203 0-.397-.042-.543-.13l-5.254-3.152c-.322-.193-.281-.53.09-.752l5.034-3.02c.202-.122.463-.184.709-.184.204 0 .399.042.544.13l5.254 3.152c.322.193.282.53-.09.751l-5.034 3.021z" fill="#0B0F89"/><path d="M1147.348 339.385c0-4.527-1.467-6.727-4.384-8.45l-105.539-61.465c-1.078-.628-2.064-.85-2.828-.542 0 0 1.018-.613 1.182-.675v-.001c.76-.289 1.734-.061 2.796.558l105.539 61.463c2.916 1.724 4.384 3.923 4.384 8.45v80.507c0 2.164-.591 3.382-1.523 3.81l-.831.474c.746-.545 1.204-1.716 1.204-3.623v-80.506z" fill="#5C7AD7"/><path d="M1147.532 339.734l.966-.669v80.402l-.966.67v-80.403z" fill="#5C7AD7"/><path d="M1143.145 423.352l-105.608-61.39c-2.65-1.533-4.388-3.186-4.388-8.44v-80.409c0-4.125 1.965-4.748 4.388-3.34l105.608 61.39c2.92 1.722 4.387 3.919 4.387 8.44v80.41c0 3.943-1.964 4.747-4.387 3.339z" fill="#0F0A5E"/><path d="M187.085 63.79c1.96 1.14 3.554 3.91 3.554 6.176v82.888c0 2.266-1.594 3.182-3.554 2.042L80.775 93.045c-1.96-1.14-3.554-3.911-3.554-6.177V3.98c0-2.266 1.594-3.182 3.554-2.042l106.31 61.852z" fill="url(#T)" transform="translate(956.411 268.128)"/><path fill="#0B0F89" d="M1142.706 336.478v77.384l-105.213-61.112v-77.384z"/><path d="M104.905 63.082c0 13.024 10.557 28.96 23.583 35.597 13.023 6.635 23.584 1.46 23.584-11.564 0-13.022-10.56-28.962-23.584-35.597-13.026-6.637-23.583-1.457-23.583 11.564zm1.696.749c0-3.162.677-5.817 1.878-7.92l10.338 33.769c-7.229-7.214-12.216-17.218-12.216-25.85zm22.033 33.225c-2.113-1.076-4.155-2.43-6.084-3.99l6.463-15.834 6.619 21.846c.043.132.096.255.152.376-2.236-.334-4.644-1.12-7.15-2.398zm17.554-13.28c1.246-2.461 1.663-4.725 1.663-6.927 0-.8-.054-1.568-.151-2.31 1.705 3.963 2.675 8.003 2.675 11.779 0 8.01-4.366 12.777-10.859 13.23l6.672-15.771zm-4.74-9.232c.827 1.877 1.797 4.242 1.797 6.945 0 1.872-.555 3.942-1.658 6.223l-2.174 6.174-7.874-27.506a58.27 58.27 0 0 0 2.492 1.061c1.175.459 1.034-1.34-.136-1.872 0 0-3.53-1.52-5.809-2.68-2.141-1.091-5.737-3.203-5.737-3.203-1.174-.663-1.31 1.067-.14 1.732 0 0 1.114.704 2.289 1.375l3.391 11.059-4.766 11.914-7.936-27.718c1.314.598 2.495 1.063 2.495 1.063 1.175.459 1.032-1.342-.14-1.874 0 0-3.527-1.518-5.808-2.68a73.776 73.776 0 0 1-1.4-.739c3.897-3.949 10.594-4.456 18.204-.579 5.672 2.89 10.837 7.699 14.711 13.237-.093-.054-.186-.113-.28-.16-2.145-1.094-3.66.004-3.66 2.012 0 1.803 1.036 3.855 2.139 6.216z" fill="url(#U)" fill-rule="nonzero" transform="translate(956.263 267.975)"/></g><g><path d="M75.874 29.064v78.449c.057.26.04.544-.064.804a1.08 1.08 0 0 1-.444.537L51.755 122.76a3.794 3.794 0 0 1-3.911 0L.882 95.236c-.296-.173-.485-.507-.53-.873h-.01V15.447l75.532 13.617z" fill="url(#V)" transform="translate(1194.61 122.422)"/><path d="M1203.35 210.235c.403.197.66.607.66 1.057v6.659a.392.392 0 0 1-.565.352l-1.226-.599a1.176 1.176 0 0 1-.66-1.057v-6.659a.392.392 0 0 1 .565-.352l1.226.599zm-3.79-2.087c.404.197.66.607.66 1.057v6.659a.392.392 0 0 1-.565.352l-1.226-.6a1.176 1.176 0 0 1-.66-1.056v-6.659a.392.392 0 0 1 .565-.352l1.226.599z" fill="#0B0F89"/><path d="M75.785 28.917v78.449c.057.26.04.544-.064.804a1.08 1.08 0 0 1-.444.537l-23.611 13.907a3.794 3.794 0 0 1-3.911 0v-95.29l28.03 1.593z" fill="url(#W)" transform="translate(1194.61 122.422)"/><path d="M1223.014 123.075l46.962 27.525c.476.28.675.972.444 1.548a1.08 1.08 0 0 1-.444.536l-23.611 13.908a3.794 3.794 0 0 1-3.911 0l-46.962-27.525c-.477-.28-.676-.972-.444-1.548a1.08 1.08 0 0 1 .444-.536l23.611-13.908a3.794 3.794 0 0 1 3.911 0z" fill="#0B0F89"/><path d="M28.368.627L73.923 27.69c.458.269.65.936.427 1.49a1.04 1.04 0 0 1-.427.515L52.11 42.55a3.649 3.649 0 0 1-3.761 0L2.205 15.818c-.458-.269-.65-.936-.427-1.49.09-.224.241-.406.427-.515L24.606.627a3.649 3.649 0 0 1 3.762 0z" fill="url(#X)" transform="translate(1194.61 122.422)"/><path d="M1219.833 131.37l-10.485 6.215a.42.42 0 0 0-.168.21c-.103.256-.015.563.168.67l33.834 19.83a2.372 2.372 0 0 0 2.451.002l10.486-6.215a.42.42 0 0 0 .168-.21c.103-.255.014-.563-.168-.67l-33.835-19.83a2.372 2.372 0 0 0-2.45-.001zm36.651 19.21c.505.296.703.987.473 1.561-.097.24-.262.44-.471.563L1246 158.918a3.094 3.094 0 0 1-3.183 0l-33.835-19.83c-.504-.297-.703-.988-.472-1.562.097-.24.262-.44.47-.562l10.487-6.215a3.094 3.094 0 0 1 3.183 0l33.834 19.83z" fill="#00BDFA" fill-rule="nonzero"/><path d="M14.06 6.092l26.196 15.277c.266.155.377.54.247.859a.6.6 0 0 1-.247.298l-2.846 1.66a2.126 2.126 0 0 1-2.182 0L9.03 8.907c-.266-.155-.377-.54-.248-.859a.6.6 0 0 1 .248-.298l2.846-1.66a2.126 2.126 0 0 1 2.182 0z" fill="url(#Y)" transform="translate(1208.09 129.918)"/><path d="M14.06 4.596l26.196 15.277c.266.155.377.54.247.86a.6.6 0 0 1-.247.297l-2.846 1.66a2.126 2.126 0 0 1-2.182 0L9.03 7.413c-.266-.155-.377-.54-.248-.86a.6.6 0 0 1 .248-.297v.085-.085l2.846-1.66a2.126 2.126 0 0 1 2.182 0z" fill="url(#Z)" transform="translate(1208.09 129.918)"/><path d="M63.747 31.638l1 .506c.099.058.14.201.092.32a.224.224 0 0 1-.093.112l-9.784 5.706a.794.794 0 0 1-.815 0l-1-.583c-.1-.058-.14-.124-.092-.244a.224.224 0 0 1 .092-.11l9.785-5.707a.794.794 0 0 1 .815 0z" fill="url(#aa)" transform="translate(1194.61 122.422)"/><g fill-rule="nonzero"><path d="M122.65 32.553H.214c.051 69.163.051 103.68 0 103.55-.284-.713-.04-1.572.545-1.918L59.657 99.74c1.516-.897 3.285-.897 4.8 0l57.648 34.111c.237.14.43.375.545.665.126.317.126-33.67 0-101.962z" fill="url(#ab)" opacity=".21" transform="translate(1118.681 110.484)"/><path d="M59.657 37.89H.214c.051 65.605.051 98.343 0 98.214-.284-.714-.04-1.573.545-1.919L59.657 99.74v-61.85z" fill="url(#ac)" opacity=".1" transform="translate(1118.681 110.484)"/><g transform="matrix(1 0 0 -1 1124.387 242.045)" fill="url(#ad)"><path d="M.228.311L7.7 4.58v101.932l-7.472-4.269zM20.002 12.153l7.472 4.27v101.932l-7.472-4.27zM39.75 23.894l7.473 4.27v101.932l-7.473-4.27zM111.095 1.103l-7.472 4.27v101.931l7.472-4.269zM91.32 12.945l-7.471 4.27v101.932l7.472-4.27zM71.573 24.686l-7.473 4.27v101.932l7.473-4.27z"/></g></g><path d="M.729 34.418l5.665 3.479c-.407.873-.615 1.798-.544 2.596l3.25 36.29c.531 5.916 2.855 10.328 6.503 12.651l-5.433-3.147.025.001c-3.836-2.264-6.291-6.751-6.836-12.843L.108 37.156c-.136-1.51.98-3.463.62-2.738z" fill="url(#ae)" opacity=".5" transform="translate(1158.564 161.424)"/><path d="M29.932 8.129L57.627.43c.517-.144.967-.093 1.32.117l5.691 3.307c-.345-.185-.776-.224-1.268-.087l-27.695 7.697-5.743-3.336z" fill="url(#af)" opacity=".5" transform="translate(1158.564 161.424)"/><path d="M6.394 37.897L.73 34.418c.375-.758.89-1.47 1.508-2.012L29.932 8.129l5.743 3.336L7.98 35.742c-.657.577-1.207 1.343-1.586 2.155z" fill="url(#ag)" opacity=".8" transform="translate(1158.564 161.424)"/><path d="M7.98 35.742c-1.364 1.196-2.267 3.211-2.13 4.751l3.25 36.29c.747 8.331 5.053 13.68 11.602 14.413l14.973 1.674 14.973-18.961c6.55-8.294 10.855-18.614 11.601-27.808L65.5 6.059c.138-1.698-.765-2.67-2.13-2.29l-27.694 7.696L7.98 35.742z" fill="url(#ah)" opacity=".8" transform="translate(1158.564 161.424)"/><path d="M15.022 38.93c-1.017.892-1.69 2.394-1.588 3.543l2.424 27.061c.557 6.214 3.767 10.203 8.651 10.75l11.166 1.247 11.166-14.14c4.884-6.184 8.094-13.88 8.651-20.737l2.424-29.86c.103-1.267-.57-1.992-1.588-1.708l-20.653 5.74L15.022 38.93z" fill="url(#ai)" transform="translate(1158.564 161.424)"/><path d="M1194.063 177.97l.08-.021 24.418-6.787c1.47-.41 2.474.67 2.334 2.397l-2.865 35.305c-.665 8.18-4.488 17.346-10.306 24.713l-13.326 16.875-13.401-1.498c-5.961-.667-9.876-5.53-10.548-13.035l-2.866-31.996c-.132-1.479.714-3.366 2-4.493l24.48-21.46zm.352.652l-24.357 21.351c-1.12.982-1.867 2.648-1.756 3.885l2.866 31.996c.644 7.189 4.321 11.758 9.91 12.383l13.002 1.453 13.078-16.56c5.732-7.26 9.5-16.291 10.152-24.325l2.866-35.304c.103-1.27-.487-1.904-1.422-1.644l-24.339 6.765z" fill="#00BDFA" fill-rule="nonzero"/><path d="M53.785 42.233l-8.904 5.239c-.38.223-.73.217-.917-.017L34.06 35.12c-.29-.362-.112-1.148.398-1.756l3.184-3.795c.33-.393.735-.631 1.062-.625l15.68.296c.328.006.528.256.526.655l-.057 10.594c-.004.617-.482 1.397-1.069 1.743zm.486 8.049L38.488 69.09c-.33.394-.735.632-1.062.626l-8.695-.164c-.506-.01-.677-.591-.38-1.298l4.495-10.73c.192-.457.544-.877.924-1.1l20.002-11.77c.587-.345 1.06-.125 1.057.493L54.808 49c-.002.4-.207.887-.537 1.28zM24.91 69.48l-3.163-.06c-.328-.006-.528-.256-.526-.655l.103-19.105c.002-.4.207-.888.537-1.28l8.751-10.43c.51-.608 1.16-.807 1.45-.445l4.408 5.49c.188.234.185.66-.006 1.118l-10.1 24.103c-.296.707-.947 1.273-1.454 1.264z" fill="url(#aj)" fill-rule="nonzero" transform="translate(1158.564 161.424)"/><g fill-rule="nonzero" opacity=".5"><path d="M122.815 46.1c.19 34.727.19 52.328 0 52.803-.115.29-.307.524-.545.665L64.623 133.68c-1.515.897-3.285.897-4.8 0L.924 99.233c-.586-.346-.83-1.205-.546-1.919.077-.193.077-17.265 0-51.215h122.436z" fill="url(#ak)" transform="translate(1118.516 147.765)"/><path d="M59.822 46.1v87.58L.925 99.232c-.586-.346-.83-1.205-.546-1.919.077-.193.077-17.265 0-51.215h59.443z" fill="url(#al)" transform="translate(1118.516 147.765)"/><path fill="url(#am)" d="M0 0l7.472 4.269v101.932L0 101.932z" transform="translate(1124.615 147.765)"/><path fill="url(#an)" d="M19.774 11.843l7.472 4.269v101.932l-7.472-4.269z" transform="translate(1124.615 147.765)"/><path fill="url(#ao)" d="M39.522 23.584l7.473 4.269v101.932l-7.473-4.27z" transform="translate(1124.615 147.765)"/><path fill="url(#am)" d="M0 0l7.472 4.269v101.932L0 101.932z" transform="matrix(-1 0 0 1 1235.482 148.557)"/><path fill="url(#an)" d="M19.774 11.843l7.472 4.269v101.932l-7.472-4.269z" transform="matrix(-1 0 0 1 1235.482 148.557)"/><path fill="url(#ao)" d="M39.522 23.584l7.473 4.269v101.932l-7.473-4.27z" transform="matrix(-1 0 0 1 1235.482 148.557)"/></g><g filter="url(#ap)" transform="translate(1190.625 249.06)"><path fill="url(#aq)" d="M4.288 9.902L0 7.427l4.288-2.476 4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#ar)" d="M8.577 7.427L4.288 4.951V0l4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#as)" d="M4.288 4.951L0 7.427V2.476L4.288 0z" transform="translate(0 .229)"/></g><g filter="url(#at)" transform="translate(1199.39 219.843)"><path fill="url(#aq)" d="M4.288 9.902L0 7.427l4.288-2.476 4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#ar)" d="M8.577 7.427L4.288 4.951V0l4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#as)" d="M4.288 4.951L0 7.427V2.476L4.288 0z" transform="translate(0 .229)"/></g><g filter="url(#au)" transform="rotate(-75 718.302 -664.667)"><path fill="url(#aq)" d="M4.288 9.902L0 7.427l4.288-2.476 4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#ar)" d="M8.577 7.427L4.288 4.951V0l4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#as)" d="M4.288 4.951L0 7.427V2.476L4.288 0z" transform="translate(0 .229)"/></g><g filter="url(#av)" transform="rotate(-30 1032.226 -2165.775)"><path fill="url(#aq)" d="M4.288 9.902L0 7.427l4.288-2.476 4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#ar)" d="M8.577 7.427L4.288 4.951V0l4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#as)" d="M4.288 4.951L0 7.427V2.476L4.288 0z" transform="translate(0 .229)"/></g><g filter="url(#aw)" transform="rotate(-60 785.923 -901.186)"><path fill="url(#aq)" d="M4.288 9.902L0 7.427l4.288-2.476 4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#ar)" d="M8.577 7.427L4.288 4.951V0l4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#as)" d="M4.288 4.951L0 7.427V2.476L4.288 0z" transform="translate(0 .229)"/></g><g filter="url(#ax)" transform="rotate(-105 678.265 -369.172)"><path fill="url(#aq)" d="M4.288 9.902L0 7.427l4.288-2.476 4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#ar)" d="M8.577 7.427L4.288 4.951V0l4.289 2.476z" transform="translate(0 .229)"/><path fill="url(#as)" d="M4.288 4.951L0 7.427V2.476L4.288 0z" transform="translate(0 .229)"/></g><g><path d="M75.874 29.064v78.449c.057.26.04.544-.064.804a1.08 1.08 0 0 1-.444.537L51.755 122.76a3.794 3.794 0 0 1-3.911 0L.882 95.236c-.296-.173-.485-.507-.53-.873h-.01V15.447l75.532 13.617z" fill="url(#V)" transform="translate(1102.828 176.598)"/><path d="M1111.568 264.41c.403.198.66.608.66 1.058v6.659a.392.392 0 0 1-.565.352l-1.226-.599a1.176 1.176 0 0 1-.66-1.057v-6.659a.392.392 0 0 1 .565-.352l1.226.599zm-3.79-2.086c.404.197.66.607.66 1.057v6.659a.392.392 0 0 1-.565.352l-1.226-.6a1.176 1.176 0 0 1-.66-1.056v-6.659a.392.392 0 0 1 .565-.352l1.226.599z" fill="#0B0F89"/><path d="M75.785 28.917v78.449c.057.26.04.544-.064.804a1.08 1.08 0 0 1-.444.537l-23.611 13.907a3.794 3.794 0 0 1-3.911 0v-95.29l28.03 1.593z" fill="url(#W)" transform="translate(1102.828 176.598)"/><path d="M1131.232 177.25l46.962 27.526c.476.28.675.972.444 1.548a1.08 1.08 0 0 1-.444.536l-23.611 13.908a3.794 3.794 0 0 1-3.911 0l-46.962-27.525c-.477-.28-.676-.972-.444-1.548a1.08 1.08 0 0 1 .444-.536l23.611-13.908a3.794 3.794 0 0 1 3.911 0z" fill="#0B0F89"/><path d="M28.368.627L73.923 27.69c.458.269.65.936.427 1.49a1.04 1.04 0 0 1-.427.515L52.11 42.55a3.649 3.649 0 0 1-3.761 0L2.205 15.818c-.458-.269-.65-.936-.427-1.49.09-.224.241-.406.427-.515L24.606.627a3.649 3.649 0 0 1 3.762 0z" fill="url(#X)" transform="translate(1102.828 176.598)"/><path d="M1128.052 185.547l-10.486 6.214a.42.42 0 0 0-.167.21c-.103.256-.015.563.167.67l33.835 19.83a2.372 2.372 0 0 0 2.451.002l10.486-6.215a.42.42 0 0 0 .167-.21c.103-.255.015-.563-.167-.67l-33.835-19.83a2.372 2.372 0 0 0-2.451-.001zm36.65 19.209c.506.296.704.987.473 1.561-.096.24-.261.44-.47.563l-10.486 6.214a3.094 3.094 0 0 1-3.183 0l-33.835-19.83c-.505-.296-.703-.988-.472-1.562.096-.24.261-.44.47-.562l10.486-6.215a3.094 3.094 0 0 1 3.183 0l33.835 19.83z" fill="#00BDFA" fill-rule="nonzero"/><path d="M14.06 6.092l26.196 15.277c.266.155.377.54.247.859a.6.6 0 0 1-.247.298l-2.846 1.66a2.126 2.126 0 0 1-2.182 0L9.03 8.907c-.266-.155-.377-.54-.248-.859a.6.6 0 0 1 .248-.298l2.846-1.66a2.126 2.126 0 0 1 2.182 0z" fill="url(#Y)" transform="translate(1116.309 184.094)"/><path d="M14.06 4.596l26.196 15.277c.266.155.377.54.247.86a.6.6 0 0 1-.247.297l-2.846 1.66a2.126 2.126 0 0 1-2.182 0L9.03 7.413c-.266-.155-.377-.54-.248-.86a.6.6 0 0 1 .248-.297v.085-.085l2.846-1.66a2.126 2.126 0 0 1 2.182 0z" fill="url(#Z)" transform="translate(1116.309 184.094)"/><path d="M63.747 31.638l1 .506c.099.058.14.201.092.32a.224.224 0 0 1-.093.112l-9.784 5.706a.794.794 0 0 1-.815 0l-1-.583c-.1-.058-.14-.124-.092-.244a.224.224 0 0 1 .092-.11l9.785-5.707a.794.794 0 0 1 .815 0z" fill="url(#aa)" transform="translate(1102.828 176.598)"/></g></g></g></svg>