<?php
/*
 * Template Name: About us
 */
?>

<?php get_header(); ?>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<style>
@font-face {
  font-family: "Montserrat";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-regular.woff2")
    format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD, U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
    U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: "Montserrat";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("https://vutruso.com/wp-content/themes/vutruso/fonts/google-fonts/montserrat-v15-vietnamese_latin-600.woff2")
    format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD, U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
    U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

:root {
  /* Colors from Vũ Trụ Số brand */
  --primary-dark: #035077;
  --primary-blue: #0c455c;
  --accent-blue: #0c49c4;
  --light-blue: #88edfe;
  --success-green: #65bc54;
  --text-primary: #1c2d3b;
  --text-secondary: #595959;
  --text-light: #666;
  --background-light: #f6f7f9;
  --background-gray: #f7f7f7;
  --background-section: #f8f8f8;

  /* Shadows from original site */
  --shadow-card: 0 15px 30px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 8px 24px rgba(86, 221, 243, 0.59);
  --shadow-light: 0 2px 3px rgb(21 30 41 / 10%), 0 0 0 1px rgb(21 30 41 / 10%);

  --border-radius: 8px;
  --border-radius-lg: 10px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Montserrat", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--background-gray);
  overflow-x: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 15px;
}

.slide {
  background: #fff;
  margin: 15px 0;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  page-break-after: always;
  position: relative;
  overflow: hidden;
}

.slide-content {
  padding: 30px 30px;
  position: relative;
  z-index: 2;
}

/* Hero Section - Based on original hero-chuyenhost */
.hero-section {
  background-color: var(--primary-dark);
  color: white;
  position: relative;
  padding: 3rem 3rem;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    /* overlay màu đen mờ */
      url(https://vutruso.com/wp-content/uploads/2022/05/vu-tru-so.jpg);
  background-size: auto 96vw;
  background-repeat: no-repeat;
  background-position: left -71vw bottom -22rem;
  opacity: 1;
  z-index: -1;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 60px;
  align-items: center;
}

.hero-text h1 {
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 1.375;
  margin-bottom: 2.5%;
  color: white;
}

.hero-text .subtitle {
  font-size: 1.1rem;
  line-height: 29px;
  margin-bottom: 3%;
}

.hero-text p {
  line-height: 29px;
  padding: 10px 0;
  color: white;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

/* Buttons - Original style */
.btn-custom {
  background-image: linear-gradient(
    to left top,
    #017bda,
    #9f6bd0,
    #e15aa8,
    #fd5f71,
    #f37f3a
  );
  color: #fff;
  position: relative;
  display: inline-block;
  font-weight: 500;
  text-align: center;
  text-transform: capitalize;
  height: 48px;
  line-height: 48px;
  border: none;
  outline: none;
  border-radius: 48px;
  padding: 0 20px;
  margin-right: 15px;
  margin-bottom: 15px;
  box-shadow: 0 10px 15px 0 rgba(56, 0, 189, 0.2);
  transition: 1.2s cubic-bezier(0.17, 0.85, 0.438, 0.99);
  overflow: hidden;
  cursor: pointer;
  text-decoration: none;
}

.btn-custom::before {
  background: rgba(255, 255, 255, 0.2);
  content: "";
  display: block;
  position: absolute;
  top: -10%;
  right: -130px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  transition: 1.2s cubic-bezier(0.17, 0.85, 0.438, 0.99);
}

.btn-custom:hover {
  color: #fff;
  text-decoration: none;
}

.btn-custom:hover::before {
  top: -10%;
  right: -80px;
}

.btn-custom2 {
  background-image: linear-gradient(to left, #017bda 0, #2196f3 100%);
}

.icon-angle-right-effect {
  margin-left: 5px;
  animation: 2s infinite ArrowAnimation;
  position: relative;
  top: -2px;
}

@keyframes ArrowAnimation {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(5px);
  }
}

/* Typography */
h1,
h2 {
  color: var(--primary-blue);
  font-weight: 700;
}

h2 {
  font-size: 1.8em;
  line-height: 1.25em;
  margin: 0 0 40px;
  position: relative;
  padding-bottom: 15px;
}

h2::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--accent-blue);
  border-radius: 2px;
  text-align: center;
}

.stats-section h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--accent-blue);
  border-radius: 2px;
}

h3 {
  font-size: 2.8em;
  font-weight: 100;
  text-transform: uppercase;
  line-height: 1.2em;
  color: var(--primary-blue);
  margin: 0 0 40px;
}

p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
}

/* Stats Section */
.stats-section {
  background: var(--text-primary);
  color: white;
  text-align: center;
  padding: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px 20px;
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-number {
  font-size: 3em;
  font-weight: 700;
  color: var(--light-blue);
  display: block;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1.1em;
  color: white;
}

/* Service Grid - Based on cta_panel_module */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(434px, 1fr));
  gap: 5px;
  margin: 40px 0;
  margin-bottom: 0;
}

.service-card {
  background: #fff;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.service-card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-5px);
}

.service-card-wrapper {
  display: flex;
  min-height: 245px;
  position: relative;
}

.service-image {
  background: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60%;
  padding: 30px;
  width: 25%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: var(--accent-blue);
}

.service-body {
  flex: 1;
  padding: 30px 25px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-body h4 {
  font-weight: 700;
  font-size: 1.4em;
  line-height: 1.2em;
  color: var(--primary-blue);
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.service-card:hover h4 {
  color: #61e2f7;
}

.service-content p {
  font-size: 1.1em;
  line-height: 1.5em;
  color: var(--text-light);
  margin-bottom: 20px;
}

.service-action {
  position: relative;
  padding: 16px 20px 18px;
  transition: all 0.2s ease;
  align-self: flex-end;
  border-radius: 28px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.service-action span {
  color: var(--primary-blue);
  position: relative;
  font-size: 17px;
  line-height: 1;
  font-weight: 700;
  vertical-align: middle;
}

.service-action:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 28px;
  background: var(--light-blue);
  width: 49px;
  height: 49px;
  transition: all 0.3s ease;
}

.service-card:hover .service-action::before {
  width: 100%;
  background: var(--light-blue);
}

/* Free Services */
.free-services .service-card {
  background: linear-gradient(
    135deg,
    rgba(101, 188, 84, 0.1),
    rgba(168, 230, 207, 0.1)
  );
  border: 2px solid rgba(101, 188, 84, 0.3);
}

.free-services .service-image {
  color: var(--success-green);
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin: 40px 0;
}

.project-card {
  background: #fff;
  padding: 35px 10px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-hover);
}

.project-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
  color: var(--accent-blue);
}

.project-icon svg {
  width: 80px;
  height: 80px;
  stroke: var(--accent-blue);
}

.project-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-blue);
  margin-bottom: 15px;
}

.project-desc {
  font-size: 1rem;
  color: var(--text-light);
  line-height: 1.5;
}

/* Quality Section - Based on circle-bg */
.quality-section {
  background: url(https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/bottom-circle.png)
    105% 190% no-repeat;
  background-color: rgba(195, 205, 225, 0.08);
  padding: 60px 40px;
}

.quality-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  align-items: center;
}

.quality-text h2 {
  font-size: 28px;
  border-bottom: 2px solid var(--accent-blue);
  padding-bottom: 8px;
  display: inline-block;
  margin-bottom: 20px;
}

.quality-text p {
  font-size: 16px;
  line-height: 27px;
  margin-bottom: 30px;
}

/* CTA Button - Based on btnAnimated */
.cta-btn {
  background: var(--accent-blue);
  color: #fff;
  border-radius: 100px;
  font-size: 15px;
  text-transform: uppercase;
  line-height: 49px;
  padding: 0 20px 0 45px;
  display: inline-block;
  position: relative;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
  border: none;
  cursor: pointer;
}

.cta-btn:before {
  position: absolute;
  display: block;
  content: "";
  width: 20px;
  height: 20px;
  top: 13px;
  left: 19px;
  border-radius: 50%;
  box-shadow: 0 0 0 0 #fff;
  transition: all 0.5s ease-in-out 0.2s;
}

.cta-btn:after {
  position: absolute;
  display: block;
  content: "";
  width: 22px;
  height: 22px;
  top: 13px;
  left: 18px;
  border-radius: 50%;
  box-shadow: inset 0 0 0 6px #fff;
}

.cta-btn:hover {
  background: #110f0f;
  color: #fff;
  text-decoration: none;
}

.cta-btn:hover:before {
  box-shadow: 0 0 0 30px rgba(0, 0, 0, 0);
}

.quality-cards {
  display: grid;
  gap: 15px;
  grid-template-columns: 1fr 1fr;
}

.quality-card {
  background: #fff;
  padding: 20px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);
  text-align: center;
  transition: all 0.3s ease;
}

.quality-card:hover {
  transform: translateY(-5px);
}

.quality-card img {
  width: 72px;
  height: 72px;
  margin-bottom: 20px;
}

.quality-card h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-blue);
  margin-bottom: 15px;
}

.quality-card p {
  font-size: 15px;
  line-height: 21px;
  color: var(--text-light);
}

/* Contact Section - Professional and beautiful */
.contact-section {
  background: linear-gradient(135deg, var(--text-primary) 0%, #34495e 100%);
  color: white;
  padding: 5rem 2rem 2rem;
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(240, 147, 251, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.contact-header {
  text-align: center;
  margin-bottom: 60px;
}

.contact-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.contact-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--light-blue), var(--success-green));
  border-radius: 2px;
}

.contact-subtitle {
  font-size: 1.3rem;
  color: #ecf0f1;
  margin-bottom: 30px;
  line-height: 1.6;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.contact-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(389px, 1fr));
  gap: 30px;
  margin: 50px 0;
}

.contact-feature {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 40px 30px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.contact-feature::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s;
}

.contact-feature:hover::before {
  left: 100%;
}

.contact-feature:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(136, 237, 254, 0.5);
}

.contact-feature i {
  font-size: 3rem;
  margin-bottom: 25px;
  display: block;
  background: linear-gradient(45deg, var(--light-blue), var(--success-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 2;
}

.contact-feature h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: white;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
}

.contact-feature p {
  color: #bdc3c7;
  line-height: 1.6;
  font-size: 1rem;
  position: relative;
  z-index: 2;
}

.contact-cta {
  text-align: center;
  margin-top: 60px;
}

.contact-main-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 30px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.contact-main-title::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(136, 237, 254, 0.1) 0%,
    rgba(101, 188, 84, 0.1) 50%,
    rgba(12, 73, 196, 0.1) 100%
  );
  z-index: 1;
}

.contact-main-title > * {
  position: relative;
  z-index: 2;
}

.contact-buttons {
  display: flex;
  justify-content: center;
  gap: 25px;
  flex-wrap: wrap;
  margin-top: 40px;
}

.contact-btn {
  background: linear-gradient(135deg, var(--success-green), #4caf50);
  color: #fff;
  padding: 0 2.5rem;
  min-width: 280px;
  display: inline-block;
  font-size: 1.4rem;
  border-radius: 60px;
  line-height: 65px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 30px rgba(101, 188, 84, 0.3);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.contact-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s;
}

.contact-btn:hover::before {
  left: 100%;
}

.contact-btn i {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  padding-right: 15px;
  margin-right: 15px;
  font-size: 1.2rem;
}

.contact-btn:hover {
  box-shadow: 0 12px 40px rgba(101, 188, 84, 0.4);
  color: #fff;
  text-decoration: none;
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.3);
}

.contact-btn.secondary {
  background: linear-gradient(135deg, var(--accent-blue), #2196f3);
  box-shadow: 0 8px 30px rgba(12, 73, 196, 0.3);
}

.contact-btn.secondary:hover {
  box-shadow: 0 12px 40px rgba(12, 73, 196, 0.4);
}

.contact-quote {
  font-style: italic;
  font-size: 1.2rem;
  margin-top: 40px;
  padding: 30px;
  border-left: 4px solid var(--light-blue);
  background: rgba(255, 255, 255, 0.08);
  border-radius: 0 15px 15px 0;
  backdrop-filter: blur(10px);
  position: relative;
  color: #fff;
}


/* Responsive Design */
@media (max-width: 768px) {
  h2 {
    font-size: 1.3em;
    line-height: 1.25em;
    margin-bottom: 15px;
  }
  .quality-card {
    padding: 10px;
  }
  .quality-card h4 {
    font-size: 14px;
  }
  .projects-grid {
    gap: 8px;
  }
  .quality-cards {
    gap: 8px;
  }
  .projects-grid {
    margin-bottom: 15px;
  }

  .contact-cta {
    margin-top: 10px;
  }

  .hero-section {
    padding: 2rem 1.5rem 4rem;
  }
  .hero-text h1 {
    font-size: 1.5rem;
  }
  .hero-text p {
    text-align: justify;
  }
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-card-wrapper {
    flex-direction: column;
    min-height: auto;
  }

  .service-image {
    width: 100%;
    padding: 20px;
    margin: 0 auto;
  }
  .service-action {
    align-self: flex-start;
  }

  .service-body {
    text-align: center;
    padding: 15px;
  }
  .service-action {
    align-self: center;
  }
  .quality-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .contact-features {
    grid-template-columns: 1fr;
    gap: 20px;
    margin: 30px 0;
  }

  .contact-title {
    font-size: 1.8rem;
  }

  .contact-subtitle {
    font-size: 1.1rem;
  }

  .contact-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .contact-btn {
    min-width: 100%;
    max-width: 320px;
    font-size: 1.2rem;
    line-height: 55px;
    padding: 0 1.5rem;
  }

  .contact-main-title {
    font-size: 1.4rem;
    padding: 25px;
  }

  .contact-quote {
    font-size: 1rem;
    padding: 20px;
  }

  .contact-btn {
    width: 240px;
    font-size: 1.2rem;
    line-height: 60px;
  }

  .slide-content {
    padding: 20px 15px;
    padding-bottom: 0;
    padding-bottom: 0;
  }
  .quality-section {
    padding: 30px 20px;
  }

  .quality-text h2 {
    font-size: 21px;
  }
  .project-card {
    padding: 15px 15px;
  }
}

@media (max-width: 480px) {
  .contact-title {
    font-size: 1.6rem;
  }

  .contact-subtitle {
    font-size: 1rem;
  }

  .contact-main-title {
    font-size: 1.2rem;
    padding: 20px;
  }

  .contact-quote {
    font-size: 0.95rem;
    padding: 15px;
  }

  .contact-btn {
    width: 100%;
    max-width: 280px;
    font-size: 0.9rem;
    line-height: 50px;
  }

  .contact-features {
    gap: 15px;
  }

  .contact-feature {
    padding: 30px 20px;
  }

  .contact-feature i {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
}

/* Print Styles */
@media print {
  body {
    background: white !important;
  }

  .slide {
    box-shadow: none;
    margin: 0;
    page-break-after: always;
  }

  .floating-nav {
    display: none;
  }

  .hero-section::before {
    display: none;
  }
}

/* Animation for elements on scroll */
.service-card,
.project-card,
.quality-card {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.6s ease;
}

.service-card:hover,
.project-card:hover,
.quality-card:hover {
  transform: translateY(-5px);
}

</style>
<div class="container">
   <!-- Slide 1: Hero Section -->
   <div class="slide">
      <div class="hero-section">
         <div class="hero-content">
            <div class="hero-text">
               <h1>Vũ Trụ Số - Chất lượng số!</h1>
               <div class="subtitle">
                  <p>Khởi đầu từ một ý tưởng vào ngày 19/06/2019, Vũ Trụ Số đã từng bước khẳng định vị thế của mình trong thế giới công nghệ. Đến ngày 27/06/2022, chúng tôi chính thức đăng ký kinh doanh với mã số thuế 0317358676, đánh dấu cột mốc quan trọng trong hành trình phát triển.</p>
                  <p>Chúng tôi không chỉ đơn thuần cung cấp các giải pháp công nghệ web và server - mà còn là người bạn đồng hành tin cậy, giúp doanh nghiệp chinh phục không gian số một cách hiệu quả nhất.</p>
                  <p>Chúng tôi tin rằng sự thành công của khách hàng chính là thành công của chúng tôi, vì vậy đội ngũ luôn sẵn sàng hỗ trợ 24/7 với tinh thần nhiệt huyết và chuyên nghiệp.</p>
                  <p>Nếu bạn là doanh nghiệp hoặc cá nhân cần <strong>thiết kế website</strong> xây dựng thương hiệu trên môi trường Internet và quảng cáo tiếp cận khách qua kênh online đừng ngần ngại liên hệ với chúng tôi để được hỗ trợ</p>
               </div>
               <div class="buttons" style="margin-top: 3%;">
                  <a href="#dich-vu" class="btn-custom">Dịch vụ khác <i class="fa fa-angle-right icon-angle-right-effect"></i></a>
                  <a href="#lien-he" class="btn-custom btn-custom2">Liên hệ với chúng tôi <i class="fa fa-angle-right icon-angle-right-effect"></i></a>
               </div>
            </div>
            <div class="hero-image">
               <img src="https://vutruso.com/wp-content/uploads/2021/11/vu-tru-so.webp" alt="Vũ Trụ Số - Dịch vụ WordPress" width="500" height="350">
            </div>
         </div>
      </div>
      <div class="stats-section">
         <h2 style="color: white; text-align: center;">Chúng tôi luôn sẵn sàng, bạn chỉ cần gửi yêu cầu cho chúng tôi.</h2>
         <div class="stats-grid">
            <div class="stat-card">
               <span class="stat-number">500+</span>
               <div class="stat-label">Dự án hoàn thành</div>
            </div>
            <div class="stat-card">
               <span class="stat-number">24/7</span>
               <div class="stat-label">Hỗ trợ kỹ thuật</div>
            </div>
            <div class="stat-card">
               <span class="stat-number">100%</span>
               <div class="stat-label">Cam kết hoàn tiền</div>
            </div>
         </div>
      </div>
   </div>
   <!-- Slide 2: Work Process -->
   <div class="slide">
      <div class="slide-content">
         <h2>Tóm tắt quy trình làm việc tại Vũ Trụ Số</h2>
         <div class="services-grid" style="grid-template-columns: repeat(auto-fit, minmax(271px, 1fr));">
            <div class="project-card">
               <div class="project-icon">
                  <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                     <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                     <polyline points="14,2 14,8 20,8"/>
                     <line x1="16" y1="13" x2="8" y2="13"/>
                     <line x1="16" y1="17" x2="8" y2="17"/>
                     <polyline points="10,9 9,9 8,9"/>
                  </svg>
               </div>
               <div class="project-title">Tiếp nhận thông tin</div>
               <div class="project-desc">Vũ Trụ Số tiếp nhận thông tin từ khách hàng sau đó tư vấn và trao đổi với khách hàng để thống nhất phương án ban đầu</div>
            </div>
            <div class="project-card">
               <div class="project-icon">
                  <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                     <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                     <polyline points="14,2 14,8 20,8"/>
                     <path d="M12 18v-6l3 3"/>
                     <path d="M12 12l-3 3"/>
                  </svg>
               </div>
               <div class="project-title">Ký kết hợp đồng</div>
               <div class="project-desc">Sau khi thống nhất phương án và báo giá, hai bên ký kết hợp đồng chính thức, khách hàng đặt cọc và bắt đầu triển khai dự án</div>
            </div>
            <div class="project-card">
               <div class="project-icon">
                  <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                     <polyline points="16 18 22 12 16 6"/>
                     <polyline points="8 6 2 12 8 18"/>
                     <line x1="12" y1="2" x2="12" y2="22"/>
                  </svg>
               </div>
               <div class="project-title">Triển khai dự án</div>
               <div class="project-desc">Với nhiều biện pháp nghiệp vụ chúng tôi sẽ tiến hành thiết kế web, nâng cấp, gỡ mã độc... đáp ứng mọi yêu cầu theo đúng tiến độ đặt ra</div>
            </div>
            <div class="project-card">
               <div class="project-icon">
                  <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                     <path d="M9 12l2 2 4-4"/>
                     <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                     <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                     <path d="M13 12h3a2 2 0 0 1 2 2v1"/>
                     <path d="M11 12H8a2 2 0 0 0-2 2v1"/>
                  </svg>
               </div>
               <div class="project-title">Bàn giao dự án</div>
               <div class="project-desc">Khách hàng nghiệm thu và chúng tôi sẽ bàn giao mọi thông tin cũng như chia sẻ một số lời khuyên bổ ích nhất cho cá nhân hoặc doanh nghiệp</div>
            </div>
         </div>
      </div>
   </div>
   <!-- Slide 3: Main Services -->
   <div class="slide">
      <div class="slide-content" id="dich-vu">
         <h2>Các dịch vụ tại</h2>
         <h3>Vũ Trụ Số</h3>
         <div class="services-grid">
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-paint-brush"></i>
                  </div>
                  <div class="service-body">
                     <h4>Dịch vụ thiết kế website</h4>
                     <div class="service-content">
                        <p>Thiết kế website chuyên nghiệp, hiện đại, responsive và tối ưu trải nghiệm người dùng cho mọi thiết bị</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-tachometer-alt"></i>
                  </div>
                  <div class="service-body">
                     <h4>Dịch vụ tối ưu WordPress</h4>
                     <div class="service-content">
                        <p>Tối ưu tốc độ tải trang dưới 3 giây, cải thiện hiệu suất và SEO, giúp tăng chuyển đổi</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-shield-virus"></i>
                  </div>
                  <div class="service-body">
                     <h4>Xử lý virus, mã độc WordPress</h4>
                     <div class="service-content">
                        <p>Quét và loại bỏ mã độc hoàn toàn, bảo vệ website khỏi các mối đe dọa bảo mật và hack</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-lock"></i>
                  </div>
                  <div class="service-body">
                     <h4>Bảo mật website WordPress</h4>
                     <div class="service-content">
                        <p>Tăng cường bảo mật toàn diện cho website WordPress, ngăn chặn 99% cuộc tấn công</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-search-plus"></i>
                  </div>
                  <div class="service-body">
                     <h4>Sửa lỗi Technical SEO website</h4>
                     <div class="service-content">
                        <p>Tối ưu SEO kỹ thuật chuyên sâu, cải thiện thứ hạng Google và tăng traffic organic</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-tools"></i>
                  </div>
                  <div class="service-body">
                     <h4>Sửa lỗi, nâng cấp WordPress</h4>
                     <div class="service-content">
                        <p>Khắc phục mọi lỗi WordPress, nâng cấp phiên bản và tối ưu hiệu suất toàn diện</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-server"></i>
                  </div>
                  <div class="service-body">
                     <h4>Hosting WordPress được quản lý</h4>
                     <div class="service-content">
                        <p>Hosting được quản lý chuyên biệt cho WordPress, đảm bảo uptime 99.9% và hiệu suất cao</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-business-time"></i>
                  </div>
                  <div class="service-body">
                     <h4>Quản trị website cho Doanh Nghiệp</h4>
                     <div class="service-content">
                        <p>Dịch vụ quản trị website chuyên nghiệp cho doanh nghiệp với đội ngũ 24/7</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-chart-line"></i>
                  </div>
                  <div class="service-body">
                     <h4>Sửa lỗi Google search console</h4>
                     <div class="service-content">
                        <p>Khắc phục lỗi Google Search Console và cải thiện chỉ số Core Web Vitals</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-cloud-upload-alt"></i>
                  </div>
                  <div class="service-body">
                     <h4>Quản lý máy chủ ảo - VPS</h4>
                     <div class="service-content">
                        <p>Quản lý và vận hành máy chủ ảo VPS chuyên nghiệp, đảm bảo bảo mật và hiệu suất</p>
                     </div>
                     <button class="service-action">
                     <span>Xem thêm</span>
                     </button>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <!-- Slide 4: Free Services -->
   <div class="slide free-services">
      <div class="slide-content">
         <h2><i class="fas fa-gift"></i> Dịch Vụ Miễn Phí</h2>
         <div class="services-grid">
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-magic"></i>
                  </div>
                  <div class="service-body">
                     <h4>Cài đặt Theme WordPress</h4>
                     <div class="service-content">
                        <p>Cài đặt theme WordPress giống web demo hoàn toàn miễn phí, bao gồm cả data mẫu và customization cơ bản.</p>
                     </div>
                     <button class="service-action">
                     <span>Miễn phí</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-exchange-alt"></i>
                  </div>
                  <div class="service-body">
                     <h4>Chuyển Host WordPress</h4>
                     <div class="service-content">
                        <p>Hỗ trợ chuyển host cho website WordPress một cách an toàn, nhanh chóng và không downtime.</p>
                     </div>
                     <button class="service-action">
                     <span>Miễn phí</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-map-marker-alt"></i>
                  </div>
                  <div class="service-body">
                     <h4>Khai báo Google Map</h4>
                     <div class="service-content">
                        <p>Hỗ trợ khai báo và xác minh Google My Business, Google Map cho doanh nghiệp hoàn toàn miễn phí.</p>
                     </div>
                     <button class="service-action">
                     <span>Miễn phí</span>
                     </button>
                  </div>
               </div>
            </div>
            <div class="service-card">
               <div class="service-card-wrapper">
                  <div class="service-image">
                     <i class="fas fa-comments"></i>
                  </div>
                  <div class="service-body">
                     <h4>Tư vấn Website & Hosting</h4>
                     <div class="service-content">
                        <p>Tư vấn miễn phí các vấn đề về website, hosting, domain và giải pháp công nghệ phù hợp cho doanh nghiệp.</p>
                     </div>
                     <button class="service-action">
                     <span>Miễn phí</span>
                     </button>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <!-- Slide 5: Featured Projects -->
   <div class="slide">
      <div class="slide-content">
         <h2><i class="fas fa-trophy"></i> Các Dự Án Nổi Bật</h2>
         <div class="projects-grid">
            <div class="project-card">
               <span class="project-icon">🏢</span>
               <div class="project-title">Tập đoàn Khang Điền</div>
               <div class="project-desc">Website tập đoàn bất động sản</div>
            </div>
            <div class="project-card">
               <span class="project-icon">☕</span>
               <div class="project-title">Mr.Phin</div>
               <div class="project-desc">Website thương mại điện tử</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🏷️</span>
               <div class="project-title">Tem nhãn 3A</div>
               <div class="project-desc">Website bán tem nhãn chuyên nghiệp</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🏭</span>
               <div class="project-title">Công ty G3</div>
               <div class="project-desc">Website doanh nghiệp</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🌌</span>
               <div class="project-title">Kosmos</div>
               <div class="project-desc">Cung cấp và phân phối vật liệu trang trí nội, ngoại thất</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🏗️</span>
               <div class="project-title">Đức Phát</div>
               <div class="project-desc">Website xây dựng</div>
            </div>
            <div class="project-card">
               <span class="project-icon">💄</span>
               <div class="project-title">Mỹ phẩm QCONE</div>
               <div class="project-desc">Website thương mại điện tử mỹ phẩm</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🏘️</span>
               <div class="project-title">BDS 3CLand</div>
               <div class="project-desc">Website bất động sản</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🚛</span>
               <div class="project-title">Logistics Toàn cầu</div>
               <div class="project-desc">Website vận chuyển quốc tế</div>
            </div>
            <div class="project-card">
               <span class="project-icon">🥖</span>
               <div class="project-title">Sơn Nguyễn Bakery</div>
               <div class="project-desc">Website giới thiệu và bán sản phẩm bánh mì</div>
            </div>
            <div class="project-card">
               <span class="project-icon">⚙️</span>
               <div class="project-title">Schsteel</div>
               <div class="project-desc">Website giới thiệu và cung cấp thép</div>
            </div>
         </div>
      </div>
   </div>
   <!-- Slide 6: Quality Commitment -->
   <div class="slide">
      <div class="quality-section">
         <div class="quality-content">
            <div class="quality-text">
               <h2><i class="fas fa-medal"></i> Chất lượng được đảm bảo</h2>
               <p>Trong thời đại số hóa ngày nay, website không chỉ là một công cụ hiển thị thông tin mà còn là trái tim của hoạt động kinh doanh. Hiểu được tầm quan trọng này<br/><br/>
                  Vũ Trụ Số cam kết mang đến cho khách hàng những dịch vụ chất lượng cao nhất.
               </p>
               <a href="#lien-he" class="cta-btn">Liên hệ với chúng tôi ngay</a>
            </div>
            <div class="quality-cards">
               <div class="quality-card">
                  <img src="https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/convert-file.svg" alt="Dữ liệu được đảm bảo 100%" width="72" height="72">
                  <h4>Dữ liệu<br/> đảm bảo 100%</h4>
                  <p>Dữ liệu được an toàn 100% và được bảo mật tuyệt đối</p>
               </div>
               <div class="quality-card">
                  <img src="https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/theme-customize.svg" alt="Mọi yêu cầu đều được đáp ứng" width="72" height="72">
                  <h4>Mọi yêu cầu<br/> đều được đáp ứng</h4>
                  <p>Chúng tôi sẽ giải quyết mọi yêu cầu từ bạn</p>
               </div>
            </div>
         </div>
      </div>
   </div>
   <!-- Slide 8: Contact -->
   <div class="slide">
      <div class="contact-section" id="lien-he">
         <div class="contact-container">
            <div class="contact-header">
               <h2 class="contact-title">📞 Liên Hệ Ngay Hôm Nay!</h2>
               <p class="contact-subtitle">Đừng để website của bạn gặp phải những vấn đề kỹ thuật ảnh hưởng đến hoạt động kinh doanh.<br>Hãy để Vũ Trụ Số trở thành đối tác tin cậy của bạn trong hành trình số hóa!</p>
            </div>
            <div class="contact-features">
               <div class="contact-feature">
                  <i class="fas fa-users"></i>
                  <h4>Đội ngũ chuyên gia</h4>
                  <p>Nhiều năm kinh nghiệm trong lĩnh vực WordPress và web development, luôn cập nhật công nghệ mới nhất</p>
               </div>
               <div class="contact-feature">
                  <i class="fas fa-rocket"></i>
                  <h4>Công nghệ hiện đại</h4>
                  <p>Áp dụng những công nghệ và quy trình tiên tiến nhất, đảm bảo website hoạt động tối ưu</p>
               </div>
               <div class="contact-feature">
                  <i class="fas fa-dollar-sign"></i>
                  <h4>Giá cả hợp lý</h4>
                  <p>Chất lượng cao với mức giá cạnh tranh, minh bạch và không phát sinh chi phí</p>
               </div>
               <div class="contact-feature">
                  <i class="fas fa-headset"></i>
                  <h4>Hỗ trợ 24/7</h4>
                  <p>Luôn sẵn sàng hỗ trợ khách hàng mọi lúc, phản hồi nhanh chóng và xử lý sự cố kịp thời</p>
               </div>
            </div>
            <div class="contact-cta">
               <div class="contact-main-title">
                  <i class="fas fa-star"></i> Vũ Trụ Số – Đối tác tin cậy cho mọi giải pháp WordPress và quản trị website! <i class="fas fa-star"></i>
               </div>
               <div class="contact-buttons">
                  <a class="contact-btn" href="https://zalo.me/0868017791">
                  <i class="fab fa-whatsapp"></i> WhatsApp: 0868 01 77 91
                  </a>
                  <a class="contact-btn secondary" href="tel:0868017791">
                  <i class="fas fa-phone"></i> Gọi ngay: 0868 01 77 91
                  </a>
               </div>
               <p class="contact-quote">
                  <i class="fas fa-quote-left"></i> Cam kết của chúng tôi là sự thành công của bạn. <br>Chất lượng dịch vụ không chỉ là lời hứa, mà là trách nhiệm chúng tôi phải thực hiện. <i class="fas fa-quote-right"></i>
               </p>
            </div>
         </div>
      </div>
   </div>
</div>

<?php get_footer(); ?>
