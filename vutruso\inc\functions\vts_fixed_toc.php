<?php

// <PERSON>àm thêm anchor v<PERSON><PERSON> tiêu đề
function add_anchor_to_title($content) {   
    if (is_singular(['post', 'page']) && !is_front_page()) {
        $pattern = "/<h([2-6])(.*?)>(.*?)<\/h([2-6])>/i";
        $content = preg_replace_callback($pattern, function($matches) {
            $level = $matches[1];
            $attrs = $matches[2];
            $title = strip_tags($matches[3]);
            
            // Tạo ID an toàn với tiền tố 'heading-'
            $id = 'vts-' . sanitize_title($title);
            
            return sprintf(
                '<h%s%s id="%s">%s</h%s>',
                $level,
                $attrs,
                $id,
                $matches[3],
                $level
            );
        }, $content);
    }
    return $content;
}

remove_filter('the_content', 'add_anchor_to_title');
add_filter('the_content', 'add_anchor_to_title', 12);

// Function tạo TOC
function vts_toc($echo = true) {
    if (!is_singular(['post', 'page']) || is_front_page()) {
        return;
    }

    global $post;
    if (!$post || !isset($post->post_content)) {
        return;
    }

    $original_content = $post->post_content;
    $patt = "/<h([2-6])(.*?)>(.*?)<\/h([2-6])>/i";
    
    if (!preg_match_all($patt, $original_content, $results)) {
        return;
    }

    $headings = array();
    foreach ($results[0] as $key => $value) {
        $level = $results[1][$key];
        $title = strip_tags($results[3][$key]);
        
        $headings[] = array(
            'level' => $level,
            'title' => $title
        );
    }

    // Kiểm tra nếu số heading nhỏ hơn 3 thì không tạo TOC
    if (count($headings) < 3) {
        return;
    }

    $output = '<div id="vts-toc-container" class="vts-toc-container show-button">';    
    $output .= '<p class="toc_title" style="margin-left: 19px;">Sơ đồ trang</p>';
    $output .= '<ol id="vts-toc-article">';
    $current_level = 0;
    $level_stack = array();

    foreach ($headings as $heading) {
        $level = (int)$heading['level'];
        $title = $heading['title'];
        
        $id = 'vts-' . sanitize_title($title);
        
        if ($current_level == 0) {
            $current_level = $level;
        } else if ($level > $current_level) {
            $output .= '<ul>';
            array_push($level_stack, $current_level);
            $current_level = $level;
        } else if ($level < $current_level) {
            while (!empty($level_stack) && $level < $current_level) {
                $output .= '</ul></li>';
                $current_level = array_pop($level_stack);
            }
        } else {
            $output .= '</li>';
        }

        $output .= sprintf(
            '<li><a href="#%s">%s</a>',
            esc_attr($id),
            esc_html($title)
        );
    }

    while (!empty($level_stack)) {
        $output .= '</ol></li>';
        array_pop($level_stack);
    }

    $output .= '</ol>';
    $output .= '<button id="toggle-read-more" class="read-more-btn">Xem thêm</button>';
    $output .= '</div>';

    if ($echo) {
        echo $output;
        return;
    }
    return $output;
}

add_shortcode('vts_toc', 'vts_toc');

// Thêm JavaScript (giữ nguyên)
function vts_add_js() {
    if (!is_singular(['post', 'page']) || is_front_page()) {
        return;
    }
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (window.location.hash) {
            setTimeout(function() {
                window.scrollTo(0, 0);
            }, 1);
        }

        const tocLinks = document.querySelectorAll('#vts-toc-article a');
        if (tocLinks) {
            tocLinks.forEach(function(anchor) {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const hash = this.getAttribute('href');
                    const target = document.querySelector(hash);

                    if (target) {
                        const offset = target.getBoundingClientRect().top + window.pageYOffset - 20;
                        window.scrollTo({
                            top: offset,
                            behavior: 'smooth'
                        });
                        history.pushState(null, null, hash);
                    }
                });
            });
        }

        const toggleButton = document.getElementById('toggle-read-more');
        if (toggleButton) {
            toggleButton.addEventListener('click', function() {
                const container = document.getElementById('vts-toc-container');
                if (container) {
                    container.classList.toggle('show');
                    this.textContent = container.classList.contains('show') ? 'Thu gọn' : 'Xem thêm';
                }
            });
        }
    });
    </script>
    <?php
}
//add_action('wp_footer', 'vts_add_js', 9999);