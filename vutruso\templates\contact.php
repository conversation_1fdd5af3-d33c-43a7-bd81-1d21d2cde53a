<?php
/*
 * Template Name: Contact
 */
?>
<?php get_header(); ?>

<div class="container page-normal">
<div class="contacts-form-page">
  <div class="form-position">

    <div class="contacts-form-page_grid">
      <div role="form" class="wpcf7"><?php echo do_shortcode('[contact-form-7 id="3354" title="Page liên hệ"]') ?></div>
      <div class="contacts-info">
        <h1><PERSON>ên hệ với Vũ T<PERSON> Số</h1>
        <ul>
          <li class="address"><span><b>Địa chỉ:</b> 84/47/12 Bùi <PERSON>uang Là - Gò Vấp</span></li>
          <li class="shedulle">Giờ mở cửa: 8:00 - 17h:00</li>
          <li class="phones">
            <a href="tel:+84868017791">(+84) 868.017.791</a>
          </li>
          <li class="mail"><EMAIL></li>
        </ul>
      </div>
    </div>

  </div>
</div>
</div>


<style type="text/css" media="screen">
.wpcf7-form-control-wrap input:placeholder{color:#585858;font-weight:300;}
.wpcf7-form-control-wrap input{border:none;box-shadow:none;width:100%;}
input.wpcf7-form-control.wpcf7-text{display:inline-block;}
input.wpcf7-form-control.wpcf7-text:focus{border:none;outline:0;}
input.wpcf7-form-control.wpcf7-submit{margin-top:-2px;padding:5px 40px;font-size:18px;box-shadow:0 10px 20px 0 rgba(0,67,91,.25);color:#fff;border-radius:50px;text-transform:none;font-weight:300;line-height:34px;}
input.wpcf7-form-control.wpcf7-submit:hover{box-shadow:none;background:0 0;color:#121212;border:1px solid #ff1569;text-transform:none;}
input[type=email],input[type=tel],input[type=text],textarea{background:0 0;}
input[type=email],input[type=tel],input[type=text]{padding-left:9px;height:34px;line-height:32px;max-width:100%;}
textarea{padding:10px;max-width:100%;}
.wpcf7-form-control-wrap input{font-size:16px;height:45px;display:block;box-shadow:none;box-sizing:border-box;}
input.wpcf7-form-control.wpcf7-submit{margin-top:-2px;outline:0;}
.wpcf7-form{position:relative;}
.form-position{position:relative;padding:0 0 40px 0;}
.contacts-form-page .form{display:grid;grid-template-columns:1fr 1fr;grid-column-gap:30px;padding:50px;}
.contacts-form-page .form>div:first-child{grid-column:1/3;position:relative;background:url(<?php echo get_template_directory_uri(); ?>/img/icon/mail-send.svg) no-repeat;background-size:50px auto;background-position:right -8px;}
.contacts-form-page .form>div:first-child h1,.contacts-info h1{font-size:20px;font-weight:400;    color: #2050a5;line-height: 33px;}
.contacts-form-page .form>div:last-child{grid-column:1/3;position:relative;}
.contacts-form-page .form>div:last-child>div.button{position:absolute;top:186px;left:0;width:80px;z-index:1;}
.contacts-form-page .form label{color:#7b8187;font-size:13px;font-weight:300;}
.contacts-form-page .form .wpcf7-form-control-wrap input{background:#70b5e800 !important; padding:0!important;color:#344a5e!important;width:100%;margin-bottom:0;height:auto;padding:5px 0;color: #7b8187;font-size: 16px!important;}
.contacts-form-page .form .wpcf7-form-control-wrap textarea{width:100%;height:150px;border:0;padding:5px 0;background: #70b5e800 !important;color: #7b8187;font-size: 16px!important;}
.contacts-form-page .form input[type=submit]{background:#0048ff;background-position-x:0;background-position-y:0;background-size:auto auto;background-position:center;border:none;background-size:26px 26px;border-radius:0;color:#fff;font-size:14px;padding:0;width:115px;line-height:46px;z-index:1;border-radius: 4px;padding: 0 !important;
font-size: 62px;
font-weight: bold;cursor: pointer;}
.wpcf7-response-output{
  margin: 0 50px!important
}

.contacts-form-page .form>div{position:relative;margin-bottom:20px;}
.contacts-form-page .form>div:first-child::after,.contacts-form-page .form>div:first-child:before{background:0 0;margin-bottom:0;}
.contacts-form-page .form>div:last-child::after,.contacts-form-page .form>div:last-child:before{margin-bottom:0;}
.contacts-form-page .form>div:after{width:100%;background:#eef0f3;height:1px;position:absolute;content:'';transition:.7s all;bottom:0;left:0;width:100%;z-index:0;}
.contacts-form-page .form>div:before{width:100%;background:#0048ff;height:2px;position:absolute;content:'';transition:.7s all;bottom:0;left:0;width:0%;z-index:0;}
.contacts-form-page .wpcf7-form-control-wrap{position:initial;}
.contacts-form-page .form>div:last-child:after{width:98%;}
.contacts-form-page_grid{display:grid;grid-template-columns:1fr minmax(300px,519px);background:#fff;
    box-shadow: 1px 11px 23px 4px #f5fafd;}
.contacts-form-page_grid>div:last-child{background-image: linear-gradient(to right top, #0d47a1, #174ba3, #1e4fa5, #2553a6, #2b57a8, #1d61ae, #0d6bb2, #0075b6, #0086b8, #0095b5, #1ca2af, #4eaea8);;color:#fff;padding: 49px 5px 5px 17px}
.contacts-info h1{color:#fff;    margin-bottom: 8%;}
.contacts-info ul{list-style:none;margin:0;padding:0;}
.contacts-info ul li{font-size: 14px; padding: 13px 0 10px 35px; font-weight: 300; list-style-type: none !important; margin: 10px 4px;}
.contacts-info ul b{font-weight:600;}
.contacts-info ul li span{display:block;}
.contacts-info ul li a{color:#fff;text-decoration:none;display:inline-block;}
.contacts-info ul li.address{background:url(<?php echo get_template_directory_uri(); ?>/img/icon/icon-location-white.svg) 0 10px no-repeat;background-size:24px;}
.contacts-info ul li.shedulle{background:url(<?php echo get_template_directory_uri(); ?>/img/icon/icon-clock-white.svg) 0 10px no-repeat;background-size:24px;}
.contacts-info ul li.phones{background:url(<?php echo get_template_directory_uri(); ?>/img/icon/icon-call-white.svg) 0 10px no-repeat;background-size:24px;}
.contacts-info ul li.mail{background:url(<?php echo get_template_directory_uri(); ?>/img/icon/icon-mail-white.svg) 0 10px no-repeat;background-size:24px;}
div.wpcf7 .screen-reader-response{position:absolute;overflow:hidden;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;margin:0;padding:0;border:0;}
div.wpcf7-response-output{margin:2em 0.5em 1em;padding:0.2em 1em;border:2px solid #ff0000;}
.wpcf7-form-control-wrap{position:relative;}
div.wpcf7 .ajax-loader {
    position: absolute;
    left: 52%;
    top: 51%;
    transform: translate(-50%, -50%);
    background-color: rgb(245, 243, 79);
    width: auto;
    min-width: 80px;
    background-image: url(https://vutruso.com/wp-content/themes/vutruso/img/icon/loading_gray.svg);
    background-size: 36px 36px;
    margin: 0px;
    background-repeat: no-repeat;
    background-position: center center;
}
.contacts-form-page .form .input-effect label {color: #0048ff;transition: .7s all}
.contacts-form-page .form .input-effect .wpcf7-form-control-wrap input,
.contacts-form-page .form .input-effect .wpcf7-form-control-wrap textarea {transition: .7s all}
.contacts-form-page .form > div.input-effect:before {background: #0048ff; left: 0; width: 100%; height: 2px; z-index: 2 }
.contacts-form-page .form > div:last-child.input-effect:before,
.contacts-form-page .form > div:last-child:after {width: 98%}
.contacts-form-page_grid span{margin:0;padding:0;border:0;font-size:100%;vertical-align:baseline;background:0 0;}
span.wpcf7-not-valid-tip{position:absolute;margin:0;top:0;right:20px;}
span.wpcf7-not-valid-tip{background:#ff1569;font-size:12px;color:#fff;padding:2px 5px;}
span.wpcf7-not-valid-tip::before{content:'';border:11px solid transparent;border-right:10px solid #ff1569;position:absolute;left:-21px;top:0;}
.wpcf7-not-valid-tip{margin:10px -20px 0 0;display:inline-block;}
.contacts-form-page .wpcf7-not-valid-tip{position:absolute;right:0;top:29px;display:inline-block!important;}
.contacts-form-page .wpcf7-not-valid-tip{margin:0;color:#fff;font-size:12px;}
span.wpcf7-not-valid-tip{font-size:1em;font-weight:normal;display:block;}

@media (max-width:992px){
  .contacts-form-page_grid{grid-template-columns:1fr;}
}

@media (max-width:768px){
  .contacts-form-page .form{display:grid;grid-template-columns:1fr;padding:40px 20px;margin-bottom: 10%}
  .contacts-form-page_grid>div:last-child{padding:40px 20px;}
  .contacts-form-page .form>div{grid-column:1/3;grid-gap:0;}
  .form-position{padding:0 0 40px 0;}
}

@media (max-width:560px){
  .form-position{padding:0 0;}
}

.input-effect .wpcf7-spinner:before{
    position: absolute;
    left: 52%;
    top: 51%;
    transform: translate(-50%, -50%);
    background-color: rgb(245, 243, 79);
    width: auto;
    min-width: 80px;
    background-image: url(https://vutruso.com/wp-content/themes/vutruso/img/icon/loading_gray.svg);
    background-size: 36px 36px;
    margin: 0px;
    background-repeat: no-repeat;
    background-position: center center;
}
</style>
<script type="text/javascript">
  jQuery(document).ready(function($) {
    $(".wpcf7-form-control").focus(function() {
          $(this).closest("div").addClass("input-effect")
      }).blur(function() {
          $(this).closest("div").removeClass("input-effect")
      });
  });    
</script>
<?php get_footer(); ?>