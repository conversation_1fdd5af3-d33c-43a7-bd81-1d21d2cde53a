<?php
/**
 * Login Form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/form-login.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 4.1.0
 */

 // Ensure the code is not accessed directly.
 if ( ! defined( 'ABSPATH' ) ) {
	 exit;
 }
 
 do_action( 'woocommerce_before_customer_login_form' );
 ?>
 
 <!-- HTML structure -->
 <div class="container login-container">
	 <div class="row">
		 <!-- Login Form -->
		 <div class="col-md-6 login-form-1">
			 <h3><?php esc_html_e( 'Login', 'woocommerce' ); ?></h3>
			 <form class="login" method="post">
				 <!-- Hook for additional actions at the start of the login form -->
				 <?php do_action( 'woocommerce_login_form_start' ); ?>
 
				 <!-- Username Input -->
				 <div class="form-group">
					 <input type="text" class="form-control" name="username" id="username"
							placeholder="<?php esc_html_e( 'Username or email address', 'woocommerce' ); ?> *"
							autocomplete="username"
							value="<?php echo ( ! empty( $_POST['username'] ) ) ? esc_attr( wp_unslash( $_POST['username'] ) ) : ''; ?>" />
				 </div>
 
				 <!-- Password Input -->
				 <div class="form-group">
					 <input type="password" class="form-control" name="password" id="password"
							placeholder="<?php esc_html_e( 'Password', 'woocommerce' ); ?> *"
							autocomplete="current-password" />
				 </div>
 
				 <?php do_action( 'woocommerce_login_form' ); ?>
 
				 <!-- Remember Me Checkbox -->
				 <div class="form-group">
					 <label class="woocommerce-form__label woocommerce-form__label-for-checkbox woocommerce-form-login__rememberme">
						 <input class="woocommerce-form__input woocommerce-form__input-checkbox" name="rememberme"
								type="checkbox" id="rememberme" value="forever" />
						 <span><?php esc_html_e( 'Remember me', 'woocommerce' ); ?></span>
					 </label>
					 <?php wp_nonce_field( 'woocommerce-login', 'woocommerce-login-nonce' ); ?>
				 </div>
 
				 <!-- Login Button -->
				 <div class="form-group">
					 <button type="submit" class="btnSubmit" name="login"
							 value="<?php esc_attr_e( 'Log in', 'woocommerce' ); ?>"><?php esc_html_e( 'Log in', 'woocommerce' ); ?></button>
				 </div>
 
				 <!-- Forgot Password Link -->
				 <div class="form-group">
					 <a class="ForgetPwd" href="<?php echo esc_url( wp_lostpassword_url() ); ?>">
						 <?php esc_html_e( 'Lost your password?', 'woocommerce' ); ?>
					 </a>
				 </div>
 
				 <?php do_action( 'woocommerce_login_form_end' ); ?>
			 </form>
		 </div>
 
		 <!-- Registration Form -->
		 <div class="col-md-6 login-form-2">
			 <h3><?php esc_html_e( 'Register', 'woocommerce' ); ?></h3>
			 <form method="post" class="register" <?php do_action( 'woocommerce_register_form_tag' ); ?> >
 
				 <?php do_action( 'woocommerce_register_form_start' ); ?>
 
				 <?php if ( 'no' === get_option( 'woocommerce_registration_generate_username' ) ) : ?>
					 <!-- Username Input -->
					 <div class="form-group">
						 <label for="reg_username"><?php esc_html_e( 'Username', 'woocommerce' ); ?>&nbsp;<span class="required">*</span></label>
						 <input type="text" class="form-control" name="username" id="reg_username"
								autocomplete="username"
								placeholder="<?php esc_html_e( 'Enter Username', 'woocommerce' ); ?>"
								value="<?php echo ( ! empty( $_POST['username'] ) ) ? esc_attr( wp_unslash( $_POST['username'] ) ) : ''; ?>" />
					 </div>
				 <?php endif; ?>
 
				 <!-- Email Input -->
				 <div class="form-group">
					 <input type="email" class="form-control" name="email" id="reg_email"
							placeholder="<?php esc_html_e( 'Email address', 'woocommerce' ); ?> *"
							autocomplete="email"
							value="<?php echo ( ! empty( $_POST['email'] ) ) ? esc_attr( wp_unslash( $_POST['email'] ) ) : ''; ?>" />
				 </div>
 
				 <?php if ( 'no' === get_option( 'woocommerce_registration_generate_password' ) ) : ?>
					 <!-- Password Input -->
					 <div class="form-group">
						 <input type="password" class="form-control" name="password" id="reg_password"
								autocomplete="new-password"
								placeholder="<?php esc_html_e( 'Password', 'woocommerce' ); ?> *" />
					 </div>
				 <?php else : ?>
					 <!-- Auto-Generated Password Notice -->
					 <div class="form-group">
						 <p><?php esc_html_e( 'A password will be sent to your email address.', 'woocommerce' ); ?></p>
					 </div>
				 <?php endif; ?>
 
				 <?php do_action( 'woocommerce_register_form' ); ?>
 
				 <!-- Registration Button -->
				 <div class="form-group">
					 <?php wp_nonce_field( 'woocommerce-register', 'woocommerce-register-nonce' ); ?>
					 <button type="submit" class="btnSubmit" name="register"
							 value="<?php esc_attr_e( 'Register', 'woocommerce' ); ?>"><?php esc_html_e( 'Register', 'woocommerce' ); ?></button>
				 </div>
 
				 <?php do_action( 'woocommerce_register_form_end' ); ?>
			 </form>
		 </div>
	 </div>
 </div>
 

<style>
.page-normal>.title {display: none}
.woocommerce-privacy-policy-text p{color:#fff;}
.woocommerce-privacy-policy-link{color: yellow!important;}
.login-container{margin-top: 5%;margin-bottom: 5%;}
.login-form-1{
    padding: 1%;
    box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 9px 26px 0 rgba(0, 0, 0, 0.19);
	background: #7aa2cd;
}
.login-form-1 h3{
    text-align: center;
    color: #333;
}
.login-form-2{
    padding: 1%;
    background: #0062cc;
    box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 9px 26px 0 rgba(0, 0, 0, 0.19);
}
.login-form-2 h3{
    text-align: center;
    color: #fff;
}
.login-container form{
    padding: 10%;
}
.btnSubmit
{
    width: 50%;
    border-radius: 1rem;
    padding: 1.5%;
    border: none;
    cursor: pointer;
}
.login-form-1 .btnSubmit{
    font-weight: 600;
    color: #fff;
    background-color: #0062cc;
	padding: 15px 10px;
}
.login-form-2 .btnSubmit{
    font-weight: 600;
    color: #0062cc;
    background-color: #fff;padding: 15px 10px;
}
.login-form-2 .ForgetPwd{
    color: #fff;
    font-weight: 600;
    text-decoration: none;
}
.login-form-1 .ForgetPwd{
    color: #0062cc;
    font-weight: 600;
    text-decoration: none;
}
.woocommerce-privacy-policy-text{display: none;}
</style>        