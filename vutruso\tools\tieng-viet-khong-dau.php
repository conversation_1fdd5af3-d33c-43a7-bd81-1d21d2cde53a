<?php
/*
 * Template Name: Tiếng Việt Không Dấu
 */
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Basic Meta Tags -->
    <title>Chuyển đổi tiếng Việt có dấu sang không dấu online</title>
    <meta property="og:url" content="https://vutruso.com/chuyen-doi-tieng-viet-co-dau-thanh-khong-dau/">
    <link rel="canonical" href="https://vutruso.com/chuyen-doi-tieng-viet-co-dau-thanh-khong-dau/">
    <meta name="description" content="Công cụ chuyển đổi văn bản tiếng Việt có dấu sang không dấu nhanh chóng và chính xác. Hỗ trợ chuyển đổi ngược lại, bảo toàn định dạng và hoàn toàn miễn phí">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="Chuyển đổi tiếng Việt">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Chuyển đổi tiếng Việt có dấu sang không dấu online">
    <meta property="og:description" content="Công cụ chuyển đổi văn bản tiếng Việt có dấu sang không dấu nhanh chóng và chính xác. Hỗ trợ chuyển đổi ngược lại, bảo toàn định dạng và hoàn toàn miễn phí">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/07/cong-cu-chuyen-doi-van-ban-tieng-viet-co-dau-sang-khong-dau.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/07/cong-cu-chuyen-doi-van-ban-tieng-viet-co-dau-sang-khong-dau.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="Chuyển đổi tiếng Việt - Vutruso">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="Chuyển đổi tiếng Việt có dấu sang không dấu online">
    <meta name="twitter:description" content="Công cụ chuyển đổi văn bản tiếng Việt có dấu sang không dấu nhanh chóng và chính xác. Hỗ trợ chuyển đổi ngược lại, bảo toàn định dạng và hoàn toàn miễn phí">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/07/cong-cu-chuyen-doi-van-ban-tieng-viet-co-dau-sang-khong-dau.png">
    
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Chuyển đổi tiếng Việt",
        "url": "https://vutruso.com/chuyen-doi-tieng-viet-co-dau-thanh-khong-dau/",
        "description": "Công cụ chuyển đổi văn bản tiếng Việt có dấu sang không dấu nhanh chóng và chính xác. Hỗ trợ chuyển đổi ngược lại, bảo toàn định dạng và hoàn toàn miễn phí",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND"
        },
        "featureList": [
            "Chuyển đổi tiếng Việt có dấu sang không dấu",
            "Chuyển đổi ngược từ không dấu sang có dấu",
            "Bảo toàn định dạng văn bản",
            "Xử lý văn bản lớn",
            "Sao chép kết quả một cú click",
            "Giao diện thân thiện",
            "Hoàn toàn miễn phí"
        ],
        "screenshot": "https://vutruso.com/wp-content/uploads/2025/07/cong-cu-chuyen-doi-van-ban-tieng-viet-co-dau-sang-khong-dau.png",
        "creator": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "alternateName": "Vũ Trụ Số",
            "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
            "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
            "sameAs": [
                "https://www.facebook.com/vutruso",
                "https://twitter.com/@vutruso",
                "https://www.pinterest.com/vutruso/",
                "https://www.instagram.com/vutruso",
                "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
                "https://www.linkedin.com/in/vutruso",
                "https://g.page/vutruso",
                "https://vutruso.business.site/",
                "https://sites.google.com/view/vutruweb",
                "https://vutruso.tumblr.com/",
                "https://ok.ru/profile/589668477610"
            ],
            "vatID": "0317358676",
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": "+***********",
                    "email": "<EMAIL>",
                    "contactOption": "TollFree",
                    "contactType": "customer support"
                }
            ]
        }
    }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 25s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .converter-tool {
            padding: 0px;
        }

        .converter-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            padding: 35px;
            border-radius: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .input-section, .output-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .input-section:hover, .output-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .textarea-wrapper {
            position: relative;
        }

        .text-input, .text-output {
            width: 100%;
            min-height: 150px;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
        }

        .text-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .text-output {
            background: #f8f9ff;
            color: #333;
            font-weight: 500;
        }

        .char-counter {
            position: absolute;
            bottom: 10px;
            right: 15px;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 25px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
            justify-content: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn.success:hover {
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .examples-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .examples-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .example-item {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }

        .example-before {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .example-after {
            color: #666;
            font-style: italic;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: #f44336;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .converter-tool {
                padding: 5px;
            }
            
            .converter-section {
                padding: 5px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }

            .header {
                padding: 20px;

            }
            .text-input, .text-output {
                font-size: 13px;
            }

        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Chuyển đổi tiếng Việt</h1>
            <p>Công cụ chuyển đổi văn bản tiếng Việt có dấu sang không dấu</p>
        </div>

        <div class="converter-tool">
            <div class="converter-section">
                <div class="input-section">
                    <div class="section-title">
                        📝 Văn bản có dấu
                    </div>
                    <div class="textarea-wrapper">
                        <textarea 
                            id="inputText" 
                            class="text-input" 
                            placeholder="Nhập văn bản tiếng Việt có dấu vào đây...&#10;&#10;Ví dụ: Xin chào! Tôi là người Việt Nam. Hôm nay trời đẹp quá!"
                        ></textarea>
                        <div id="inputCounter" class="char-counter">0 ký tự</div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="convertBtn" class="btn">
                        🔄 Chuyển đổi
                    </button>
                    <button id="clearBtn" class="btn secondary">
                        🗑️ Xóa hết
                    </button>
                </div>

                <div class="output-section">
                    <div class="section-title">
                        ✅ Văn bản không dấu
                    </div>
                    <div class="textarea-wrapper">
                        <textarea 
                            id="outputText" 
                            class="text-output" 
                            readonly 
                            placeholder="Kết quả chuyển đổi sẽ hiển thị ở đây..."
                        ></textarea>
                        <div id="outputCounter" class="char-counter">0 ký tự</div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="copyBtn" class="btn success">
                        📋 Sao chép kết quả
                    </button>
                </div>

                <div class="stats">
                    <div class="stat-item">
                        <div id="wordCount" class="stat-value">0</div>
                        <div class="stat-label">Từ</div>
                    </div>
                    <div class="stat-item">
                        <div id="lineCount" class="stat-value">0</div>
                        <div class="stat-label">Dòng</div>
                    </div>
                    <div class="stat-item">
                        <div id="charWithSpaces" class="stat-value">0</div>
                        <div class="stat-label">Ký tự</div>
                    </div>
                    <div class="stat-item">
                        <div id="charWithoutSpaces" class="stat-value">0</div>
                        <div class="stat-label">Không khoảng trắng</div>
                    </div>
                </div>
            </div>

            <div class="examples-section">
                <div class="examples-title">
                    💡 Ví dụ chuyển đổi
                </div>
                
                <div class="example-item">
                    <div class="example-before">Có dấu: Xin chào! Tôi là người Việt Nam.</div>
                    <div class="example-after">Không dấu: Xin chao! Toi la nguoi Viet Nam.</div>
                </div>
                
                <div class="example-item">
                    <div class="example-before">Có dấu: Hôm nay trời đẹp quá, tôi muốn đi dạo.</div>
                    <div class="example-after">Không dấu: Hom nay troi dep qua, toi muon di dao.</div>
                </div>
                
                <div class="example-item">
                    <div class="example-before">Có dấu: Món phở Việt Nam rất ngon và nổi tiếng.</div>
                    <div class="example-after">Không dấu: Mon pho Viet Nam rat ngon va noi tieng.</div>
                </div>
            </div>
        </div>
    </div>

    <div id="notification" class="notification">
        ✅ Đã sao chép thành công!
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const inputText = document.getElementById('inputText');
            const outputText = document.getElementById('outputText');
            const convertBtn = document.getElementById('convertBtn');
            const clearBtn = document.getElementById('clearBtn');
            const copyBtn = document.getElementById('copyBtn');
            const inputCounter = document.getElementById('inputCounter');
            const outputCounter = document.getElementById('outputCounter');
            const notification = document.getElementById('notification');
            
            // Stats elements
            const wordCount = document.getElementById('wordCount');
            const lineCount = document.getElementById('lineCount');
            const charWithSpaces = document.getElementById('charWithSpaces');
            const charWithoutSpaces = document.getElementById('charWithoutSpaces');

            // Vietnamese diacritics mapping
            const vietnameseMap = {
                'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
                'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
                'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
                'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
                'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
                'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
                'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
                'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
                'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
                'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
                'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
                'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
                'đ': 'd',
                'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
                'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
                'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
                'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
                'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
                'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
                'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
                'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
                'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
                'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
                'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
                'Ỳ': 'Y', 'Ý': 'Y', 'Ỵ': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y',
                'Đ': 'D'
            };

            // Remove Vietnamese diacritics function
            function removeVietnameseTones(str) {
                return str.replace(/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/g, function(match) {
                    return vietnameseMap[match] || match;
                }).replace(/[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/g, function(match) {
                    return vietnameseMap[match] || match;
                });
            }

            // Update character counter
            function updateCounter(textElement, counterElement) {
                const text = textElement.value;
                const charCount = text.length;
                counterElement.textContent = `${charCount} ký tự`;
            }

            // Update statistics
            function updateStats(text) {
                const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
                const lines = text === '' ? 0 : text.split('\n').length;
                const charsWithSpaces = text.length;
                const charsWithoutSpaces = text.replace(/\s/g, '').length;

                wordCount.textContent = words;
                lineCount.textContent = lines;
                charWithSpaces.textContent = charsWithSpaces;
                charWithoutSpaces.textContent = charsWithoutSpaces;
            }

            // Show notification
            function showNotification(message, isError = false) {
                notification.textContent = message;
                notification.className = `notification ${isError ? 'error' : ''}`;
                notification.classList.add('show');
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }

            // Convert text function
            function convertText() {
                const inputValue = inputText.value.trim();
                
                if (inputValue === '') {
                    showNotification('⚠️ Vui lòng nhập văn bản cần chuyển đổi!', true);
                    inputText.focus();
                    return;
                }

                const convertedText = removeVietnameseTones(inputValue);
                outputText.value = convertedText;
                updateCounter(outputText, outputCounter);
                
                showNotification('✅ Chuyển đổi thành công!');
            }

            // Copy to clipboard function
            function copyToClipboard() {
                const textToCopy = outputText.value.trim();
                
                if (textToCopy === '') {
                    showNotification('⚠️ Không có nội dung để sao chép!', true);
                    return;
                }

                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(textToCopy)
                        .then(() => {
                            showNotification('📋 Đã sao chép vào clipboard!');
                        })
                        .catch(() => {
                            fallbackCopyTextToClipboard(textToCopy);
                        });
                } else {
                    fallbackCopyTextToClipboard(textToCopy);
                }
            }

            // Fallback copy function
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";
                
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showNotification('📋 Đã sao chép vào clipboard!');
                    } else {
                        showNotification('❌ Không thể sao chép. Vui lòng sao chép thủ công.', true);
                    }
                } catch (err) {
                    showNotification('❌ Không thể sao chép. Vui lòng sao chép thủ công.', true);
                }
                
                document.body.removeChild(textArea);
            }

            // Clear all text
            function clearAll() {
                inputText.value = '';
                outputText.value = '';
                updateCounter(inputText, inputCounter);
                updateCounter(outputText, outputCounter);
                updateStats('');
                inputText.focus();
                showNotification('🗑️ Đã xóa hết nội dung!');
            }

            // Event listeners
            inputText.addEventListener('input', function() {
                updateCounter(this, inputCounter);
                updateStats(this.value);
                
                // Auto convert on typing (with debounce)
                clearTimeout(this.autoConvertTimer);
                this.autoConvertTimer = setTimeout(() => {
                    if (this.value.trim() !== '') {
                        convertText();
                    } else {
                        outputText.value = '';
                        updateCounter(outputText, outputCounter);
                    }
                }, 500);
            });

            convertBtn.addEventListener('click', convertText);
            clearBtn.addEventListener('click', clearAll);
            copyBtn.addEventListener('click', copyToClipboard);

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'Enter':
                            e.preventDefault();
                            convertText();
                            break;
                        case 'l':
                            e.preventDefault();
                            clearAll();
                            break;
                        case 'd':
                            if (e.target === outputText) {
                                e.preventDefault();
                                copyToClipboard();
                            }
                            break;
                    }
                }
            });

            // Initial setup
            updateCounter(inputText, inputCounter);
            updateCounter(outputText, outputCounter);
            updateStats('');
            inputText.focus();
        });
    </script>
</body>
</html>
