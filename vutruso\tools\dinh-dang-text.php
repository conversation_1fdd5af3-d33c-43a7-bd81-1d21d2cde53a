<?php
/*
 * Template Name: Format text
 */
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Basic Meta Tags -->
    <title>🔧 Công cụ xử lý văn bản đa năng - Text Editor Online</title>
    <meta property="og:url" content="https://vutruso.com/dinh-dang-text/">
    <link rel="canonical" href="https://vutruso.com/dinh-dang-text/">
    <meta name="description" content="Công cụ xử lý văn bản đa năng với 15+ tính năng: chuyển đổi hoa/thường, tìm & thay thế, định dạng text, xóa khoảng trắng. Miễn phí, nhanh chóng, không cần đăng ký.">

    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="🔧 Công cụ xử lý văn bản đa năng - Text Editor Online">
    <meta property="og:locale" content="vi_VN">
    <meta property="og:type" content="website">
    <meta property="og:title" content="🔧 Công cụ xử lý văn bản đa năng - Text Editor Online">
    <meta property="og:description" content="Công cụ xử lý văn bản đa năng với 15+ tính năng: chuyển đổi hoa/thường, tìm & thay thế, định dạng text, xóa khoảng trắng. Miễn phí, nhanh chóng, không cần đăng ký.">
    <meta property="og:image" content="https://vutruso.com/wp-content/uploads/2025/08/cong-cu-xu-ly-van-ban-da-nang.png">
    <meta property="og:image:secure_url" content="https://vutruso.com/wp-content/uploads/2025/08/cong-cu-xu-ly-van-ban-da-nang.png">
    <meta property="og:image:width" content="405">
    <meta property="og:image:height" content="270">
    <meta property="og:image:alt" content="🔧 Công cụ xử lý văn bản đa năng - Text Editor Online">
    <meta property="fb:admins" content="100002603951764">
    <meta property="fb:app_id" content="***************">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vutruso">
    <meta name="twitter:title" content="🔧 Công cụ xử lý văn bản đa năng - Text Editor Online">
    <meta name="twitter:description" content="Công cụ xử lý văn bản đa năng với 15+ tính năng: chuyển đổi hoa/thường, tìm & thay thế, định dạng text, xóa khoảng trắng. Miễn phí, nhanh chóng, không cần đăng ký.">
    <meta name="twitter:image" content="https://vutruso.com/wp-content/uploads/2025/08/cong-cu-xu-ly-van-ban-da-nang.png">
    <!-- Structured Data JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Công cụ chuyển đổi chữ",
        "url": "https://vutruso.com/dinh-dang-text/",
        "description": "Công cụ chuyển đổi định dạng text nhanh chóng: UPPERCASE, lowercase, Title Case, tạo slug SEO, tìm & thay thế. Hỗ trợ đầy đủ tiếng Việt có dấu, không cần đăng ký, hoàn toàn miễn phí",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND"
        },
        "featureList": [
            "Chuyển đổi UPPERCASE và lowercase",
            "Title Case và Sentence case",
            "Capitalized Case hỗ trợ tiếng Việt",
            "Create Slug SEO từ tiếng Việt",
            "Tìm và thay thế nâng cao với regex",
            "Bỏ khoảng trắng và thay thế ký tự",
            "Chuyển đổi dòng mới và dấu phẩy",
            "Xóa dòng trắng tự động",
            "Hỗ trợ đầy đủ tiếng Việt có dấu",
            "Tự động scroll đến kết quả",
            "Sao chép kết quả nhanh",
            "Xử lý văn bản lớn",
            "Hoàn toàn miễn phí"
        ],
        "screenshot": "https://vutruso.com/wp-content/uploads/2025/07/cong-cu-chuyen-doi-chu.png",
        "creator": {
            "@type": "Organization",
            "name": "Vũ Trụ Số",
            "url": "https://vutruso.com",
            "alternateName": "Vũ Trụ Số",
            "description": "Vũ Trụ Số chuyên cung cấp các giải pháp công nghệ, thiết kế website, quản trị website cho doanh nghiệp, cung cấp máy chủ ảo VPS/Hosting, dịch vụ chạy quảng cáo Google, quảng cáo Facebook, quảng cáo Zalo, các dịch vụ bảo mật website WordPress, tăng tốc website WordPress và SEO mới từ khóa lên top Google",
            "logo": "https://vutruso.com/wp-content/uploads/2020/07/vutruso_login.jpg",
            "sameAs": [
                "https://www.facebook.com/vutruso",
                "https://twitter.com/@vutruso",
                "https://www.pinterest.com/vutruso/",
                "https://www.instagram.com/vutruso",
                "https://www.youtube.com/channel/UCOEXvu-rxMQ6Tvqm1kZJUrg/about",
                "https://www.linkedin.com/in/vutruso",
                "https://g.page/vutruso",
                "https://vutruso.business.site/",
                "https://sites.google.com/view/vutruweb",
                "https://vutruso.tumblr.com/",
                "https://ok.ru/profile/589668477610"
            ],
            "vatID": "0317358676",
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": "+***********",
                    "email": "<EMAIL>",
                    "contactOption": "TollFree",
                    "contactType": "customer support"
                }
            ]
        }
    }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 0;
        }

        .header-banner {
            background: linear-gradient(135deg, #285aaa 0%, #1269b1 100%);
            padding: 10px 0 96px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-banner::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .main-title {
            font-size: 2em;
            margin: 0 0 10px 0;
            color: #fff;
            font-weight: 300;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
        }

        .sub-title {
            color: rgba(255,255,255,0.9);
            font-size: 1.1em;
            line-height: 1.6;
            font-weight: 300;
            max-width: 860px;
            margin: 0 auto;
        }

        .container {
            max-width: 1200px;
            margin: -80px auto 0;
            position: relative;
            z-index: 10;
            padding: 0 20px 40px;
        }

        .converter-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(15px);
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            text-align: center;
            position: relative;
        }

        .card-title {
            font-size: 1.3em;
            margin: 0;
            font-weight: 300;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .card-body {
            padding: 15px 30px;
        }

        .input-section {
            margin-bottom: 15px;
        }

        .input-label {
            display: block;
            margin-bottom: 10px;
            color: #333;
            font-weight: 600;
            font-size: 1em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-textarea {
            width: 100%;
            min-height: 150px;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: all 0.3s ease;
            background: #f8f9ff;
            line-height: 1.5;
        }

        .input-textarea:focus {
            outline: none;
            border-color: #667eea;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .converter-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
            gap: 10px;
            margin-bottom: 30px;
        }

        .convert-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 10px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: none;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .convert-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .convert-btn:active {
            transform: translateY(0px);
        }

        /* New utility buttons with different color */
        .utility-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .utility-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .find-replace-section {
            background: linear-gradient(135deg, #fff8e1 0%, #f3e5f5 100%);
            border-radius: 20px;
            padding: 15px 20px;
            margin-bottom: 30px;
            border: 2px solid #e8eaf6;
        }

        .find-replace-title {
            color: #333;
            font-weight: 600;
            font-size: 1.3em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .find-replace-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
            margin-bottom: 15px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 14px;
        }

        .find-replace-input {
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .find-replace-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .replace-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            height: fit-content;
        }

        .replace-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .find-replace-options {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-top: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .checkbox-group label {
            font-size: 14px;
            color: #666;
            margin: 0;
        }

        .output-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border-radius: 20px;
            padding: 0px;
            margin-bottom: 30px;
        }

        .output-label {
            display: block;
            margin-bottom: 15px;
            color: #333;
            font-weight: 600;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .output-textarea {
            width: 100%;
            min-height: 150px;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            background: white;
            line-height: 1.5;
            color: #333;
        }

        .output-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .action-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .copy-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .download-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .clear-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .clear-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }

        .character-count {
            text-align: center;
            margin-top: 5px;
            color: #666;
            font-size: 14px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .info-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .info-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            text-align: center;
        }

        .info-title {
            font-size: 1.5em;
            margin: 0;
            font-weight: 300;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .info-body {
            padding: 15px 15px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .feature-item {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            padding: 15px;
            border-radius: 15px;
            border-left: 2px solid #667eea;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        .example-text {
            font-style: italic;
            color: #888;
            font-size: 13px;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .info-body {
                padding: 15px;
            }

            .header-banner {
                padding: 15px 0 87px;
            }
            
            .main-title {
                font-size: 1.3em;
            }
            
            .sub-title {
                font-size: 1.1em;
            }
            
            .container {
                margin: -60px auto 0;
                padding: 0 15px 30px;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .converter-buttons {
                grid-template-columns: 1fr;
            }

            .find-replace-inputs {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .find-replace-options {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .action-btn {
                width: 100%;
                justify-content: center;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="header-banner">
        <div class="header-content">
            <h1 class="main-title">🔧 Công cụ xử lý văn bản đa năng - Text Editor Online</h1>
            <p class="sub-title">Chuyển đổi định dạng text nhanh chóng với nhiều tùy chọn. Hỗ trợ tiếng Việt có dấu, tạo slug SEO, tìm & thay thế</p>
        </div>
    </div>

    <div class="container">
        <div class="converter-card">
            <div class="card-header">
                <h2 class="card-title">✨ Text Case Converter</h2>
            </div>
            
            <div class="card-body">
                <div class="input-section">
                    <label class="input-label">📝 Nhập text cần chuyển đổi:</label>
                    <textarea id="inputText" class="input-textarea" placeholder="Nhập text của bạn vào đây..."></textarea>
                    <div class="character-count">
                        Số ký tự: <span id="charCount">0</span>
                    </div>
                </div>

                <!-- Find & Replace Section -->
                <div class="find-replace-section">
                    <div class="find-replace-title">🔍 Tìm & Thay thế</div>
                    <div class="find-replace-inputs">
                        <div class="input-group">
                            <label for="findInput">Tìm kiếm:</label>
                            <input type="text" id="findInput" class="find-replace-input" placeholder="Nhập text cần tìm...">
                        </div>
                        <div class="input-group">
                            <label for="replaceInput">Thay thế bằng:</label>
                            <input type="text" id="replaceInput" class="find-replace-input" placeholder="Nhập text thay thế...">
                        </div>
                        <button class="replace-btn" onclick="findAndReplace()">
                            🔄 Thay thế
                        </button>
                    </div>
                    <div class="find-replace-options">
                        <div class="checkbox-group">
                            <input type="checkbox" id="caseSensitive">
                            <label for="caseSensitive">Phân biệt hoa/thường</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="wholeWord">
                            <label for="wholeWord">Toàn bộ từ</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="useRegex">
                            <label for="useRegex">Sử dụng Regex</label>
                        </div>
                    </div>
                </div>

                <div class="converter-buttons">
                    <!-- Original conversion buttons -->
                    <button class="convert-btn" onclick="convertText('sentence')">
                        📄 Sentence case
                    </button>
                    <button class="convert-btn" onclick="convertText('lower')">
                        🔽 lower case
                    </button>
                    <button class="convert-btn" onclick="convertText('upper')">
                        🔼 UPPER CASE
                    </button>
                    <button class="convert-btn" onclick="convertText('capitalize')">
                        📚 Capitalized Case
                    </button>
                    <button class="convert-btn" onclick="convertText('snakeCase')">
                        🐍 snake_case
                    </button>
                    <button class="convert-btn" onclick="convertText('title')">
                        👑 Title Case
                    </button>
                    <button class="convert-btn" onclick="convertText('slug')">
                        🔗 Create Slug
                    </button>
                    
                    <!-- New utility buttons -->
                    <button class="convert-btn utility-btn" onclick="convertText('removeSpaces')">
                        🚫 Bỏ khoảng cách
                    </button>
                    <button class="convert-btn utility-btn" onclick="convertText('spaceToUnderscore')">
                        🔗 Space → _
                    </button>
                    <button class="convert-btn utility-btn" onclick="convertText('newlineToComma')">
                        📝 Dòng mới → Phẩy
                    </button>
                    <button class="convert-btn utility-btn" onclick="convertText('commaToNewline')">
                        📃 Phẩy → Dòng mới
                    </button>
                    <button class="convert-btn utility-btn" onclick="convertText('removeEmptyLines')">
                        🗑️ Xóa dòng trắng
                    </button>
                </div>

                <div class="output-section">
                    <label class="output-label">✅ Kết quả:</label>
                    <textarea id="outputText" class="output-textarea" placeholder="Kết quả sẽ hiển thị ở đây..." readonly></textarea>
                </div>

                <div class="action-buttons">
                    <button class="action-btn copy-btn" onclick="copyToClipboard()">
                        📋 Copy to Clipboard
                    </button>
                    <button class="action-btn download-btn" onclick="downloadText()">
                        💾 Download Text
                    </button>
                    <button class="action-btn clear-btn" onclick="clearAll()">
                        🗑️ Clear
                    </button>
                </div>
            </div>
        </div>

        <div class="info-card">
            <div class="info-header">
                <h3 class="info-title">📚 Hướng dẫn các định dạng chữ</h3>
            </div>
            <div class="info-body">
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-title">📄 Sentence case</div>
                        <div class="feature-desc">Chữ cái đầu tiên viết hoa, còn lại viết thường</div>
                        <div class="example-text">Ví dụ: "This is sentence case"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🔽 lower case</div>
                        <div class="feature-desc">Tất cả chữ cái đều viết thường</div>
                        <div class="example-text">Ví dụ: "this is lower case"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🔼 UPPER CASE</div>
                        <div class="feature-desc">Tất cả chữ cái đều viết hoa</div>
                        <div class="example-text">Ví dụ: "THIS IS UPPER CASE"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">📚 Capitalized Case</div>
                        <div class="feature-desc">Chữ cái đầu mỗi từ viết hoa</div>
                        <div class="example-text">Ví dụ: "This Is Capitalized Case"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🐍 snake_case</div>
                        <div class="feature-desc">Chuyển text cách nhau _</div>
                        <div class="example-text">Ví dụ: "chuyển đổi" → "chuyen_doi"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">👑 Title Case</div>
                        <div class="feature-desc">Định dạng tiêu đề chuẩn</div>
                        <div class="example-text">Ví dụ: "This Is Title Case"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🔗 Create Slug</div>
                        <div class="feature-desc">Tạo URL slug từ tiếng Việt có dấu, phù hợp cho SEO</div>
                        <div class="example-text">Ví dụ: "Nhập text cần chuyển đổi" → "nhap-text-can-chuyen-doi"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🔍 Tìm & Thay thế</div>
                        <div class="feature-desc">Tìm và thay thế text với nhiều tùy chọn</div>
                        <div class="example-text">Hỗ trợ regex, phân biệt hoa/thường</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🚫 Bỏ khoảng cách</div>
                        <div class="feature-desc">Xóa tất cả khoảng trắng trong text</div>
                        <div class="example-text">Ví dụ: "hello world" → "helloworld"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">🔗 Space → _</div>
                        <div class="feature-desc">Thay thế khoảng trắng bằng dấu gạch dưới</div>
                        <div class="example-text">Ví dụ: "hello world" → "hello_world"</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">📝 Dòng mới → Phẩy</div>
                        <div class="feature-desc">Chuyển đổi xuống dòng thành dấu phẩy</div>
                        <div class="example-text">Hữu ích cho tạo danh sách</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">📃 Phẩy → Dòng mới</div>
                        <div class="feature-desc">Chuyển đổi dấu phẩy thành xuống dòng</div>
                        <div class="example-text">Tách danh sách thành từng dòng</div>
                    </div>
                </div>
            </div>

            <footer class="seo-footer" style="background: #f8f9fa; border-radius: 17px 17px 0 0; padding: 10px; text-align: center; margin: 10px 0; margin-bottom: 0;">
              <div class="footer-content">
                <p style="margin-bottom: 10px; color: #666;">🔧 Công cụ xử lý văn bản đa năng - Text Editor Online</p>
                <p style="margin-bottom: 10px; color: #666;">Phát triển bởi <a style="color: #4285f4;" href="https://vutruso.com/">Vũ Trụ Số</a></p>
              </div>
            </footer>

        </div>
    </div>

    <div id="notification" class="notification">
        ✅ Thao tác thành công!
    </div>

    <script>
        // Helper function to remove Vietnamese accents (simple version)
        function removeVietnameseAccents(str) {
            return str
                .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
                .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
                .replace(/[ìíịỉĩ]/g, 'i')
                .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
                .replace(/[ùúụủũưừứựửữ]/g, 'u')
                .replace(/[ỳýỵỷỹ]/g, 'y')
                .replace(/[đ]/g, 'd')
                .replace(/[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]/g, 'A')
                .replace(/[ÈÉẸẺẼÊỀẾỆỂỄ]/g, 'E')
                .replace(/[ÌÍỊỈĨ]/g, 'I')
                .replace(/[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]/g, 'O')
                .replace(/[ÙÚỤỦŨƯỪỨỰỬỮ]/g, 'U')
                .replace(/[ỲÝỴỶỸ]/g, 'Y')
                .replace(/[Đ]/g, 'D');
        }

        // Update character count
        function updateCharCount() {
            const inputText = document.getElementById('inputText').value;
            document.getElementById('charCount').textContent = inputText.length;
        }

        // Show notification
        function showNotification(message, isError = false) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.background = isError ? '#f44336' : '#4CAF50';
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Scroll to result section
        function scrollToResult() {
            const outputSection = document.querySelector('.output-section');
            if (outputSection) {
                outputSection.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
                
                // Small delay then focus on output textarea for better UX
                setTimeout(() => {
                    const outputTextarea = document.getElementById('outputText');
                    if (outputTextarea && outputTextarea.value.trim()) {
                        outputTextarea.focus();
                        outputTextarea.select(); // Select all text for easy copying
                    }
                }, 300);
            }
        }

        // Find and Replace function
        function findAndReplace() {
            const inputText = document.getElementById('inputText').value;
            const findText = document.getElementById('findInput').value;
            const replaceText = document.getElementById('replaceInput').value;
            const outputTextarea = document.getElementById('outputText');
            
            if (!inputText.trim()) {
                showNotification('⚠️ Vui lòng nhập text cần xử lý!', true);
                return;
            }
            
            if (!findText) {
                showNotification('⚠️ Vui lòng nhập text cần tìm!', true);
                return;
            }
            
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const wholeWord = document.getElementById('wholeWord').checked;
            const useRegex = document.getElementById('useRegex').checked;
            
            let result = inputText;
            let flags = 'g'; // global replacement
            
            if (!caseSensitive) {
                flags += 'i'; // case insensitive
            }
            
            try {
                let searchPattern;
                
                if (useRegex) {
                    searchPattern = new RegExp(findText, flags);
                } else {
                    // Escape special regex characters if not using regex
                    const escapedFind = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    
                    if (wholeWord) {
                        searchPattern = new RegExp('\\b' + escapedFind + '\\b', flags);
                    } else {
                        searchPattern = new RegExp(escapedFind, flags);
                    }
                }
                
                result = result.replace(searchPattern, replaceText);
                
                outputTextarea.value = result;
                
                // Count replacements
                const matches = inputText.match(searchPattern);
                const count = matches ? matches.length : 0;
                
                if (count > 0) {
                    showNotification(`✅ Đã thay thế ${count} kết quả!`);
                    scrollToResult(); // Auto scroll to result
                } else {
                    showNotification('⚠️ Không tìm thấy text cần thay thế!', true);
                }
                
            } catch (error) {
                showNotification('❌ Lỗi regex không hợp lệ!', true);
            }
        }

        // Convert text based on type
        function convertText(type) {
            const inputText = document.getElementById('inputText').value;
            const outputTextarea = document.getElementById('outputText');
            
            if (!inputText.trim()) {
                showNotification('⚠️ Vui lòng nhập text cần chuyển đổi!', true);
                return;
            }
            
            let result = '';
            
            switch(type) {
                case 'sentence':
                    result = inputText.toLowerCase();
                    result = result.charAt(0).toUpperCase() + result.slice(1);
                    break;
                    
                case 'lower':
                    result = inputText.toLowerCase();
                    break;
                    
                case 'upper':
                    result = inputText.toUpperCase();
                    break;
                    
                case 'capitalize':
                    result = inputText.toLowerCase().split(' ').map(word => {
                        if (word.length > 0) {
                            return word.charAt(0).toUpperCase() + word.slice(1);
                        }
                        return word;
                    }).join(' ');
                    break;
                    
                case 'snakeCase':
                    // Create snake_case from Vietnamese text
                    result = removeVietnameseAccents(inputText)
                        .toLowerCase()                    // Convert to lowercase
                        .replace(/[^\w\s]/g, '')         // Remove special characters except word chars and spaces
                        .replace(/\s+/g, '_')            // Replace spaces with underscores
                        .replace(/_+/g, '_')             // Replace multiple underscores with single underscore
                        .replace(/^_+|_+$/g, '');        // Remove leading and trailing underscores
                    break;
                    
                case 'title':
                    // Articles, conjunctions, and prepositions to keep lowercase (unless first/last word)
                    const smallWords = ['a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'if', 'in', 'nor', 'of', 'on', 'or', 'so', 'the', 'to', 'up', 'yet'];
                    result = inputText.toLowerCase().split(' ').map((word, index, array) => {
                        // Always capitalize first and last word
                        if (index === 0 || index === array.length - 1) {
                            return word.charAt(0).toUpperCase() + word.slice(1);
                        }
                        // Keep small words lowercase unless they're the first word
                        if (smallWords.includes(word)) {
                            return word;
                        }
                        // Capitalize other words
                        return word.charAt(0).toUpperCase() + word.slice(1);
                    }).join(' ');
                    break;
                    
                case 'slug':
                    // Create URL-friendly slug from Vietnamese text
                    result = removeVietnameseAccents(inputText)
                        .toLowerCase()                     // Convert to lowercase
                        .replace(/[^\w\s-]/g, '')         // Remove special characters except word chars, spaces, and hyphens
                        .replace(/\s+/g, '-')             // Replace spaces with hyphens
                        .replace(/-+/g, '-')              // Replace multiple hyphens with single hyphen
                        .replace(/^-+|-+$/g, '');         // Remove leading and trailing hyphens
                    break;
                
                // New utility functions
                case 'removeSpaces':
                    result = inputText.replace(/\s+/g, '');
                    break;
                    
                case 'spaceToUnderscore':
                    result = inputText.replace(/\s+/g, '_');
                    break;
                    
                case 'newlineToComma':
                    result = inputText.replace(/\n+/g, ', ').replace(/,\s*$/, ''); // Remove trailing comma
                    break;
                    
                case 'commaToNewline':
                    result = inputText.replace(/,\s*/g, '\n').replace(/^\n|\n$/g, ''); // Remove leading/trailing newlines
                    break;
                    
                case 'removeEmptyLines':
                    result = inputText.replace(/^\s*[\r\n]/gm, '');
                    break;
                    
                default:
                    result = inputText;
            }
            
            outputTextarea.value = result;
            showNotification('✅ Chuyển đổi thành công!');
            scrollToResult(); // Auto scroll to result
        }

        // Copy to clipboard
        function copyToClipboard() {
            const outputText = document.getElementById('outputText').value;
            
            if (!outputText.trim()) {
                showNotification('⚠️ Không có text để copy!', true);
                return;
            }
            
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(outputText).then(function() {
                    showNotification('📋 Đã copy vào clipboard!');
                }).catch(function() {
                    fallbackCopy(outputText);
                });
            } else {
                fallbackCopy(outputText);
            }
        }

        // Fallback copy method
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showNotification('📋 Đã copy vào clipboard!');
            } catch (err) {
                showNotification('❌ Không thể copy. Vui lòng copy thủ công.', true);
            }
            
            document.body.removeChild(textArea);
        }

        // Download text as file
        function downloadText() {
            const outputText = document.getElementById('outputText').value;
            
            if (!outputText.trim()) {
                showNotification('⚠️ Không có text để download!', true);
                return;
            }
            
            const blob = new Blob([outputText], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'converted_text.txt';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showNotification('💾 File đã được download!');
        }

        // Clear all text
        function clearAll() {
            document.getElementById('inputText').value = '';
            document.getElementById('outputText').value = '';
            document.getElementById('findInput').value = '';
            document.getElementById('replaceInput').value = '';
            updateCharCount();
            showNotification('🗑️ Đã xóa tất cả text!');
        }

        // Initialize event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const inputTextarea = document.getElementById('inputText');
            
            // Update character count on input
            inputTextarea.addEventListener('input', updateCharCount);
            
            // Allow Enter key to work in textarea but prevent form submission
            inputTextarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    // Allow normal Enter behavior in textarea
                }
            });

            // Add Enter key support for find/replace
            document.getElementById('findInput').addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    findAndReplace();
                }
            });

            document.getElementById('replaceInput').addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    findAndReplace();
                }
            });
            
            // Initialize character count
            updateCharCount();
        });
    </script>
</body>
</html>