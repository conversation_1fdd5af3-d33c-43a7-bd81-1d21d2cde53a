<?php
/*
 * Template Name: Gỡ mã độc
 */
?>
<?php get_header(); ?>

<section class="lead vts-lead">
    <div class="innerContainer">
        <div class="row">
            <div class="lead-text col-md-6 col-sm-12 col-lg-6" itemscope itemtype="http://schema.org/Service">
                <!-- Add service name -->
                <meta itemprop="name" content="Dịch vụ gỡ bỏ mã độc WordPress">
                <meta itemprop="serviceType" content="WordPress Malware Removal">

                <!-- Add description -->
                <meta itemprop="description" content="Dịch vụ diệt sạch mã độc WordPress với bảo hành 12 tháng">

                <h1>Dich vụ gỡ bỏ mã độc <span>WordPress</span></h1>

                <div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
                    <ul style="color: #fff; line-height: 40px;">
                        <li>️️🎉 Diệt sạch mã độc từ <del>1,500,000VNĐ</del>
                            <span itemprop="price">800,000VNĐ</span>
                        </li>
                        <!-- Add availability -->
                        <meta itemprop="availability" content="InStock">
                        <!-- Add valid from -->
                        <meta itemprop="validFrom" content="2025-12-12">
                        <li>️💫 Nếu bạn sở hữu nhiều website và bị nhiễm mã độc chung trên 1 host chúng tôi có chính
                            sách trợ giá</li>
                    </ul>
                    <meta itemprop="priceCurrency" content="VND">
                </div>
                <div class="lead-button">
                    <a href="https://vutruso.com/lien-he/">Liên hệ</a>
                </div>
            </div>
            <div class="lead-picture col-md-6 col-sm-12 col-lg-6">
                <figure>
                    <img src="https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/wordpress-malware-removal-service.png"
                        alt="Gỡ bỏ mã độc hoàn toàn khỏi WordPress">
                </figure>
            </div>
            <div class="lead-line col-12"></div>
        </div>
    </div>
</section>

<style type="text/css" media="screen">
.fadeInUp {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
}

.lead-line::after {
    content: '';
    background-image: url(https://vutruso.com/wp-content/uploads/2020/11/header-shape.svg);
    background-size: 101%;
    background-repeat: no-repeat;
    background-position: bottom;
    display: block;
    height: 100%;
    position: absolute;
    bottom: -6px;
    left: -1px;
    right: 0px;
    z-index: 1;
}


.vts-lead {
    position: relative;
}


section.lead {
    background-color: #196bbb;
    padding: 100px 0;
}

.lead-text h1 {
    color: #fff;
    font-size: 50px;
    font-size: 2.0rem;
    margin-bottom: 28px;
    font-family: 'Baloo Chettan 2';
}

.lead-text h1 span {
    font-size: 6.2rem;
    line-height: 79px;
    color: #edf0f8a6;
}

.lead-text p {
    font-size: 20px;
    color: #fff;
    font-weight: 400;
}

.lead-text p span.clear {
    display: block;
}

.lead-button {
    background-color: #EBB000;
    border-radius: 6px;
    display: inline-block;
    position: relative;
    margin-top: 4%
}

.lead-button a {
    color: #fff;
    text-decoration: none;
    padding: 16px 62px 16px 98px;
    display: block;
    font-size: 20px;
    text-transform: uppercase;
}

.lead-button a:before {
    background-image: url(<?php echo get_template_directory_uri();
    ?>/img/icon/tool-button.png);
    background-size: 32px;
    width: 32px;
    height: 32px;
    background-repeat: no-repeat;
    top: 16px;
    left: 36px;
    position: absolute;
    content: "";
}

.lead-text .rating {
    display: none;
}

.lead-picture {
    text-align: center;
}

.lead-button:active,
.lead-button:hover,
.lead-button:focus {
    background-color: #e0a801;
}

.lead-picture img {
    max-width: 100%
}

@media only screen and (max-width:860px) {
    .lead-picture {
        display: none;
    }

    .lead-text h1,
    .lead-text h1 span {
        font-size: 36px;
        font-size: 2rem;
        text-align: left;
    }

    .lead-text p {
        font-size: 1rem;
    }

    .lead-text p span.clear {
        display: inline;
    }

    section.lead {
        padding: 69px 0 44px 0;
    }
}
</style>

<section class="toi-uu" style="padding-top: 4%;">
  <div class="innerContainer">
    <div class="lead-heading">
      <h2 style="font-size:22px;margin-bottom: 2px;text-transform: capitalize;">Một số dạng tấn công vào website
        bạn nên biết</h2>
      <div class="separator_wrapper bounce_fade animated bounceIn">
        <div class="separator_first_circle">
          <div class="separator_second_circle"></div>
        </div>
      </div>
    </div>

    <div class="accordion">
      <div class="accordion-item">
        <div class="accordion-item-header">
          JAVASCRIPT MALWARE INJECTIONS
        </div>
        <div class="accordion-item-body">
          <div class="accordion-item-body-content">
            JavaScript là một đoạn mã có thể thao túng và phát tán phần mềm độc hại vào hệ thống website,
            Hầu hết những kẻ tấn công bằng phần mềm độc hại tiêm JavaScript độc hại vào phần quản trị
            website. Chúng tôi thực hiện các hoạt động giải mã và giúp bạn phát hiện mã JavaScript độc hại
            bị che giấu.
          </div>
        </div>
      </div>
      <div class="accordion-item">
        <div class="accordion-item-header">
          <p>MALICIOUS WEBSITE REDIRECTIONS</p>
        </div>
        <div class="accordion-item-body">
          <div class="accordion-item-body-content">
            <p>Nếu website của bạn tự động chuyển hướng sang các website khác và lặp lại điều này thì chắc
              chắn website của bạn đã bị nhiễm malware, hacker sẻ sử dụng các đoạn mã để chuyển hướng
              website của bạn sang 1 website được chỉ định nào đó</p>
          </div>
        </div>
      </div>
      <div class="accordion-item">
        <div class="accordion-item-header">
          HIDDEN AND MALICIOUS IFRAMES
        </div>
        <div class="accordion-item-body">
          <div class="accordion-item-body-content">
            IFrame là một mã độc hại lây nhiễm các trang web của bn. Nó có khả năng ảnh hởng đến các giao
            diện trong hệ thống quản lý ni dung. Chúng tôi sẻ quét và dọn dẹp sạch các mã độc hại này
          </div>
        </div>
      </div>
      <div class="accordion-item">
        <div class="accordion-item-header">
          BACKDOORS AND WEBSHELLS
        </div>
        <div class="accordion-item-body">
          <div class="accordion-item-body-content">
            BACKDOORS là một phần mềm độc hại sẽ bỏ qua quy trình xác thực thông thường và có quyền truy cập
            vào hệ thống của bạn. Một khi backdoor được cài đặt, rất khó để loại bỏ. Mặt khác, WEBSHELLS là
            tập lệnh có thể được tải lên máy chủ web và cho phép hacker quản trị máy chủ từ xa. Chúng tôi
            phát hiện và loại bỏ các web-shell độc hại và các cửa hậu được cài đặt trái phép.
          </div>
        </div>
      </div>
      <div class="accordion-item">
        <div class="accordion-item-header">
          WEB MAILER SCRIPT
        </div>
        <div class="accordion-item-body">
          <div class="accordion-item-body-content">
            Web Mailer scripts được kẻ tấn công chèn vào hệ thống nhàm mục đích phát tán thư rác khắp nơi,
            dẫn đến trang web của bạn sẻ bị đánh dấu spam trên công cụ tìm kiếm, địa chỉ máy chủ lưu trữ...
          </div>
        </div>
      </div>
      <div class="accordion-item">
        <div class="accordion-item-header">
          PHARMACEUTICAL INJECTIONS
        </div>
        <div class="accordion-item-body">
          <div class="accordion-item-body-content">
            Pharma hack được thực hiện bởi một kỹ thuật gọi là Black Hat SEO. Điều này có thể Hacker làm
            website của bạn không thể ngóc đầu lên trong việc SEO các từ khóa lên top Google được hoặc
            Hacker cũng sẻ chèn backlink ẩn bên trong website của bạn. Chúng tôi sẻ quét và gỡ bỏ toàn bộ
            mã độc và backlinks ẩn trong website và giúp website của bạn đạt được thứ hạng bình thường.
          </div>
        </div>
      </div>

    </div>
  </div>
</section>

<style type="text/css" media="screen">
.accordion {
    width: 90%;
    max-width: 1000px;
    margin: 2rem auto;
}

.accordion-item {
    background-color: #fff;
    color: #111;
    margin: 1rem 0;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.25);
}

.accordion-item-header {
    padding: 0.5rem 3rem 0.5rem 1rem;
    min-height: 3.5rem;
    line-height: 1.25rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.accordion-item-header::after {
    content: "\002B";
    font-size: 2rem;
    position: absolute;
    right: 1rem;
}

.accordion-item-header.active::after {
    content: "\2212";
}

.accordion-item-body {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
}

.accordion-item-body-content {
    padding: 1rem;
    line-height: 1.5rem;
    border-top: 1px solid;
    border-image: linear-gradient(to right, transparent, #34495e, transparent) 1;
}


.accordion {
    width: 100%;
}

.accordion-item {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Thêm các style cho accordion-item khi active */
.accordion-item.active {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.accordion-item-header {
    padding: 15px;
    background-color: #f7f7f7;
    cursor: pointer;
    position: relative;
    font-weight: bold;
    transition: all 0.3s ease;
}

.accordion-item-header.active {
    background-color: #e7f1ff;
}

/* Thêm icon + và - */
.accordion-item-header::after {
    content: '+';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5em;
}

.accordion-item-header.active::after {
    content: '-';
}

.accordion-item-body {
    overflow: hidden;
    transition: max-height 0.3s ease;
    max-height: 0;
}

.accordion-item-body-content {
    padding: 15px;
    line-height: 1.5;
}

/* Style bổ sung cho accordion-item active */
.accordion-item.active .accordion-item-body {
    max-height: 200px; /* Giá trị này sẽ bị ghi đè bởi JavaScript */
}

.active .accordion-item-body{
 display: block!important;
}
</style>

<script type="text/javascript">

const accordionItemHeaders = document.querySelectorAll(".accordion-item-header");

accordionItemHeaders.forEach(accordionItemHeader => {
    accordionItemHeader.addEventListener("click", event => {
        // Lấy phần tử cha (accordion-item)
        const accordionItem = accordionItemHeader.parentElement;
        
        // Đóng tất cả các accordion items khác đang mở
        const allActiveItems = document.querySelectorAll(".accordion-item.active");
        allActiveItems.forEach(activeItem => {
            // Bỏ qua nếu đó là item được click
            if (activeItem !== accordionItem) {
                // Remove class active từ item
                activeItem.classList.remove("active");
                
                // Remove class active từ header
                const activeHeader = activeItem.querySelector(".accordion-item-header");
                activeHeader.classList.remove("active");
                
                // Đóng body
                const activeBody = activeItem.querySelector(".accordion-item-body");
                activeBody.style.maxHeight = 0;
                
                // Sử dụng setTimeout để đợi animation hoàn thành trước khi ẩn hoàn toàn
                setTimeout(() => {
                    // Chỉ ẩn khi phần tử vẫn không active
                    if (!activeItem.classList.contains("active")) {
                        activeBody.style.display = "none";
                    }
                }, 300);
            }
        });

        // Toggle class active cho cả accordion item hiện tại
        const isActive = accordionItem.classList.toggle("active");
        
        // Toggle class active cho header hiện tại
        accordionItemHeader.classList.toggle("active");
        
        const accordionItemBody = accordionItemHeader.nextElementSibling;
        
        if (isActive) {
            // Mở accordion item hiện tại
            accordionItemBody.style.display = "block";
            // Dùng setTimeout để đảm bảo display: block được áp dụng trước khi set maxHeight
            setTimeout(() => {
                accordionItemBody.style.maxHeight = accordionItemBody.scrollHeight + "px";
            }, 10);
        } else {
            // Đóng accordion item hiện tại
            accordionItemBody.style.maxHeight = 0;
            setTimeout(() => {
                if (!accordionItem.classList.contains("active")) {
                    accordionItemBody.style.display = "none";
                }
            }, 300);
        }
    });
});
</script>



<section class="process">
    <div class="innerContainer">
        <div class="row">

            <div class="col-12 lead-heading">
                <h2 style="font-size:27px;margin-bottom: 2px">Một số hình ảnh cho thấy website đã bị hack</h2>
                <p style="font-size: 16px">Nguyên nhân tại sao website của bạn bị hack thì bạn có thể xem <a
                        href="https://vutruso.com/nguyen-nhan-cac-trang-web-wordpress-bi-hack/">bài viết này</a></p>
                <div class="separator_wrapper bounce_fade animated bounceIn">
                    <div class="separator_first_circle">
                        <div class="separator_second_circle"></div>
                    </div>
                </div>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6 mb-4">
                <a href="https://vutruso.com/wp-content/uploads/2020/08/web-bi-hack.jpg">
                    <img alt="Trang chủ website của bạn bị hacker thay đổi"
                        src="https://vutruso.com/wp-content/uploads/2020/08/web-bi-hack.jpg">
                </a>
                <p class="text-center">Trang chủ website của bạn bị hacker thay đổi</p>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6 mb-4">
                <a href="https://vutruso.com/wp-content/uploads/2020/08/chen-ma-doc-virut.png">
                    <img alt="Website của bạn bị chèn các liên kết độc hại"
                        src="https://vutruso.com/wp-content/uploads/2020/08/chen-ma-doc-virut.png">
                </a>
                <p class="text-center">Website của bạn bị chèn các liên kết độc hại</p>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6 mb-4">
                <a href="https://vutruso.com/wp-content/uploads/2020/08/website-dinh-virus-tieng-nhat-tieng-tau.png">
                    <img alt="website index tiếng Nhật"
                        src="https://vutruso.com/wp-content/uploads/2020/08/website-dinh-virus-tieng-nhat-tieng-tau.png">
                </a>
                <p class="text-center">Kết quả tìm kiếm website của bạn trên google hiển thị tiêu đề và mô tả không
                    chính xác, có nhiều trường hợp bị chuyển sang tiếng nhật,...</p>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6 mb-4">
                <a href="https://vutruso.com/wp-content/uploads/2020/08/website-bi-google-canh-bao-mau-do.jpg">
                    <img alt="Website bị google cảnh báo"
                        src="https://vutruso.com/wp-content/uploads/2020/08/website-bi-google-canh-bao-mau-do.jpg">
                </a>
                <p class="text-center">Website của bạn bị GOOGLE cảnh báo</p>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6 mb-4">
                <a
                    href="https://vutruso.com/wp-content/uploads/2020/08/khong-the-login-vao-trang-quan-tri-wordpress.jpg">
                    <img alt="Không thể login vào wp"
                        src="https://vutruso.com/wp-content/uploads/2020/08/khong-the-login-vao-trang-quan-tri-wordpress.jpg">
                </a>
                <p class="text-center">Không thể đăng nhập vào WordPress</p>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6 mb-4">
                <a href="https://vutruso.com/wp-content/uploads/2020/08/acquisition-overview.png">
                    <img alt="Traffic bị giảm đột ngột"
                        src="https://vutruso.com/wp-content/uploads/2020/08/acquisition-overview.png">
                </a>
                <p class="text-center">Lượng truy cập của website bạn giảm đột ngột thông qua báo cáo kết quả Google
                    Analytics</p>
            </div>


        </div>
    </div>
</section>






<section id="services" class="section_wrapper">
    <div class="section_box innerContainer">
        <div class="row">

            <div class="twelvecol align_center col-md-12 col-sm-12">
                <h3 class="subtitle right_fade animated fadeInRight">Chúng tôi sẽ kiểm tra tất cả các vấn đề tồn tại
                    trong website và tiến hành fix tất cả các lỗi bảo mật</h3>
            </div>

            <div class="col-lg-4 mal">
                <ul>
                    <li>Dirty Search Engine Results</li>
                    <li>Hidden &amp; Malicious iFrames</li>
                    <li>Mobile Malware Infections</li>
                    <li>Phishing Lures</li>
                    <li>Hijacked Websites</li>
                    <li>Obfuscated JavaScript Injections</li>
                    <li>Redirects Targeting Mobile Devices</li>
                    <li>Drive-by-Download Injections</li>
                </ul>
            </div>
            <div class="col-lg-4 mal">
                <ul>
                    <li>Pharma Hacks</li>
                    <li>Desktop AntiVirus Blacklisting</li>
                    <li>Infected Database / SQL Injections</li>
                    <li>Website Defacements</li>
                    <li>Embedded Trojans</li>
                    <li>Cross-Site Scripting (XSS) Infections</li>
                    <li>Search Engine Blacklisting</li>
                    <li>Email Spam Abuse</li>
                </ul>
            </div>
            <div class="col-lg-4 mal">
                <ul>
                    <li>Known Website Hacks</li>
                    <li>Security Issues and Anomalies</li>
                    <li>Blacklist Warnings</li>
                    <li>Downtime</li>
                    <li>SSL Certificate Changes</li>
                    <li>DNS Record Changes</li>
                    <li>Core File Integrity</li>
                    <li>Blackhat SEO Spam Injections</li>
                </ul>
            </div>

            <style type="text/css" media="screen">
            .mal li {
                font-size: 15px;
                line-height: 26px;
                margin: 0 0 10px 0;
                padding: 0 15px 0 20px;
                background: url(https://vutruso.com/wp-content/uploads/2020/08/li-bg.png) no-repeat left 5px;
            }

            .home_services {
    padding: 0 25px;
}
            </style>

        </div>

<div class="row">
            <div class="twelvecol align_center col-md-12 col-sm-12">
                <h2 class="title left_fade animated fadeInLeft" style="text-transform: capitalize;margin-top: 3%">Gỡ bỏ
                    mã độc hoàn toàn khỏi WordPress</h2>
                <div class="separator_wrapper bounce_fade animated bounceIn text-center">
                    <div class="separator_first_circle">
                        <div class="separator_second_circle">
                        </div>
                    </div>
                </div>
                <h3 class="subtitle right_fade animated fadeInRight">Vũ Trụ Số cung cấp dịch vụ chất lượng cao giúp gỡ
                    bỏ hoàn toàn mã độc ra khỏi website của bạn, giúp website hoạt động ổn định, sạch sẽ nhất. Đội ngũ
                    chúng tôi sẻ cấu hình bảo mật giúp ngăn chặn các hành vi tấn công hoặc xâm nhập website trái phép từ
                    hacker</h3>

            </div>




            <div class="home_services bounce_fade animated bounceIn col-md-6 col-sm-12 col-xl-6">
                <span class="circle_icons">
                    <i class="fa fa-repeat" aria-hidden="true"></i>
                </span>
                <h4>Sửa chữa nhanh các website bị hacker tấn công</h4>
                <p>Nắm bắt thông tin, phân tích tình trạng website gặp phi để cho ra phương án sửa chửa tối ưu, tránh
                    thất thoát dữ liệu của khách hàng và giúp website hoạt động lại như trước khi bị nhiễm mã độc là
                    điều chúng tôi luôn nổ lực làm việc trong những năm qua.
                </p>
            </div>
            <div class="home_services bounce_fade animated bounceIn last col-md-6 col-sm-12 col-xl-6">
                <span class="circle_icons"><i class="fa fa-bug" aria-hidden="true"></i></span>
                <h4>Diệt toàn bộ mã độc (Malware) trên website</h4>
                <p>Website của bạn hiển thị quảng cáo popup hoặc chuyển hướng sang 1 trang website khc một cách bất
                    thường thì chắc chắn website của bạn đã bị dính mã độc rồi đấy. Hãy gọi cho chúng tôi ngay để được
                    trợ giải quyết sự cố.</p>
            </div>
            <div class="home_services bounce_fade animated bounceIn col-md-6 col-sm-12 col-xl-6">
                <span class="circle_icons"><i class="fa fa-shield" aria-hidden="true"></i></span>
                <h4>Gỡ bỏ tên miền khỏi danh sách đen (Domain Blacklist)</h4>
                <p>Chúng tôi hỗ trợ xóa tên miền khỏi danh sch đen của Google hoặc các máy chủ DNS khắp thế giới nhanh
                    chóng. Một số trường hợp dính <strong>quảng cáo Google adwords</strong> từ chối quảng cáo của bạn,
                    chúng tôi sẽ hỗ trợ gỡ bỏ mã độc và phần mềm độc hại và khôi phục quảng cáo</p>
            </div>
            <div class="home_services bounce_fade animated bounceIn last col-md-6 col-sm-12 col-xl-6">
                <span class="circle_icons"><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></span>
                <h4>Fix các lỗi bảo mật còn tồn tại trên website</h4>
                <p>Vũ Trụ Số sẻ tiến hành rà soát lại toàn bộ lỗi đang tồn tại trên website của bạn và chúng tôi sẻ vá
                    các lỗ hỏng bảo mật có nguy cơ bị hacker tấn công cao.
                </p>
            </div>
            <div class="home_services bounce_fade animated bounceIn col-md-6 col-sm-12 col-xl-6">
                <span class="circle_icons"><i class="fa fa-database" aria-hidden="true"></i></span>
                <h4>Tối ưu và sửa chữa lỗi cơ sở dữ liệu (Repair SQL Database)</h4>
                <p>Chuyên viên của chúng tôi sẻ sửa chữa các cơ sở dữ liệu bị tấn công, tối ưu các bảng cơ sở dữ liệu để
                    website của bạn được truy suất dữ liệu nhanh hơn</p>
            </div>
            <div class="home_services bounce_fade animated bounceIn col-md-6 col-sm-12 col-xl-6">
                <span class="circle_icons"><i class="fa fa-lock" aria-hidden="true"></i></span>
                <h4>Tăng cường bảo mật cho VPS, Hosting và tổng thể Website</h4>
                <p>Ngoài tối ưu mã nguồn, chúng tôi cũng phải quét và tăng cường bảo mật ở VPS hoặc Server giúp tăng
                    cưng độ bảo mật của website lên mức cao nhất</p>
            </div>
            <ul>

        </div>
    </div>
</section>

<style type="text/css" media="screen">
.last {
    margin-right: 0px;
}

.home_services p,
.process-step p {
    font-size: 14px;
    line-height: 24px;
    font-weight: 300;
    color: #656565;
}

.section_wrapper {
    width: 100%;
    font-size: 20px;
    background-color: #EBF7FF;
}

.section_box {
    margin: 0 auto;
    padding-top: 4%;
    padding-bottom: 70px;
    padding-left: 5%;
    padding-right: 5%;
    position: relative;
    margin-top: -1%;
}

h2.title {
    margin-bottom: 3px;
    display: block;
    clear: both;
    font-size: 28px;
    line-height: 38px;
    text-align: center;
    font-weight: 400;
    color: #454545;
    font-family: 'Baloo Chettan 2';
}

h3.subtitle {
    margin: 0 0 50px 0;
    display: block;
    clear: both;
    font-size: 16px;
    line-height: 28px;
    text-align: center;
    font-weight: 300;
    color: #656565;
}

.separator_wrapper {
    margin-bottom: 10px;
}

.separator_first_circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    border: 3px solid #3498db;
    -moz-border-radius: 7px;
    -webkit-border-radius: 7px;
    border-radius: 7px;
}

.separator_second_circle {
    float: left;
    width: 4px;
    height: 4px;
    background-color: #3498db;
    border: 2px solid #fff;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

.separator_wrapper::after,
.separator_wrapper::before {
    background-color: #3498db;
    display: inline-block;
    vertical-align: middle;
    content: "";
    width: 70px;
    height: 1px;
    margin-top: -6px;
}

.home_services {
    margin-bottom: 35px;
}

.home_services h4 {
    font-size: 16px;
    margin-bottom: 5px;
}

span.circle_icons i {
    margin: 5px 25px 10% 0;
    font-size: 46px;
    float: left;
    color: #3498db;
    transition: all .50s ease-in-out;
    -moz-transition: all .50s ease-in-out;
    -webkit-transition: all .50s ease-in-out;
}

.animated {
    -webkit-animation-duration: 1s;
    -moz-animation-duration: 1s;
    -o-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
}

.animated.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    -moz-animation-name: fadeInLeft;
    -o-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}

.animated.fadeInRight {
    -webkit-animation-name: fadeInRight;
    -moz-animation-name: fadeInRight;
    -o-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

.animated.bounceIn {
    -webkit-animation-name: bounceIn;
    -moz-animation-name: bounceIn;
    -o-animation-name: bounceIn;
    animation-name: bounceIn;
}

[class*=" icon-"]:before {
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    speak: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/*! CSS Used keyframes */
@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
}

@-moz-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -moz-transform: translateX(-20px);
    }

    100% {
        opacity: 1;
        -moz-transform: translateX(0);
    }
}

@-o-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -o-transform: translateX(-20px);
    }

    100% {
        opacity: 1;
        -o-transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }

    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@-webkit-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
}

@-moz-keyframes fadeInRight {
    0% {
        opacity: 0;
        -moz-transform: translateX(20px);
    }

    100% {
        opacity: 1;
        -moz-transform: translateX(0);
    }
}

@-o-keyframes fadeInRight {
    0% {
        opacity: 0;
        -o-transform: translateX(20px);
    }

    100% {
        opacity: 1;
        -o-transform: translateX(0);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(20px);
    }

    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@-webkit-keyframes bounceIn {
    0% {
        opacity: 0;
        -webkit-transform: scale(.3);
    }

    50% {
        opacity: 1;
        -webkit-transform: scale(1.05);
    }

    70% {
        -webkit-transform: scale(.9);
    }

    100% {
        -webkit-transform: scale(1);
    }
}

@-moz-keyframes bounceIn {
    0% {
        opacity: 0;
        -moz-transform: scale(.3);
    }

    50% {
        opacity: 1;
        -moz-transform: scale(1.05);
    }

    70% {
        -moz-transform: scale(.9);
    }

    100% {
        -moz-transform: scale(1);
    }
}

@-o-keyframes bounceIn {
    0% {
        opacity: 0;
        -o-transform: scale(.3);
    }

    50% {
        opacity: 1;
        -o-transform: scale(1.05);
    }

    70% {
        -o-transform: scale(.9);
    }

    100% {
        -o-transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(.3);
    }

    50% {
        opacity: 1;
        transform: scale(1.05);
    }

    70% {
        transform: scale(.9);
    }

    100% {
        transform: scale(1);
    }
}
</style>


<section class="process">
    <div class="innerContainer">
        <div class="row">
            <div class="col-12">
                <div class="lead-heading">
                    <h2 style="font-size:22px;margin-bottom: 2px">QUY TRÌNH SỬA CHỮA GỒM 3 BƯỚC</h2>
                    <div class="separator_wrapper bounce_fade animated bounceIn">
                        <div class="separator_first_circle">
                            <div class="separator_second_circle">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="clearfix process-container">
                    <div class="one-third first process-step col-md-6 col-lg-4 col-sm-12">
                        <div class="icon">
                            <img src="<?php echo get_template_directory_uri(); ?>/img/icon/scan.png" alt="Scan"
                                title="Scan">
                        </div>
                        <h4>1. Quét qua toàn bộ website</h4>
                        <p>Chúng tôi sẻ tiến hành quét qua toàn bộ website của quý khách hàng bằng những công cụ thông
                            minh giúp xác định được vị trí bị dính mã độc, Tùy theo dữ liệu của khách hàng mà quá trình
                            này diễn ra nhanh hoặc chậm</p>
                    </div>
                    <div class="one-third process-step col-md-6 col-lg-4 col-sm-12">
                        <div class="icon">
                            <img src="<?php echo get_template_directory_uri(); ?>/img/icon/remove.png" alt="Clean"
                                title="Clean">
                        </div>
                        <h4>2. Tiến hành xử lý mã độc khỏi WordPress</h4>
                        <p>Tiến hành phân tích và gỡ bỏ các đoạn mã độc tồn tại trên website hoặc trên VPS, Hosting cho
                            quý khách hàng (Thời gian xử lý tùy vào độ phức tạp của mã độc và mức độ lan tỏa của mã độc
                            trên website (Thông thường là 12-24h)</p>
                    </div>
                    <div class="one-third process-step col-md-6 col-lg-4 col-sm-12">
                        <div class="icon">
                            <img src="<?php echo get_template_directory_uri(); ?>/img/icon/optimize.png"
                                alt="Development" title="Optimize">
                        </div>
                        <h4>3. Tối ưu và bàn giao website</h4>
                        <p>Chúng tôi sẻ tối ưu hóa cơ sở dữ liệu, fix các lỗi bảo mật khác đang tồn tại trên website và
                            cấu hình tường lửa tăng cường bảo mật cho website sau đó tiến hành bàn giao website cho
                            khách hàng</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style type="text/css" media="screen">
.clearfix::before,
.wrap::before {
    content: " ";
    display: table;
}

.clearfix::after,
.wrap::after {
    clear: both;
    content: " ";
    display: table;
}

.first {
    clear: both;
    margin-left: 0;
}

.process {
    padding: 23px 0;
    background-color: #EBF7FF;
    background-image: url(https://vutruso.com/wp-content/themes/vutruso/img/web-bg/sekcja3_bg.jpg);
}

.process-step .icon img {
    max-width: 200px;
}

.lead-heading {
    text-align: center;
    margin: 0 auto;
    width: 100%;
    clear: both;
    display: block;
    margin-bottom: 2%;
}

.lead-heading h2 {
    font-size: 4.2rem;
    font-family: 'Baloo Chettan 2';
}

.lead-heading p {
    font-size: 2.2rem;
    margin-bottom: 0;
}

.process-step {
    text-align: center
}

.process-step h4 {
    font-size: 18px;
    padding-bottom: 14px;
}

::-webkit-input-placeholder {
    color: inherit;
    opacity: .54;
}

.process-container::before,
.process-container::after {
    box-sizing: inherit;
}

.one-third {
    float: left;
    margin-left: 2.564102564102564%;
    width: 30.623931623931625%;
}


@media only screen and (max-width:860px) {
    .one-third {
        margin: 0;
        width: 100%;
    }
}
</style>


<section class="service-section">
    <div class="innerContainer">
        <div class="row">

            <div class="col-12">
                <div class="lead-heading" style=" margin-top: 4%; ">
                    <h2 style="font-size:22px;margin-bottom: 2px">Công việc loại bỏ malware khỏi WordPress</h2>
                    <div class="separator_wrapper bounce_fade animated bounceIn">
                        <div class="separator_first_circle">
                            <div class="separator_second_circle"></div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-8 service-column">
                <div class="service-content centred">
                    <div class="row">
                        <div class="col-md-6 col-sm-6 col-xs-12 service-column">
                            <div class="single-item wow slideInUp animated" data-wow-delay="0ms"
                                data-wow-duration="1500ms"
                                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: slideInUp;">
                                <div class="img-box"><i class="fa fa-spinner fa-2x"></i></div>
                                <div class="lower-content">
                                    <h5 itemprop="headline"><a href="#">Quét toàn bộ website</a></h5>
                                    <div class="text">
                                        <p>Chúng tôi sẽ quét toàn bộ website để phát hiện các tệp bị nhiễm mã độc, sau
                                            đó chúng tôi sẽ sửa / xóa các tệp bị nhiễm và chia sẽ chúng với bạn trong
                                            báo cáo</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-12 service-column">
                            <div class="single-item wow slideInUp animated" data-wow-delay="0ms"
                                data-wow-duration="2000ms"
                                style="visibility: visible; animation-duration: 2000ms; animation-delay: 0ms; animation-name: slideInUp;">
                                <div class="img-box"><i class="fa fa-database fa-2x"></i></div>
                                <div class="lower-content">
                                    <h5 itemprop="headline"><a href="#">Dọn dẹp và tối ưu cơ sở dữ liệu</a></h5>
                                    <div class="text">
                                        <p>Chuyên gia bảo mật của chúng ti sẽ dọn dẹp cơ sở dữ liệu, sửa chữa các bảng
                                            bị hỏng và tối ưu hóa cơ sở dữ liệu để đảm bảo an toàn cho trang web của
                                            bạn.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6 service-column">
                            <div class="single-item wow slideInUp animated" data-wow-delay="0ms"
                                data-wow-duration="2500ms"
                                style="visibility: visible; animation-duration: 2500ms; animation-delay: 0ms; animation-name: slideInUp;">
                                <div class="img-box"><i class="fa fa-shield fa-2x"></i></div>
                                <div class="lower-content">
                                    <h5 itemprop="headline"><a href="#">Kiểm toán bảo mật toàn trang</a></h5>
                                    <div class="text">
                                        <p>Chúng tôi sẽ chạy kiểm toán bảo mật chi tiết trên trang web của bạn để phát
                                            hiện và báo cáo về các lỗ hổng trên trang web của bạn.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6 service-column">
                            <div class="single-item wow slideInUp animated" data-wow-delay="0ms"
                                data-wow-duration="3000ms"
                                style="visibility: visible; animation-duration: 3000ms; animation-delay: 0ms; animation-name: slideInUp;">
                                <div class="img-box"><i class="fa fa-tasks fa-2x"></i></div>
                                <div class="lower-content">
                                    <h5><a href="#">Kiểm tra thủ công các tập tin quan trọng</a></h5>
                                    <div class="text">
                                        <p>Htaccess, WP-config, index.php là các tệp dữ liệu nhạy cảm và dễ bị tấn công
                                            thông qua tấn công backdoor. Chúng tôi sẽ kiểm tra kỹ lưỡng và kiểm tra cẩn
                                            thận, làm sạch các tệp này</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-sm-6 service-column">
                            <div class="single-item wow slideInUp animated" data-wow-delay="0ms"
                                data-wow-duration="3000ms"
                                style="visibility: visible; animation-duration: 3000ms; animation-delay: 0ms; animation-name: slideInUp;">
                                <div class="img-box"><i class="fa fa-wrench fa-2x"></i></div>
                                <div class="lower-content">
                                    <h5 itemprop="headline"><a href="#">Sửa lỗi bảo mật</a></h5>
                                    <div class="text">
                                        <p>Chúng tôi sẽ tin hành quét trang web hoàn chỉnh và phân tích nguyên nhân làm
                                            website bị xâm nhập. Chúng tôi sẽ tìm và vá các lỗ hổng nhằm ngăn chặn các
                                            hành vi xâm nhập trái phép</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-sm-6 service-column">
                            <div class="single-item wow slideInUp animated" data-wow-delay="0ms"
                                data-wow-duration="3000ms"
                                style="visibility: visible; animation-duration: 3000ms; animation-delay: 0ms; animation-name: slideInUp;">
                                <div class="img-box"><i class="fa fa-exclamation fa-2x"></i></div>
                                <div class="lower-content">
                                    <h5 itemprop="headline"><a href="#">Xóa cảnh báo Blacklist</a></h5>
                                    <div class="text">
                                        <p>Nếu trang web của bạn bị Google, Norton, Yahoo hoặc AdWord liệt kê vào danh
                                            sách đen, chúng tôi sẽ thực hiện tất cả các bước cần thiết để xóa tất cả các
                                            cảnh báo trong danh sách đen</p>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-6 col-xs-12 service-column service-column-bao-gia">
                <div class="form-area  wow slideInRight animated" data-wow-delay="0ms" data-wow-duration="1500ms"
                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: slideInRight;">
                    <div class="title" id="bao-gia">Tư vấn và báo giá</div>
                    <?php echo do_shortcode('[contact-form-7 id="6024" title="Gỡ malware"]'); ?>
                </div>
            </div>
        </div>
    </div>
</section>


<style type="text/css" media="screen">
.img-box i {
    line-height: 2.3em
}


.service-section .wpcf7-form {
    padding-bottom: 18%;
}

.service-section .wpcf7-response-output {
    margin: 0em 0.5em 1em;
    border: 2px solid #ff0000;
}

.service-section .wpcf7-submit {
    position: relative;
    display: block;
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center;
    height: 55px;
    width: 100%;
    border: 2px solid #111;
    background: #111;
    color: #fff;
    transition: all 500ms ease;
    cursor: pointer;
}

.service-section span.wpcf7-not-valid-tip {
    color: #111;
    font-size: 1em;
    font-weight: normal;
    display: block;
}

.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight;
}

.slideInUp {
    -webkit-animation-name: slideInUp;
    animation-name: slideInUp;
}

.form-group {
    margin-bottom: 15px;
}

.centred {
    text-align: center;
}

.btn-two {
    position: relative;
    color: #222;
    font-size: 14px;
    background: transparent;
    padding: 3px;
    position: relative;
    border-bottom: 2px solid #df5626;
    cursor: pointer;
    font-weight: 700;
    display: inline-block;
    text-align: center;
    z-index: 1;
}

.btn-two:hover {
    color: #df5626;
}

.service-section {
    position: relative;
    padding: 10px 0px 10px 0px;
}

.service-section .service-title .title {
    position: relative;
    font-size: 32px;
    line-height: 57px;
    color: #222;
    margin-bottom: 6px;
}

.service-section .service-title p {
    font-style: italic;
    font-size: 16px;
    margin-top: 2%;
}

.service-section .service-title {
    position: relative;
    margin-bottom: 20px;
}

.service-section .service-content .single-item {
    position: relative;
    border: 1px solid #eee;
    padding: 5px;
    margin-bottom: 20px;
    transition: all 500ms ease;
    min-height: 230px;
    border-radius: 7px;
}

.service-section .service-content .single-item:hover {
    box-shadow: 0px 0px 20px 2px rgba(0, 0, 0, 0.10);
}

.service-section .service-content .single-item .img-box {
    margin-bottom: 20px;
    overflow: hidden;
    display: inline-block;
    border-radius: 50%;
    background: #71d456;
    width: 64px;
    height: 64px;
}

.service-section .service-content .service-column:nth-child(2) .single-item .img-box {
    background: #fbd303;
}

.service-section .service-content .service-column:nth-child(3) .single-item .img-box {
    background: #4b5aa7;
}

.service-section .service-content .service-column:nth-child(4) .single-item .img-box {
    background: #ff553e;
}

.service-section .service-content .service-column:nth-child(5) .single-item .img-box {
    background: #ff553e
}

.service-section .service-content .service-column:nth-child(6) .single-item .img-box {
    background: #fbd303;
}

.service-section .service-content .single-item .img-box img {
    width: 100%;
}

.service-section .service-content .single-item h5 {
    margin-bottom: 15px;
    font-size: 17px
}

.lower-content p {
    font-size: 14px;
    line-height: 21px;
    ;
}

.service-section .form-area {
    background: #df5626;
    padding: 36px 35px 17px 35px;
    left: 68px;
    box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.30);
    z-index: 1;
}

.form-group {
    margin-bottom: 0px;
}

.service-section .form-area input[type='tel'],
.service-section .form-area input[type='text'],
.service-section .form-area input[type='email'],
.service-section .form-area textarea {
    position: relative;
    width: 100%;
    background: transparent;
    border-bottom: 1px solid #efab93;
    margin-bottom: 0px;
    font-style: italic;
    color: #ffffff;
    padding: 33px 10px 13px 8px !important;
    transition: all 900ms ease;
    border-top: none;
    border-right: none;
    border-left: none;
    border-radius: 0;
}

.service-section .form-area input:focus,
.service-section .form-area textarea:focus {
    border-bottom: 1px solid #111;
    transition: all 900ms ease;
}

.service-section .form-area textarea {
    height: 168px;
    resize: none;
    margin-bottom: 22px;
}

::-webkit-input-placeholder {
    color: #fff;
}

:-ms-input-placeholder {
    color: #fff;
}

::-moz-placeholder {
    color: #fff;
    opacity: 1;
}

:-moz-placeholder {
    color: #fff;
    opacity: 1;
}

.service-section .form-area button {
    position: relative;
    display: block;
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center;
    height: 55px;
    width: 100%;
    border: 2px solid #111;
    background: #111;
    color: #fff;
    transition: all 500ms ease;
    cursor: pointer;
}

.service-section .form-area button:hover {
    background: transparent;
    color: #111;
    transition: all 500ms ease;
}

.service-section .form-area .title {
    position: relative;
    font-size: 30px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 15px;
}

.service-section .service-content .button {
    position: relative;
    margin-top: 39px;
}

@media only screen and (max-width: 1200px) {
    .service-section {
        padding-bottom: 80px;
    }
}

@media only screen and (max-width: 991px) {
    .service-section .service-content {
        margin-bottom: 50px;
    }
}

@media only screen and (max-width: 767px) {
    .service-section .service-content:before {
        display: none;
    }

    .service-section .service-content:after {
        display: none;
    }
}

/*! CSS Used keyframes */
@-webkit-keyframes slideInRight {
    0% {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }

    100% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInRight {
    0% {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }

    100% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@-webkit-keyframes slideInUp {
    0% {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        visibility: visible;
    }

    100% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInUp {
    0% {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        visibility: visible;
    }

    100% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
</style>



<div class="dt-sc-fullwidth-section " style="background-color:#fff;">
    <div class="container">
        <h2 itemprop="headline">Bảng giá dịch vụ gỡ bỏ mã độc khỏi website sử dụng mã nguồn WordPress</h2>
        <div class="separator_wrapper bounce_fade animated bounceIn" style="margin-bottom: 2px;text-align: center">
            <div class="separator_first_circle">
                <div class="separator_second_circle">
                </div>
            </div>
        </div>
        <div class="dt-sc-pricing-table type2">
            <table class="pricing-table one-plans">
                <tbody>
                    <tr class="table-header title-row-1">
                        <td>
                            <div class="table-title first-col">
                                <p class="packages">Nội dung công việc gỡ bỏ mã độc / xử lý mã độc cho mã nguồn
                                    WordPress</p>
                            </div>
                        </td>
                        <td class="plan1 last">
                            <div class="table-title">
                                <div class="head">
                                    <h3>Phí dịch vụ gỡ bỏ mã độc khỏi WP</h3>
                                    <p class="actual-price"><span style="font-size:15px">Chỉ từ</span> 800.000 VND</p>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr class="table-separator">
                        <td colspan="2">Xử lý và loại bỏ mã độc trên WordPress - Malware Removal Includes</td>
                    </tr>
                    <tr class="table-content">
                        <td>
                            <span title="Xóa b mã độc khỏi WordPress">Xóa bỏ mã độc khỏi WordPress</span>
                        </td>
                        <td class="last">
                            <span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span>
                        </td>
                    </tr>
                    <tr class="table-content">
                        <td>
                            <span title="1 website giới hạn 1000 trang">1 website giới hạn 2000 trang </span>
                        </td>
                        <td class="last">
                            <span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span>
                        </td>
                    </tr>
                    <tr class="table-content">
                        <td>
                            <span title="Cập nhật core và plugin sạch nhất cho WP">Cập nhật core và plugin sạch nhất cho
                                WP</span>
                        </td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-separator">
                        <td colspan="2">Quét lổ hỏng bảo mật - Vulnerability Scanning</td>
                    </tr>
                    <tr class="table-content">
                        <td>
                            <span title="Xóa b các backlink ẩn">Xóa bỏ các backlink ẩn (SEO Spam Injections)</span>
                        </td>
                        <td class="last">
                            <span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span>
                        </td>
                    </tr>
                    <tr class="table-content">
                        <td>
                            <span title="Loại bỏ hoàn toàn phần mềm độc hại khỏi Website">Loại bỏ hoàn toàn phần mềm độc
                                hại khỏi Website</span>
                        </td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-content">
                        <td> <span title="Xóa các tệp tin lừa đảo có trên VPS/Hosting">Xóa các tệp tin lừa đảo có trên
                                VPS/Hosting</span></td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-content">
                        <td> <span title="Xóa các chuyển hướng độc hại">Xóa các chuyển hướng độc hại</span></td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-separator">
                        <td colspan="2">Xóa IP website khỏi danh sách đen (Blacklist Removal)</td>
                    </tr>
                    <tr class="table-content">
                        <td><span title="Google Review Submission">Google Review Submission</span></td>
                        <td class="last">
                            <img class="size-full"
                                src="<?php echo get_template_directory_uri(); ?>/img/icon/malware-engine-logo-google.png"
                                alt="Google Safe Browsing Approved" width="227" height="66">
                        </td>
                    </tr>
                    <tr class="table-content">
                        <td> <span title="">Xóa Blacklist khỏi Google</span></td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-content">
                        <td><span title="Sửa các cảnh báo Google SERP">Sửa các cảnh báo Google SERP</span></td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-content">
                        <td>Gỡ bỏ cảnh báo độc hại của trình duyệt khi truy cập website</td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-content">
                        <td>Xóa Google index tiếng Nhật, Hàn hoặc index bậy bạ (giới hạn 1k index)<br>
                            <strong>Tùy theo số lượng link index chúng tôi sẽ báo giá cụ thể</strong>
                        </td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                    <tr class="table-separator">
                        <td colspan="2">Bảo hành</td>
                    </tr>
                    <tr class="table-content">
                        <td><span title="Hỗ trợ bảo hành trong 18 tháng">Hỗ trợ bảo hành trong 12 tháng</span></td>
                        <td class="last"><span class="fa fa-check-circle fa-lg" style="color:#08c43c;"></span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>


<style type="text/css" media="screen">
.dt-sc-pricing-table {
    float: left;
    line-height: 1;
    margin-bottom: 20px;
    padding: 0;
    width: 100%;
    margin-left: -3px;
    margin-top: 3%
}

.dt-sc-pricing-table.type2 table {
    border-left: none;
}

.dt-sc-pricing-table.type2 td {
    line-height: 28px;
    padding: 16px 15px;
    width: 155px;
    background-color: #fff;
}

.dt-sc-pricing-table.type2 td:first-child {
    font-size: 13px;
    padding-left: 0;
    text-align: left;
    font-weight: 500;
    text-transform: none;
}

.dt-sc-pricing-table.type2 td:last-child {
    border-right: none;
}

.dt-sc-pricing-table table {
    border-collapse: separate;
    border-spacing: 0;
    border-color: #dfdfdf;
    border-style: solid;
    border-width: 1px 0 0 1px;
    clear: both;
    margin-bottom: 20px;
    width: 100%;
    margin: 0px 0;
    padding: 0;
}

.dt-sc-pricing-table td {
    background: #fff;
    border-color: #dfdfdf;
    border-style: solid;
    border-width: 0 1px 1px 0;
    line-height: normal;
    text-align: center;
    vertical-align: middle;
    font-size: 14px;
    padding: 12px 10px;
}

.dt-sc-pricing-table img,
#primary img {
    height: auto;
    max-width: 100%;
}

.dt-sc-pricing-table img.size-full {
    max-width: 99.6%;
    height: auto;
}

.dt-sc-fullwidth-section {
    clear: both;
    float: left;
    margin: 0;
    padding: 0;
    width: 100%;
    margin-top: 3%;
}

.dt-sc-fullwidth-section h2 {
    font-size: 22px;
    margin-bottom: 2px;
    text-align: center;
    text-transform: capitalize;
    font-family: 'Baloo Chettan 2';
}

.dt-sc-pricing-table.type2 table.pricing-table {
    background: #fff none repeat scroll 0 0;
    border: 5px solid #d1d1d1;
    border-radius: 0;
    box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
    table-layout: fixed;
    width: 100%;
}

.dt-sc-pricing-table.type2 table.pricing-table .table-separator td {
    background: #e1e1e1;
    border-top: 1px solid #464646;
    padding: 10px;
    font-size: 14px;
    font-weight: 600;
}

.dt-sc-pricing-table.type2 table.pricing-table .table-header td,
.dt-sc-pricing-table.type2 table.pricing-table .table-content td {
    border-left: 1px solid #464646;
    border-top: 1px solid #464646;
    padding: 20px 6px 12px 6px;
    line-height: 18px;
}

.dt-sc-pricing-table.type2 table.pricing-table td.last {
    border-right: 1px solid #464646;
}

.pricing-table .title-row-1 .first-col h3 {
    color: #d47b09;
    font-size: 1.2em;
    font-weight: 600;
    padding: 5px 0;
    margin: 0px;
}

.pricing-table .title-row-1 .first-col p.packages {
    line-height: 38px;
    font-size: 1em;
    color: #233d87 !important;
    font-weight: 800;
    padding: 0 0 0 20px;
}

.pricing-table .order p.packages {
    font-size: 1.8em;
    color: #000 !important;
}

.pricing-table .order p.packages-fine-print {
    font-size: 0.9em;
    color: #000 !important;
    font-style: italic;
    line-height: 8pt;
    font-weight: 400;
    text-transform: none;
}

.pricing-table .table-header p {
    color: #fff !important;
}

.pricing-table .table-header {
    border-left: 1px solid #464646;
    border-top: 1px solid #464646;
    color: #000 !important;
}

.pricing-table .table-content {
    border-left: 1px solid #797979;
    border-top: 1px solid #797979;
}

.pricing-table .table-content td {
    font-size: 11px !important;
}

.pricing-table p {
    font-size: 1.0em;
    line-height: 15pt;
}

.pricing-table p.desc {
    font-size: 14px;
    margin: -1px 0 !important;
    white-space: nowrap;
    font-weight: 500;
}

.pricing-table p.actual-price {
    font-size: 28px;
    margin: 0px 0px 5px 0px !important;
    white-space: nowrap;
    font-weight: 700;
    font-style: italic;
}

.pricing-table p.note-price {
    font-size: 15px;
    margin: 0px 0px 0px 0px !important;
    white-space: nowrap;
    font-weight: 600;
}

.pricing-table p.gst-price {
    font-size: 12px;
    padding: 5px 0px;
    border-top: 1px dotted #cccccc;
    margin: 20px 0px 0px 0px;
    white-space: nowrap;
}

.pricing-table .table-header .plan1 {
    background: #f8981d !important;
    color: #fff !important;
}

.pricing-table .table-header .plan1 div.head:after {
    content: "";
    position: absolute;
    height: 48px;
    width: 48px;
    margin-left: -11px;
}

.pricing-table .table-header .plan1 div.head {
    position: relative;
}

.one-plans .table-header .plan1 {
    background: #f8981d !important;
    color: #fff !important;
}

.one-plans .table-header .plan1 div.head:before {
    background: none repeat scroll 0 0 #f8981d;
}

.one-plans .table-header .plan1 div.head:after {
    margin-top: -281px;
}

.one-plans .table-header.title-row-1 td {
    width: 40%;
}

.one-plans .table-header td.plan1 {
    width: 50%;
}

.one-plans .table-header .plan1 .actual-price {
    padding-top: 18px;
}

@media only screen and (min-width : 480px) {
    .dt-sc-pricing-table {
        margin-left: 0px;
        width: 100%;
    }
}

@media only screen and (min-width : 768px) {
    .pricing-table .table-content td {
        font-size: 13px !important;
    }

    .pricing-table p {
        font-size: 11pt;
        line-height: 15pt;
    }
}

@media only screen and (min-width:960px) and (max-width:1240px) {
    .dt-sc-pricing-table img {
        height: auto;
        max-width: 100%;
    }

    .dt-sc-pricing-table td {
        padding: 10px 5px;
        font-size: 12px;
    }
}

@media only screen and (min-width:768px) and (max-width:959px) {
    .dt-sc-pricing-table img {
        height: auto;
    }

    .dt-sc-pricing-table td {
        font-size: 12px;
    }
}

@media only screen and (max-width: 767px) {
    .dt-sc-pricing-table.type2 table {
        display: block;
        overflow-x: auto;
        position: relative;
        width: auto;
    }
}

@media only screen and (max-width: 479px) {
    .dt-sc-pricing-table td {
        font-size: 8px;
        padding: 8px 0;
    }

    .dt-sc-pricing-table.type2 td {
        font-size: 13px;
    }
}
</style>



<section class="common-section circle-bg">
    <div class="container">
        <div class="row justify-content-center align-items-center spaceTop75">
            <div class="col-md-6">
                <h2 class="small-title medium mb-4"><span class="border_bottom">Chất lượng được đảm bảo</span></h2>
                <p>Chúng tôi luôn đặt chất lượng và uy tín trong việc gỡ bỏ hoàn toàn mã độc khỏi website của quý khách
                    hàng, chúng tôi sẻ hoàn tiền cho quý khách 100% nếu khách hàng không hài lòng về chất lượng dịch vụ
                </p>
                <a href="<?php echo esc_url(home_url()); ?>/lien-he/"
                    class="btn btnAnimated yellow-btn-default mt-4">Liên hệ với chúng tôi ngay</a>
            </div>
            <div class="col-md-6">
                <div class="card-columns columns2 text-center">
                    <div class="card rounded-corner">
                        <img class="loaded"
                            src="https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/convert-file.svg"
                            alt="Dữ liệu được đảm bảo 100%" width="72" height="72">
                        <div class="card-body">
                            <h4 class="small-title bold">Dữ liệu được đảm bảo 100%</h4>
                            <p class="card-text">Chúng tôi luôn sao lưu dữ liệu trước khi thực hiện bất cứ sự thay đi
                                nào trên website</p>
                        </div>
                    </div>
                    <div class="card rounded-corner">
                        <img class="loaded"
                            src="https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/theme-customize.svg"
                            alt="Mọi sự cố sẽ được khắc phục" width="72" height="72">
                        <div class="card-body">
                            <h4 class="small-title bold">Mọi sự cố sẽ được khắc phục</h4>
                            <p class="card-text">Với sự chuyên nghiệp chúng tôi sẻ giải quyết mọi mã độc cứng đầu nhất
                                cho khách hàng</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<style type="text/css" media="screen">
.align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
}

.justify-content-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
}

.btn {
    display: inline-block;
    font-weight: 700;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.card {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem;
}

.card-body {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-body h4 {
    padding-bottom: 12%;
}

.text-center {
    text-align: center;
}

.btnAnimated {
    display: inline-block;
    position: relative;
    z-index: 1;
    font-size: 16px;
    color: #fff;
    outline: none;
    cursor: pointer;
    overflow: hidden;
    line-height: 42px;
    text-align: center;
    padding: 0 20px 0 45px;
    background: #ef323a;
    -webkit-border-radius: 28px;
    -moz-border-radius: 28px;
    -ms-border-radius: 28px;
    border-radius: 28px;
    -webkit-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
}

.btnAnimated:hover {
    text-decoration: none;
}

.btnAnimated:before {
    position: absolute;
    display: block;
    content: '';
    width: 20px;
    height: 20px;
    top: 11px;
    left: 19px;
    z-index: -1;
    pointer-events: none;
    background-color: unset;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    -webkit-box-shadow: 0 0 0 0 rgba(1, 42, 94, 1);
    box-shadow: 0 0 0 0 rgba(1, 42, 94, 1);
}

.btnAnimated:after {
    position: absolute;
    display: block;
    content: '';
    width: 22px;
    height: 22px;
    top: 10px;
    left: 18px;
    z-index: -1;
    pointer-events: none;
    background-color: transparent;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    -webkit-box-shadow: inset 0 0 0 5px #0c49c4;
    box-shadow: inset 0 0 0 5px #0c49c4;
}

.btnAnimated:hover:before {
    -webkit-transition: all .5s ease-in-out .2s;
    -o-transition: all .5s ease-in-out .2s;
    transition: all .5s ease-in-out .2s;
    -webkit-box-shadow: 0 0 0 30px rgba(0, 0, 0, 0);
    box-shadow: 0 0 0 30px rgba(0, 0, 0, 0);
}

.yellow-btn-default {
    background: #0c49c4;
    border-radius: 100px;
    -webkit-border-radius: 100px;
    font-size: 15px;
    text-transform: uppercase;
    color: #fff !important;
    line-height: 49px;
}

.yellow-btn-default:before {
    -webkit-box-shadow: 0 0 0 0 #fff;
    box-shadow: 0 0 0 0 #fff;
    top: 13px;
}

.yellow-btn-default:after {
    -webkit-box-shadow: inset 0 0 0 6px #fff;
    box-shadow: inset 0 0 0 6px #fff;
    top: 13px;
}

.yellow-btn-default:hover {
    background: #110F0F;
    color: #fff;
}

.small-title {
    font-size: 18px;
}

.spaceTop75 {
    padding-top: 75px;
}

.border_bottom {
    border-bottom: 2px solid #0c49c4;
    padding-bottom: 8px;
    display: inline-block;
    font-size: 28px
}

.bold {
    font-weight: 600;
}

.circle-bg {
    background: url(https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/top-circle.png) left top no-repeat, url(https://vutruso.com/wp-content/themes/vutruso/img/dich-vu/bottom-circle.png) 105% 190% no-repeat;
    background-color: rgba(195, 205, 225, 0.08);
}

.columns2 .card {
    height: auto;
    margin-bottom: 35px;
    padding-top: 30px;
    display: inline-block;
    width: 100%;
}

.rounded-corner {
    border: 0;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.common-section {
    background-repeat: no-repeat !important;
    clear: both;
}


@media screen and (min-width: 576px) and (max-width: 767px) {
    .card {
        -ms-flex: 1 0 44% !important;
        flex: 2 1 44% !important;
        margin-bottom: 25px !important;
    }

    .card-columns {
        -webkit-column-count: 3;
        -moz-column-count: 3;
        column-count: 2;
    }
}

@media (min-width:768px) {
    .card-columns {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;
        -webkit-column-gap: 1.25rem;
        -moz-column-gap: 1.25rem;
        column-gap: 1.25rem;
    }
}

@media (max-width:1024px) {
    .card-columns {
        padding-top: 32px;
    }

    .spaceTop75 {
        padding-top: 30px;
    }
}

@media (max-width: 768px) {
    .common-section p {
        font-size: 16px;
        line-height: 27px;
    }

    .card-body {
        padding: 20px;
    }

    .spaceTop75 {
        padding-top: 30px;
    }

    .yellow-btn-default {
        margin-bottom: 30px;
        margin-top: 15px !important;
    }
}

@media (max-width: 767px) {
    .yellow-btn-default {
        margin-bottom: 30px;
    }

    p {
        font-size: 18px;
    }
}

@media (max-width: 640px) {
    .spaceTop75 {
        padding-top: 30px;
    }
}

@media (max-width: 480px) {
    .spaceTop75 {
        padding-top: 30px;
    }
}
</style>

<?php //echo get_template_part('templates/footer-1', ''); ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Lấy tất cả headers
    const headers = document.querySelectorAll('.accordion-item-header');
    
    // Khởi tạo: Ẩn tất cả body
    document.querySelectorAll('.accordion-item-body').forEach(function(body) {
        body.style.display = 'none';
    });

    // Xử lý sự kiện click
    headers.forEach(function(header) {
        header.addEventListener('click', function() {
            // Lấy body tương ứng
            const body = this.nextElementSibling;
            
            // Nếu body đang ẩn, hiện nó lên và ẩn các body khác
            if (body.style.display === 'none') {
                // Ẩn tất cả body
                document.querySelectorAll('.accordion-item-body').forEach(function(item) {
                    item.style.display = 'none';
                });
                // Xóa class active từ tất cả headers
                document.querySelectorAll('.accordion-item-header').forEach(function(item) {
                    item.classList.remove('active');
                });
                
                // Hiện body được chọn
                body.style.display = 'block';
                this.classList.add('active');
            } else {
                // Nếu body đang hiện, ẩn nó đi
                body.style.display = 'none';
                this.classList.remove('active');
            }
        });
    });
});
</script>


<style>

</style>
<?php get_footer(); ?>