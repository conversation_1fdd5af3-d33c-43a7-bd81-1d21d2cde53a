<?php
/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

global $product;
global $woocommerce;

// Ensure visibility.
if ( empty( $product ) || ! $product->is_visible() ) {
	return;
}
?>


<div class="col-sm-12 col-md-6 col-lg-4">
	<div class="item" <?php wc_product_class( '', $product ); ?>>
	    <div class="thumb">
	    	<a href="<?php the_permalink(); ?>">
	    		<?php the_post_thumbnail("full",array('title'=>get_the_title(),'alt'=>get_the_title())); ?>
	    	</a>
	    </div>
	    <span class="vts-price"><?php echo $product->get_price_html(); ?><!-- <small>VNĐ</small> --></span>
	    <h3 itemprop="headline"><a itemprop="url" href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
	    <strong><svg width="12.5" height="12.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 8.25h15m-16.5 7.5h15m-1.8-13.5-3.9 19.5m-2.1-19.5-3.9 19.5" />
</svg>

                <?php 
                        global $post;
                        $terms = get_the_terms( $post->ID, 'product_cat' );        
                        foreach ($terms as $term){
                            $product_cat_id = $term->term_id;
                            echo $product_cat_name = $term->name;
                            break;
                        }
                 ?>
	    </strong>
	    
	    <div class="link-detail">
	    	<a itemprop="url" title="Chi tiết dự án <?php the_title(); ?>" href="<?php the_permalink(); ?>">CHI TIẾT
<svg width="13" height="13" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
</svg>

</a>
	    </div>
	</div>
</div>
