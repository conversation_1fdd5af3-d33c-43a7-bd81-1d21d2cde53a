<?php
/*
*===============================================================
* Related posts by tags in mid post
*===============================================================
*/
function vts_related_posts_by_tags() {
	if(is_single()){
		// Lấy danh sách tag ID của bài viết hiện tại
		$tags = wp_get_post_tags(get_the_ID(), array('fields' => 'ids'));
		
		// Kiểm tra nếu bài viết có tag
		if($tags) {
			$args = array(
				'post_type'             => 'post',
				'posts_per_page'        => 3,
				'tag__in'               => $tags, // Sử dụng tag__in thay vì category__in
				'post__not_in'          => array(get_the_ID()),
				'orderby'               => 'rand',
				'update_post_meta_cache' => false, // Không chạy query cho post meta
				'update_post_term_cache' => false, // Không chạy query cho terms
				'ignore_sticky_posts'    => true,  // Bỏ qua sticky posts
				'no_found_rows'          => true   // Không đếm số lượng bài viết
			);
			
			$the_query = new WP_Query($args);
			
			// The Loop
			if($the_query->have_posts()) {
				$string = "";
				$string .= '<div class="vts_news-list random">';
				$string .= '<h4 class="related-title">Bài viết liên quan</h4>';
				
				while($the_query->have_posts()) {
					$the_query->the_post();
					$string .= '<div class="news-itm clearfix"><div class="news-ttl"><i class="fa fa-check"></i>';
					$string .= '<a href="' . get_the_permalink() .'" rel="bookmark">'. get_the_title() .'</a></div></div>';
				}
				
				$string .= '</div>';
				wp_reset_postdata();
				
				return $string;
			}
		}
	}
	
	return ''; // Trả về chuỗi rỗng nếu không có bài viết nào
}

// Add a shortcode
add_shortcode('vts_related_posts_tags', 'vts_related_posts_by_tags');

// Thêm shortcode vào nội dung
add_filter('the_content', 'vts_content');
function vts_content($content){
    if (!is_single()) return $content;
    
    $paragraphAfter = 8; // Hiển thị sau paragraph thứ 8
    $content = explode("</p>", $content);
    $new_content = '';
    
    for ($i = 0; $i < count($content); $i++) {
        if ($i == $paragraphAfter) {
            $new_content .= do_shortcode('[vts_related_posts_tags]');
        }
        $new_content .= $content[$i] . "</p>";
    }
    
    return $new_content;
}