<?php

/* Featured image in wp-admin post and page and custom post types
*===============================================================*/

// Hàm này sẽ được gọi để thêm cột cho tất cả các post type được chỉ định
function vts_add_featured_image_column_to_post_types() {
    // Danh sách các post type bạn muốn thêm cột ảnh đại diện
    // Thêm các custom post type vào mảng này
    $post_types = array('post', 'page', 'thu-vien-anh'); // Thêm các custom post type của bạn ở đây
    
    // Lặp qua tất cả post type và thêm filter cho mỗi loại
    foreach ($post_types as $post_type) {
        add_filter('manage_' . $post_type . '_posts_columns', 'vts_featured_image_column');
        add_action('manage_' . $post_type . '_posts_custom_column', 'vts_render_the_column', 10, 2);
    }
}
// Kích hoạt việc thêm cột
add_action('init', 'vts_add_featured_image_column_to_post_types');

// Định nghĩa cột ảnh đại diện
function vts_featured_image_column($column_array) {
    // Thêm cột ảnh đại diện vào đầu danh sách các cột
    $column_array = array_slice($column_array, 0, 1, true)
    + array('featured_image' => 'Ảnh đại diện')
    + array_slice($column_array, 1, NULL, true);
 
    return $column_array;
}

// Chỉnh sửa hàm render để sử dụng background-image thay vì img tag
function vts_render_the_column($column_name, $post_id) {
    if ($column_name == 'featured_image') {
        // Nếu có ảnh đại diện
        if (has_post_thumbnail($post_id)) {
            $thumb_id = get_post_thumbnail_id($post_id);
            $image_url = wp_get_attachment_url($thumb_id);
            
            // Hiển thị div với background-image
            echo '<div class="vts-image-preview" data-id="' . $thumb_id . '" style="background-image: url(\'' . $image_url . '\')"></div>';
        } else {
            // Nếu không có ảnh đại diện, hiển thị ảnh mặc định
            $default_image = get_stylesheet_directory_uri() . '/img/nothumb.jpg';
            echo '<div class="vts-image-preview vts-no-image" data-id="-1" style="background-image: url(\'' . $default_image . '\')"></div>';
        }
    }
}

// Đảm bảo media scripts được load
add_action('admin_enqueue_scripts', 'vts_include_myuploadscript');
function vts_include_myuploadscript() {
    if (!did_action('wp_enqueue_media')) {
        wp_enqueue_media();
    }
}

// PHẦN BỊ THIẾU: Thêm trường ảnh đại diện vào Quick Edit cho tất cả post types
function vts_add_quick_edit_featured_image_to_post_types() {
    // Danh sách các post type - giữ đồng bộ với danh sách ở trên
    $post_types = array('post', 'page', 'thu-vien-anh'); // Thêm các custom post type của bạn ở đây
    
    foreach ($post_types as $post_type) {
        add_action('quick_edit_custom_box', function($column_name, $post_type_arg) use ($post_type) {
            // Chỉ thêm nếu đúng post type và đúng cột
            if ($column_name == 'featured_image' && $post_type_arg == $post_type) {
                vts_add_featured_image_quick_edit($column_name, $post_type);
            }
        }, 10, 2);
    }
}
add_action('init', 'vts_add_quick_edit_featured_image_to_post_types');

// Hiển thị trường ảnh đại diện trong Quick Edit
function vts_add_featured_image_quick_edit($column_name, $post_type) {
    // Chỉ thêm nếu chúng ta có cột ảnh đại diện
    if ($column_name != 'featured_image') return;
 
    echo '<fieldset id="vts_featured_image" class="inline-edit-col-left">
        <div class="inline-edit-col">
            <span class="title">Ảnh đại diện</span>
            <div>
                <a href="#" class="vts_upload_featured_image">Đặt ảnh đại diện</a>
                <input type="hidden" name="_thumbnail_id" value="" />
                <a href="#" class="vts_remove_featured_image">Xóa ảnh đại diện</a>
                <div class="vts-image-preview-quickedit"></div>
            </div>
        </div>
    </fieldset>';
}

// Cập nhật CSS
add_action('admin_head', 'vts_custom_css');
function vts_custom_css() {
    echo '<style>
        /* Thiết lập độ rộng cột */
        #featured_image {
            width: 100px;
            text-align: center;
        }
        
        /* Container ảnh với background */
        .vts-image-preview {
            width: 80px;
            height: 60px;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 0 auto;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        /* Hiệu ứng hover */
        .vts-image-preview:hover {
            border-color: #0073aa;
            box-shadow: 0 0 2px rgba(0,115,170,0.8);
        }
        
        /* Hiển thị khi không có ảnh */
        .vts-no-image {
            background-size: contain;
            background-color: #f7f7f7;
            opacity: 0.8;
        }
 
        /* CSS cho menu Quick Edit */
        #vts_featured_image .title {
            margin-top: 10px;
            display: block;
        }
        
        #vts_featured_image a.vts_upload_featured_image {
            display: inline-block;
            margin: 10px 0 0;
            padding: 5px 8px;
            background: #f7f7f7;
            border: 1px solid #ccc;
            border-radius: 3px;
            text-decoration: none;
            cursor: pointer;
        }
        
        #vts_featured_image a.vts_upload_featured_image:hover {
            background: #fafafa;
            border-color: #999;
        }
        
        /* Sử dụng background-image cho Quick Edit */
        #vts_featured_image .vts-image-preview-quickedit {
            width: 120px;
            height: 80px;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-top: 5px;
            display: none;
        }
        
        #vts_featured_image .vts_remove_featured_image {
            display: none;
            margin-top: 5px;
            color: #a00;
            text-decoration: none;
            font-size: 12px;
        }
        
        #vts_featured_image .vts_remove_featured_image:hover {
            color: #dc3232;
            text-decoration: underline;
        }
    </style>';
}

// JavaScript cập nhật để hỗ trợ background-image
add_action('admin_footer', 'vts_quick_edit_js_update');
function vts_quick_edit_js_update() {
    global $current_screen;
 
    // Danh sách các post type - cập nhật theo nhu cầu
    $post_types = array('post', 'page', 'thu-vien-anh'); 
    
    if (!$current_screen || !in_array($current_screen->post_type, $post_types)) {
        return;
    }
 
    ?><script>
    jQuery(function($) {
        // Xử lý click vào ảnh trong danh sách
        $('body').on('click', '.column-featured_image .vts-image-preview', function() {
            var post_id = $(this).closest('tr').attr('id').replace('post-', '');
            
            // Mở Media Library
            var frame = wp.media({
                title: 'Ảnh đại diện',
                library: {type: 'image'},
                multiple: false,
                button: {text: 'Đặt ảnh đại diện'}
            });
            
            // Khi chọn ảnh
            frame.on('select', function() {
                var attachment = frame.state().get('selection').first().toJSON();
                
                // Cập nhật ảnh đại diện
                wp.ajax.post('set-post-thumbnail', {
                    post_id: post_id,
                    thumbnail_id: attachment.id,
                    _ajax_nonce: wpPostThumbnailL10n.setThumbnail
                }).done(function() {
                    // Cập nhật hiển thị không cần tải lại trang
                    $('.column-featured_image .vts-image-preview', '#post-' + post_id)
                        .css('background-image', 'url(' + attachment.url + ')')
                        .attr('data-id', attachment.id)
                        .removeClass('vts-no-image');
                });
            });
            
            frame.open();
        });
 
        // Xử lý nút tải ảnh trong Quick Edit
        $('body').on('click', '.vts_upload_featured_image', function(e) {
            e.preventDefault();
            var button = $(this);
            var preview = button.parent().find('.vts-image-preview-quickedit');
            var input = button.siblings('input[name="_thumbnail_id"]');
            
            var custom_uploader = wp.media({
                title: 'Ảnh đại diện',
                library: {type: 'image'},
                multiple: false,
                button: {text: 'Đặt ảnh đại diện'}
            }).on('select', function() {
                var attachment = custom_uploader.state().get('selection').first().toJSON();
                
                // Cập nhật hiển thị và input
                preview.css({
                    'background-image': 'url(' + attachment.url + ')',
                    'display': 'block'
                });
                
                input.val(attachment.id);
                button.siblings('.vts_remove_featured_image').show();
            }).open();
        });
 
        // Xử lý nút xóa ảnh
        $('body').on('click', '.vts_remove_featured_image', function(e) {
            e.preventDefault();
            $(this).hide()
                  .siblings('input[name="_thumbnail_id"]').val('-1')
                  .siblings('.vts-image-preview-quickedit').css('display', 'none');
            return false;
        });
 
        // Ghi đè chức năng Edit để tải dữ liệu ảnh
        var $wp_inline_edit = inlineEditPost.edit;
        inlineEditPost.edit = function(id) {
            $wp_inline_edit.apply(this, arguments);
            var $post_id = 0;
            if (typeof(id) == 'object') { 
                $post_id = parseInt(this.getId(id));
            }
 
            if ($post_id > 0) {
                var $edit_row = $('#edit-' + $post_id),
                    $post_row = $('#post-' + $post_id),
                    $featured_image = $('.column-featured_image .vts-image-preview', $post_row),
                    $featured_image_id = $featured_image.attr('data-id'),
                    $featured_image_style = $featured_image.attr('style');
 
                if ($featured_image_id != -1) {
                    // Cập nhật ID và hiển thị ảnh trong Quick Edit
                    $(':input[name="_thumbnail_id"]', $edit_row).val($featured_image_id);
                    $('.vts-image-preview-quickedit', $edit_row).attr('style', $featured_image_style).show();
                    $('.vts_remove_featured_image', $edit_row).show();
                }
            }
        }
    });
    </script>
<?php
}






