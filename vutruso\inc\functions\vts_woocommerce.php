<?php

// Remove the product description heading in WooCommerce
add_filter('woocommerce_product_description_heading', '__return_false');


/* woocommerce_max_quantity
*===============================================================*/
function woocommerce_max_quantity( $args, $product ) {
    $args['max_value'] = 1;
    return $args;
}
add_filter( 'woocommerce_quantity_input_args', 'woocommerce_max_quantity', 10, 2 );



    // Disable WooCommerce Blocks
    add_action('plugins_loaded', function() {
		// Remove WooCommerce Blocks
		if (class_exists('Automattic\WooCommerce\Packages')) {
		remove_action('init', 'wc_blocks_register_assets');
		remove_action('enqueue_block_assets', 'wc_blocks_enqueue_assets');
	
		// Disable loading of WooCommerce Blocks
		if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
		add_filter('woocommerce_blocks_enabled', '__return_false');
		}
		}
		}, 5);
	
		// Remove WooCommerce Blocks package
		use Automattic\WooCommerce\Packages;
		add_action('plugins_loaded', function() {
		if (!class_exists(Packages::class)) {
		return;
		}
		// Keep dependencies for Woo admin screens
		if (!empty($_GET['page']) && is_admin() && str_starts_with($_GET['page'], 'wc-')) {
		return;
		}
		$override = new class() extends Packages {
		public function __construct() {
		}
		public function remove_woocommerce_blocks(): void {
		unset(static::$packages['woocommerce-blocks']);
		}
		};
		$override->remove_woocommerce_blocks();
		}, 6);
	
		// Remove block widgets registration
		add_action('after_setup_theme', function() {
		remove_theme_support('widgets-block-editor');
		});
	
	
		/**
		* Disable WooCommerce block styles (front-end).
		*/
		add_action( 'wp_enqueue_scripts', 'themesharbor_disable_woocommerce_block_styles' );
		function themesharbor_disable_woocommerce_block_styles() {
		wp_dequeue_style( 'wc-blocks-style' );
		}
	
	
		/**
		* Disable WooCommerce block styles (back-end).
		*/
		add_action( 'enqueue_block_assets', 'themesharbor_disable_woocommerce_block_editor_styles', 1, 1 );
		function themesharbor_disable_woocommerce_block_editor_styles() {
		wp_deregister_style( 'wc-block-editor' );
		wp_deregister_style( 'wc-blocks-style' );
		}



    /* Remove the generated product schema markup from Product Category and Shop pages.
    *===============================================================*/
    function wc_remove_product_schema() {
		remove_action( 'woocommerce_shop_loop', array( WC()->structured_data, 'generate_product_data' ), 10, 0 );
		}
		add_action( 'woocommerce_init', 'wc_remove_product_schema' );
	
	
/* Disable WooCommerce Marketplace
*===============================================================*/
add_filter( 'woocommerce_allow_marketplace_suggestions', '__return_false' );
add_filter('woocommerce_show_marketplace_suggestions', function ($show) { return 'no'; });


/* Disable WooCommerce wp-status + add-on +wp-reports
*===============================================================*/
add_action( 'admin_menu', 'woo_remove_admin_submenus', 999 );
function woo_remove_admin_submenus() {
    remove_submenu_page( 'woocommerce', 'wc-status' );
    remove_submenu_page( 'woocommerce', 'wc-addons');
	remove_submenu_page('woocommerce', 'wc-addons&section=helper');
	remove_submenu_page( 'woocommerce', 'wc-reports');
}

/* Disable WooCommerce dashboard status widget
*===============================================================*/

add_action('wp_dashboard_setup', 'vutruso_disable_woocommerce_status');
function vutruso_disable_woocommerce_status() {
	remove_meta_box('woocommerce_dashboard_status', 'dashboard', 'normal');
	remove_meta_box('woocommerce_dashboard_recent_reviews', 'dashboard', 'normal');
}

/* Suppport woocommerce
*===============================================================*/
add_theme_support( 'woocommerce' );


// Remove woocommerce_breadcrumb
remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20 );


/* Change Mua hàng => Tải về
*===============================================================*/	
add_filter('woocommerce_product_single_add_to_cart_text', 'vts_custom_mua_hang');
function vts_custom_mua_hang() {
return __('Tải về', 'vutruso');
}


/* Change Order Notes Placeholder Text - WooCommerce
*===============================================================*/
add_filter( 'woocommerce_checkout_fields', 'vts_woocommerce_checkout_fields' );
function vts_woocommerce_checkout_fields( $fields ) {
	$fields['order']['order_comments']['placeholder'] = 'Ghi chú về đơn hàng, các vấn đề cần hỗ trợ...';
	return $fields;
}


/* Remove featured image duplicate on gallery single product slider
*===============================================================*/
add_filter('woocommerce_single_product_image_thumbnail_html', 'remove_featured_image', 10, 2);
function remove_featured_image($html, $attachment_id ) {
	global $post, $product;
	$featured_image = get_post_thumbnail_id( $post->ID );
	if ( $attachment_id == $featured_image )
		$html = '';
	return $html;
}

/* Change number of related products output
*===============================================================*/

function change_related_products_count( $args ) {
    $args['posts_per_page'] = 6; // Replace 6 with the desired number of related products to display
    return $args;
}
add_filter( 'woocommerce_output_related_products_args', 'change_related_products_count' );




/* wc_product_gallery Support
*===============================================================*/
// function wc_product_gallery() {
// 	add_theme_support( 'wc-product-gallery-zoom' );
// 	add_theme_support( 'wc-product-gallery-lightbox' );
// 	add_theme_support( 'wc-product-gallery-slider' );
// }
// add_action( 'after_setup_theme', 'wc_product_gallery' );



/* Remove woo styles
*===============================================================*/
add_action( 'wp_enqueue_scripts', 'vts_remove_woo_styles', 20 ); // priority 20 and higher
function vts_remove_woo_styles() {
	wp_deregister_style( 'woocommerce-general' ); // the main CSS file
	wp_deregister_style( 'woocommerce-smallscreen' ); // for max-width: 768px
	wp_deregister_style( 'woocommerce-layout' ); // layout only
}


/* Change Price VTS
*===============================================================*/
add_filter( 'woocommerce_get_price_html', 'vts_price', 100, 2 );
function vts_price( $price, $product ){

	if ( '' == $product->get_price() ) {
		if(!is_product()){
			$price = '<button class="nhan-bao-gia demo">Liên hệ</button>';
		} else{
			$price = '<button id="bao-gia" class="demo">Liên hệ</button>';
		}
	} elseif ( 0 == $product->get_price() ) {
		$price = '<button class="demo">Miễn phí</button>';
	}

return $price;
}


/* Thêm VNĐ cho woo
*===============================================================*/
add_filter('woocommerce_currency_symbol', 'change_existing_currency_symbol', 10, 2);
function change_existing_currency_symbol( $currency_symbol, $currency ) {
	switch( $currency ) {
	case 'VND': $currency_symbol = 'VNĐ'; break;
	}
	return $currency_symbol;
}



/* Them phone vao phan quan tri colum order page
*===============================================================*/
add_filter( 'manage_edit-shop_order_columns', 'shop_order_columns' );
function shop_order_columns( $columns ){
	$new_columns = (is_array($columns)) ? $columns : array();
	$new_columns['phone'] = 'Số điện thoại';
	return $new_columns;
}
add_action( 'manage_shop_order_posts_custom_column', 'shop_order_posts_custom_column' );
function shop_order_posts_custom_column( $column ){
	global $post, $the_order;

	if ( empty( $the_order ) || $the_order->get_id() != $post->ID ) {
		$the_order = wc_get_order( $post->ID );
	}

	$billing_address = $the_order->get_address();
	if ( $column == 'phone' ) {    
		echo ( isset( $billing_address['phone'] ) ? $billing_address['phone'] : '');
	}
}


/* Auto update cart when change quantity
*===============================================================*/
	//add_action( 'wp_footer', 'vts_cart_refresh_update_qty' ); 
	function vts_cart_refresh_update_qty() { 
	if (is_cart()) { 
		?>
<script type="text/javascript">
jQuery('div.woocommerce').on('click', 'input.qty', function() {
    jQuery("[name='update_cart']").trigger("click");
});
</script>
<?php 
} 
}


/* Hide Account display Name
*===============================================================*/
add_filter('woocommerce_save_account_details_required_fields', 'wc_save_account_details_required_fields' );
function wc_save_account_details_required_fields( $required_fields ){
	unset( $required_fields['account_display_name'] );
	return $required_fields;
}



add_filter('woocommerce_default_address_fields', 'override_default_address_checkout_fields', 20, 1);
function override_default_address_checkout_fields( $address_fields ) {
	$address_fields['first_name']['placeholder'] = 'Tên của bạn*';
	$address_fields['last_name']['placeholder'] = 'Họ của bạn *';
	$address_fields['address_1']['placeholder'] = 'Địa chỉ*';
	// $address_fields['state']['placeholder'] = 'Stat';
	// $address_fields['postcode']['placeholder'] = 'Postnummer';
	// $address_fields['city']['placeholder'] = 'By';
	return $address_fields;
}


// Remove zip code
add_filter( 'woocommerce_checkout_fields' , 'vts_remove_billing_postcode_checkout' );
function vts_remove_billing_postcode_checkout( $fields ) {
  unset($fields['billing']['billing_postcode']);
  //unset($fields['billing']['last_name']);
  return $fields;
}


/* Remove company name, postcode, billing_city
*===============================================================*/
add_filter( 'woocommerce_checkout_fields' , 'custom_override_checkout_fields' );
function custom_override_checkout_fields( $fields ) {
	//Change billing input
	$fields['billing']['billing_phone']['placeholder'] = 'Số điện thoại*';
	$fields['billing']['billing_email']['placeholder'] = 'Địa chỉ email*';
	unset($fields['billing']['billing_company']);
	unset($fields['billing']['billing_address_2']);
	unset($fields['billing']['billing_city']);
	unset($fields['billing']['billing_postcode']);
	unset($fields['billing']['billing_country']);
	unset($fields['billing']['billing_state']);


	unset($fields['billing']['billing_address_2']);
	unset($fields['billing']['billing_postcode']);
	unset($fields['billing']['billing_company']);
	unset($fields['shipping']['shipping_country']);
	unset($fields['billing']['billing_city']);
	return $fields;
}




add_filter( 'woocommerce_billing_fields' , 'custom_override_billing_fields' );
function custom_override_billing_fields( $fields ) {
	unset($fields['billing_country']);
	unset($fields['billing_city']);
	unset($fields['billing_postcode']);
	return $fields;
}

add_filter( 'woocommerce_shipping_fields' , 'custom_override_shipping_fields' );
function custom_override_shipping_fields( $fields ) {
	unset($fields['shipping_country']);
	return $fields;
}


/* Sắp xếp lại vị trí các hook trong trang content-single-product.php
*===============================================================*/
add_action( 'woocommerce_before_single_product', 'vts_change_single_product_layout' );
function vts_change_single_product_layout() {
	// Disable the hooks so that their order can be changed.
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_title', 5 );
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_price', 10 );
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20 );
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30 );
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40 );
	remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_sharing', 50 );

	// Put the price first.
	add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_price', 5 );

	// And finally include the 'Add to cart' section.
	add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 10 );

	// Move the title to near the end.
	//add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_title', 20 );

	// Include the category/tags info.
	add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 60 );
	// Then the product short description.
	add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 40 );


}





/* Display the custom text field
*===============================================================*/
function vts_custom_field_demo() {
	$args = array(
	'id' => 'custom_text_field_demo',
	'label' => __( 'Link demo', 'vutruso' ),
	'class' => 'vts-custom-field_demo',
	'desc_tip' => true,
	'description' => __( 'Nhập link demo dự án', 'vutruso' ),
	);
	woocommerce_wp_text_input( $args );
}
add_action( 'woocommerce_product_options_general_product_data', 'vts_custom_field_demo' );


/**
 * Save the custom field
 */
function vts_save_custom_field( $post_id ) {
	$product = wc_get_product( $post_id );
	$title = isset( $_POST['custom_text_field_demo'] ) ? $_POST['custom_text_field_demo'] : '';
	$product->update_meta_data( 'custom_text_field_demo', sanitize_text_field( $title ) );
	$product->save();
}
add_action( 'woocommerce_process_product_meta', 'vts_save_custom_field' );
//display front end: <?php echo get_post_meta($post->ID, 'custom_text_field_demo', true);



/* Change "Place Order" Button text @ WooCommerce Checkout
*===============================================================*/
add_filter( 'woocommerce_order_button_text', 'vts_custom_button_text' );
function vts_custom_button_text( $button_text ) {
	return 'Gửi thông tin'; // new text is here 
}



/* Remove quantity column in cart page
*===============================================================*/
// function vts_remove_quantity_column_from_cart( $return, $product ) {
//     if ( is_cart() ) return true;
// }
// add_filter( 'woocommerce_is_sold_individually', 'vts_remove_quantity_column_from_cart', 10, 2 );



/* Subtotal Price 0đ -> Free text
*===============================================================*/
function filter_cart_item_subtotal( $subtotal, $cart_item, $cart_item_key ) {
	if ( $cart_item[ 'data' ]->price == 0 ) {
		$subtotal = __( 'Miễn phí', 'vutruso' );
	}
	return $subtotal;
}
add_filter( 'woocommerce_cart_item_subtotal', 'filter_cart_item_subtotal', 10, 3 );



/* Free page cart price = 0đ
*===============================================================*/
add_filter( 'woocommerce_cart_item_price', 'filter_cart_item_price', 10, 3 );
function filter_cart_item_price( $price, $cart_item, $cart_item_key ) {
	if ( $cart_item[ 'data' ]->price == 0 ) {
		$price = __( 'Miễn phí', 'vutruso' );
	}
	return $price;
}


/* Source
*===============================================================*/
function vts_woo_add_custom_fields() {
	global $woocommerce, $post;
	echo '<div class="options_group">';
	woocommerce_wp_textarea_input(
		array(
			'id'          => '_textarea',
			'label'       => __( 'Source code', 'vutruso' ),
			'placeholder' => '',
			'desc_tip'    => true,
			'description' => __( "Nhập thông tin liên quan tới source code", "vutruso" )
		)
	);
	echo '</div>';
}
add_action( 'woocommerce_product_options_general_product_data', 'vts_woo_add_custom_fields' );  

/* Save our simple product fields */
function vts_woo_add_custom_fields_save( $post_id ){
	// Textarea
	$woocommerce_textarea = $_POST['_textarea'];
	update_post_meta( $post_id, '_textarea', esc_html( $woocommerce_textarea ) );
}
add_action( 'woocommerce_process_product_meta', 'vts_woo_add_custom_fields_save' );
/* End source
*===============================================================*/


/* Add filtering by featured products
*===============================================================*/
// add_action('restrict_manage_posts', 'featured_products_sorting');
// function featured_products_sorting() {
//     global $typenow;
//     $post_type = 'product'; // You can change this if it is for other type of content
//     $taxonomy  = 'product_visibility'; // Change to your taxonomy
//     if ($typenow == $post_type) {
//         $selected      = isset($_GET[$taxonomy]) ? $_GET[$taxonomy] : '';
//         $info_taxonomy = get_taxonomy($taxonomy);
//         wp_dropdown_categories(array(
//             'show_option_all' => __("Show all {$info_taxonomy->label}"),
//             'taxonomy'        => $taxonomy,
//             'name'            => $taxonomy,
//             'orderby'         => 'name',
//             'selected'        => $selected,
//             'show_count'      => true,
//             'hide_empty'      => true,
//         ));
//     };
// }


// add_filter('parse_query', 'featured_products_sorting_query');
// function featured_products_sorting_query($query) {
//     global $pagenow;
//     $post_type = 'product'; // You can change this if it is for other type of content
//     $taxonomy  = 'product_visibility'; // Change to your taxonomy
//     $q_vars    = &$query->query_vars;
//     if ( $pagenow == 'edit.php' && isset($q_vars['post_type']) && $q_vars['post_type'] == $post_type && isset($q_vars[$taxonomy]) && is_numeric($q_vars[$taxonomy]) && $q_vars[$taxonomy] != 0 ) {
//         $term = get_term_by('id', $q_vars[$taxonomy], $taxonomy);
//         $q_vars[$taxonomy] = $term->slug;
//     }
// }


// Remove order comment note
add_filter( 'woocommerce_enable_order_notes_field', '__return_false', 9999 );


/**
 * Remove password strength check.
 */
function vts_remove_password_strength() {
    wp_dequeue_script( 'wc-password-strength-meter' );
}
add_action( 'wp_print_scripts', 'vts_remove_password_strength', 10 );


/* Remove confirm logout woocommerce
*===============================================================*/
add_action('template_redirect', 'disable_wc_logout_confirmation');
function disable_wc_logout_confirmation(){
	global $wp;
    if(isset($wp->query_vars['customer-logout'])){
        wp_redirect(str_replace('&amp;','&',wp_logout_url( wc_get_page_permalink('myaccount'))));
        exit;
    }
}



/* Add first name + last name to register woo
*===============================================================*/
add_action( 'woocommerce_register_form_start', 'vutruso_form_registration_fields', 25 );
function vutruso_form_registration_fields() {
	// first name
	$billing_first_name = ! empty( $_POST[ 'billing_first_name' ] ) ? $_POST[ 'billing_first_name' ] : '';
	echo '
	<div class="form-group">
		<input autocomplete="off" type="text" class="form-control" value="' . esc_attr( $billing_first_name ) . '" name="billing_first_name" placeholder="Họ của bạn *">
	</div>
		';
	
	// last name
	$billing_last_name = ! empty( $_POST[ 'billing_last_name' ] ) ? $_POST[ 'billing_last_name' ] : '';
	echo '
	<div class="form-group">
		<input autocomplete="off" type="text" class="form-control" name="billing_last_name" value="' . esc_attr( $billing_last_name ) . '" placeholder="Tên của bạn *">
	</div>
	';

	// Phone
	// $billing_phone = ! empty( $_POST[ 'billing_phone' ] ) ? $_POST[ 'billing_phone' ] : '';
	// echo '<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
    //     <label for="username">Phone&nbsp;<span class="required">*</span></label>
	// 	<input type="text" class="form-control"  name="billing_phone" id="kind_of_l_name" value="' . esc_attr( $billing_phone ) . '" />
	// </p>';

}







// add input password - register page woo
function wc_register_form_password_repeat() { ?>

<div class="form-group">
    <input autocomplete="off" type="password" class="form-control" name="password2" id="reg_password2"
            value="<?php if ( ! empty( $_POST['password2'] ) ) echo esc_attr( $_POST['password2'] ); ?>"
            placeholder="Nhập lại mật khẩu *" >
</div>

<?php
}
add_action( 'woocommerce_register_form', 'wc_register_form_password_repeat' );





add_filter( 'woocommerce_registration_errors', 'wooc_validate_extra_register_fields', 10, 3 );

function wooc_validate_extra_register_fields( $errors, $username, $email ) {

	global $woocommerce;

	extract( $_POST );

	if ( strcmp( $password, $password2 ) !== 0 ) {
        $errors->add( 'repassword-error', __( '<strong>Error</strong>: Mật khẩu không khớp!', 'woocommerce' ) );
	}

    if ( isset( $_POST['billing_first_name'] ) && empty( $_POST['billing_first_name'] ) ) {
        $errors->add( 'billing_first_name_error', __( '<strong>Error</strong>: Họ và tên là bắt buộc!', 'woocommerce' ) );
    }

    if ( isset( $_POST['billing_last_name'] ) && empty( $_POST['billing_last_name'] ) ) {
        $errors->add( 'billing_last_name_error', __( '<strong>Error</strong>: Họ và tên là bắt buộc!.', 'woocommerce' ) );
    }

    // if ( isset( $_POST['billing_phone'] ) && empty( $_POST['billing_phone'] ) ) {
    //     $errors->add( 'billing_phone_error', __( '<strong>Error</strong>: Phone is required!.', 'woocommerce' ) );
    // }


    return $errors;
}

// Check out page
add_action( 'woocommerce_after_checkout_validation', 'misha_one_err', 9999, 2);
function misha_one_err( $fields, $errors ){
	// if any validation errors
	if( !empty( $errors->get_error_codes() ) ) {
		// remove all of them
		foreach( $errors->get_error_codes() as $code ) {
			$errors->remove( $code );
		}
		// add our custom one
		$errors->add( 'validation', 'Please fill in all required fields to place order!' );
		
	} else if ( empty( $_POST['billing_first_name'] ) ) {
		$errors->add( 'billing_first_name_error', __( 'First Name is a required field' ) );
	} else if ( empty( $_POST['billing_last_name'] ) ) {
		$errors->add( 'billing_last_name_error', __( 'Last Name is a required field' ) );
	}
}


// save to meta
add_action( 'woocommerce_created_customer', 'vutruso_save_fields', 25 );
function vutruso_save_fields( $user_id ) {
	// save first name
	if ( isset( $_POST[ 'billing_first_name' ] ) ) {
		update_user_meta( $user_id, 'first_name', sanitize_text_field( $_POST['billing_first_name'] ) );
		update_user_meta( $user_id, 'billing_first_name', sanitize_text_field( $_POST['billing_first_name'] ) );
	}
	// save last name
	if ( isset( $_POST[ 'billing_last_name' ] ) ) {
		update_user_meta( $user_id, 'last_name', sanitize_text_field( $_POST['billing_last_name'] ) );
		update_user_meta( $user_id, 'billing_last_name', sanitize_text_field( $_POST['billing_last_name'] ) );
	}

	// save last name
	// if ( isset( $_POST[ 'billing_phone' ] ) ) {
	// 	//update_user_meta( $user_id, 'billing_phone', sanitize_text_field( $_POST['billing_phone'] ) );
	// 	update_user_meta( $user_id, 'billing_phone', sanitize_text_field( $_POST['billing_phone'] ) );
	// }
 
}




// Tu dong lay lastname + firstname dat cho ten woocommerce-MyAccount-content
add_action( 'woocommerce_created_customer', 'bbloomer_save_name_fields' );
function bbloomer_save_name_fields( $customer_id ) {
    if ( isset( $_POST['billing_first_name'] ) ) {
        update_user_meta( $customer_id, 'billing_first_name', sanitize_text_field( $_POST['billing_first_name'] ) );
        update_user_meta( $customer_id, 'first_name', sanitize_text_field($_POST['billing_first_name']) );
    }
    if ( isset( $_POST['billing_last_name'] ) ) {
        update_user_meta( $customer_id, 'billing_last_name', sanitize_text_field( $_POST['billing_last_name'] ) );
        update_user_meta( $customer_id, 'last_name', sanitize_text_field($_POST['billing_last_name']) );
    }
    
    if ( isset( $_POST['billing_first_name'] ) && isset( $_POST['billing_last_name'] ) ) {
    
        if ( !empty( $_POST['billing_first_name'] ) && !empty( $_POST['billing_last_name'] ) ) {
    
            $display_name = sanitize_text_field( $_POST['billing_first_name'] ) . ' ' . sanitize_text_field( $_POST['billing_last_name'] );
            wp_update_user( array( 'ID' => $customer_id, 'display_name' => $display_name ) );
        }
    
    }

}



add_filter( 'woocommerce_product_tabs', 'vts_change_description_tab_name' );

function vts_change_description_tab_name( $tabs ) {
    $tabs['description']['title'] = __( 'Thông tin chi tiết', 'vutruso' );
    return $tabs;
}