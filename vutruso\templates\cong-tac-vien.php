<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cộng tác viên</title>
</head>
<body>
    <?php
/*
 * Template Name: Cộng tác viên - Premium (Responsive Improved)
 */
?>


<!-- Performance Optimizations -->
<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
<meta name="theme-color" content="#0d47a1">

<!-- Preload Critical Resources -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preconnect" href="https://cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="https://i.pravatar.cc">

<!-- Optimized Font Loading -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" media="print" onload="this.media='all'">
<noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"></noscript>

<!-- Hero Banner Section -->
<section class="hero-banner">
    <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
    </div>
    
    <div class="hero-content">
        <div class="container">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="title-highlight">Chương Trình Đối Tác</span>
                    <span class="title-main">tại Vũ Trụ Số</span>
                </h1>
                <p class="hero-subtitle">
                    Cơ hội hợp tác kinh doanh đầy hứa hẹn - Cùng nhau phát triển và thành công
                </p>
                
                <div class="hero-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Đối Tác</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percent"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">30%</div>
                            <div class="stat-label">Chiết Khấu</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Hỗ Trợ</div>
                        </div>
                    </div>
                </div>
                
                <div class="hero-cta">
                    <a href="#pricing" class="btn-hero-primary">
                        <span>Trở Thành Đối Tác</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="#features" class="btn-hero-secondary">
                        <i class="fas fa-play"></i>
                        <span>Tìm Hiểu Thêm</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="features-section">
    <div class="container">
        <div class="section-header">
            <div class="section-badge">
                <i class="fas fa-star"></i>
                <span>Ưu Điểm Nổi Bật</span>
            </div>
            <h2 class="section-title">Tại sao chọn trở thành đối tác của chúng tôi?</h2>
            <p class="section-subtitle">Khám phá những lợi ích độc quyền dành riêng cho đối tác của Vũ Trụ Số</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                <div class="feature-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <h3 class="feature-title">Chiết Khấu Hấp Dẫn</h3>
                <p class="feature-description">
                    Với mức chiết khấu lên tới 30%, bạn sẽ có cơ hội thu nhập cao từ mỗi giao dịch thành công.
                </p>
                <div class="feature-highlight">Lên đến 30%</div>
            </div>
            
            <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                <div class="feature-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <h3 class="feature-title">Tự Do Định Giá</h3>
                <p class="feature-description">
                    Hoàn toàn chủ động trong việc cấu hình giá bán các dịch vụ theo chiến lược kinh doanh của riêng bạn.
                </p>
                <div class="feature-highlight">100% Linh hoạt</div>
            </div>
            
            <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                <div class="feature-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3 class="feature-title">Công Cụ Chuyên Nghiệp</h3>
                <p class="feature-description">
                    Nhận website giới thiệu miễn phí và các công cụ marketing chuyên nghiệp để tăng hiệu quả bán hàng.
                </p>
                <div class="feature-highlight">Miễn Phí</div>
            </div>
            
            <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                <div class="feature-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <h3 class="feature-title">Hỗ Trợ Marketing</h3>
                <p class="feature-description">
                    Đội ngũ chuyên gia marketing sẽ hỗ trợ bạn xây dựng chiến lược quảng cáo hiệu quả và thu hút khách hàng.
                </p>
                <div class="feature-highlight">Chuyên Gia</div>
            </div>
            
            <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
                <div class="feature-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h3 class="feature-title">Đào Tạo Toàn Diện</h3>
                <p class="feature-description">
                    Chương trình đào tạo từ cơ bản đến nâng cao, giúp bạn nắm vững kiến thức và kỹ năng bán hàng.
                </p>
                <div class="feature-highlight">Miễn Phí</div>
            </div>
            
            <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3 class="feature-title">Hỗ Trợ 24/7</h3>
                <p class="feature-description">
                    Đội ngũ hỗ trợ kỹ thuật chuyên nghiệp luôn sẵn sàng giải đáp mọi thắc mắc bất kể thời gian nào.
                </p>
                <div class="feature-highlight">24/7</div>
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="process-section">
    <div class="container">
        <div class="section-header">
            <div class="section-badge">
                <i class="fas fa-route"></i>
                <span>Quy Trình</span>
            </div>
            <h2 class="section-title">4 Bước Đơn Giản Để Bắt Đầu</h2>
            <p class="section-subtitle">Trở thành đối tác của chúng tôi chỉ trong vài phút</p>
        </div>
        
        <div class="process-timeline">
            <div class="process-step" data-aos="fade-right" data-aos-delay="100">
                <div class="step-number">01</div>
                <div class="step-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="step-content">
                    <h3>Đăng Ký Tài Khoản</h3>
                    <p>Điền form đăng ký với thông tin cơ bản. Quá trình xét duyệt chỉ mất 24h.</p>
                </div>
                <div class="step-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
            </div>
            
            <div class="process-step" data-aos="fade-left" data-aos-delay="200">
                <div class="step-number">02</div>
                <div class="step-icon">
                    <i class="fas fa-file-signature"></i>
                </div>
                <div class="step-content">
                    <h3>Ký Hợp Đồng</h3>
                    <p>Ký hợp đồng hợp tác điện tử và nhận thông tin truy cập hệ thống quản lý.</p>
                </div>
                <div class="step-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
            </div>
            
            <div class="process-step" data-aos="fade-right" data-aos-delay="300">
                <div class="step-number">03</div>
                <div class="step-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="step-content">
                    <h3>Đào Tạo Miễn Phí</h3>
                    <p>Tham gia khóa đào tạo online về sản phẩm, quy trình bán hàng và kỹ năng chăm sóc khách hàng.</p>
                </div>
                <div class="step-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
            </div>
            
            <div class="process-step" data-aos="fade-left" data-aos-delay="400">
                <div class="step-number">04</div>
                <div class="step-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="step-content">
                    <h3>Bắt Đầu Kinh Doanh</h3>
                    <p>Bắt đầu bán hàng và nhận hoa hồng ngay lập tức. Thanh toán hàng tháng đúng hẹn.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="pricing-section">
    <div class="container">
        <div class="section-header">
            <div class="section-badge">
                <i class="fas fa-tags"></i>
                <span>Gói Hợp Tác</span>
            </div>
            <h2 class="section-title">Chọn Gói Hợp Tác Phù Hợp</h2>
            <p class="section-subtitle">Linh hoạt lựa chọn theo nhu cầu và khả năng của bạn</p>
        </div>
        
        <div class="pricing-grid">
            <!-- Partner Package -->
            <div class="pricing-card" data-aos="zoom-in" data-aos-delay="100">
                <div class="pricing-header">
                    <div class="pricing-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="pricing-title">PARTNER</h3>
                    <p class="pricing-subtitle">Cộng tác viên</p>
                    <div class="pricing-price">
                        <span class="price-currency">Miễn Phí</span>
                    </div>
                </div>
                
                <div class="pricing-features">
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Làm dự án theo yêu cầu với giá cạnh tranh</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Gói cài đặt web chỉ từ <strong>500k</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Giá mua giao diện giảm <strong>50%</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Chứng chỉ SSL <strong>200k/năm</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Hosting chỉ từ <strong>500k/năm</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Chiết khấu marketing <strong>30%</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Miễn phí tối ưu tốc độ web</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Miễn phí bảo mật WordPress</span>
                    </div>
                </div>
                
                <div class="pricing-action">
                    <a href="#contact" class="btn-pricing">
                        <span>Liên Hệ Hợp Tác</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
            
            <!-- Business Package -->
            <div class="pricing-card business" data-aos="zoom-in" data-aos-delay="150">
                <div class="popular-badge">
                    <i class="fas fa-star"></i>
                    <span>Phổ Biến Nhất</span>
                </div>
                <div class="pricing-header">
                    <div class="pricing-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="pricing-title">BUSINESS</h3>
                    <p class="pricing-subtitle">Đại lý cấp độ trung</p>
                    <div class="pricing-price">
                        <span class="price-amount">5 triệu</span>
                        <span class="price-period">VNĐ/năm</span>
                    </div>
                </div>
                
                <div class="pricing-features">
                    <div class="feature-item highlighted">
                        <i class="fas fa-star"></i>
                        <span><strong>Tất cả quyền lợi gói Partner</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Chiết khấu cao hơn lên đến <strong>60%</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Website bán hàng cơ bản miễn phí</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Hỗ trợ marketing cơ bản</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Đào tạo online chuyên nghiệp</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Hỗ trợ kỹ thuật ưu tiên</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Tài liệu marketing chuyên nghiệp</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Báo cáo doanh thu chi tiết</span>
                    </div>
                </div>
                
                <div class="pricing-action">
                    <a href="#contact" class="btn-pricing business">
                        <span>Nâng Cấp Ngay</span>
                        <i class="fas fa-arrow-up"></i>
                    </a>
                </div>
            </div>
            
            <!-- Premium Package -->
            <div class="pricing-card premium" data-aos="zoom-in" data-aos-delay="200">
                <div class="pricing-header">
                    <div class="pricing-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="pricing-title">PREMIUM</h3>
                    <p class="pricing-subtitle">Đại lý chính thức</p>
                    <div class="pricing-price">
                        <span class="price-amount">10 triệu</span>
                        <span class="price-period">VNĐ/năm</span>
                    </div>
                </div>
                
                <div class="pricing-features">
                    <div class="feature-item highlighted">
                        <i class="fas fa-star"></i>
                        <span><strong>Tất cả quyền lợi gói Partner</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Chiết khấu cao hơn lên đến <strong>30%</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Tặng website bán hàng chuyên nghiệp</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Hỗ trợ marketing miễn phí</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Đào tạo chuyên sâu kỹ năng bán hàng</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Ưu tiên hỗ trợ kỹ thuật VIP</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Tham gia sự kiện độc quyền</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Giấy chứng nhận đại lý chính thức</span>
                    </div>
                </div>
                
                <div class="pricing-action">
                    <a href="#contact" class="btn-pricing premium">
                        <span>Bắt Đầu Ngay</span>
                        <i class="fas fa-crown"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Additional Benefits -->
        <div class="pricing-benefits">
            <div class="benefit-item">
                <i class="fas fa-shield-alt"></i>
                <span>Cam kết hỗ trợ nhanh chóng và tận tình</span>
            </div>
            <div class="benefit-item">
                <i class="fas fa-tools"></i>
                <span>Hỗ trợ fix bug website lâu dài miễn phí</span>
            </div>
            <div class="benefit-item">
                <i class="fas fa-gift"></i>
                <span>Tặng nhiều plugin và theme bản quyền</span>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section">
    <div class="container">
        <div class="section-header">
            <div class="section-badge">
                <i class="fas fa-quote-left"></i>
                <span>Đánh Giá</span>
            </div>
            <h2 class="section-title">Đối Tác Nói Gì Về Chúng Tôi</h2>
            <p class="section-subtitle">Những chia sẻ chân thật từ các đối tác thành công</p>
        </div>
        
        <div class="testimonials-grid">
            <div class="testimonial-card" data-aos="fade-up" data-aos-delay="100">
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="testimonial-content">
                    "Hợp tác với Vũ Trụ Số đã thay đổi hoàn toàn cuộc sống của tôi. Thu nhập ổn định, hỗ trợ tuyệt vời và cơ hội phát triển không giới hạn."
                </p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="https://i.pravatar.cc/60?img=1" alt="Nguyễn Văn An">
                    </div>
                    <div class="author-info">
                        <h4>Nguyễn Văn An</h4>
                        <p>Đại lý Premium • Hà Nội</p>
                        <div class="author-stats">
                            <span class="stat">2 năm hợp tác</span>
                            <span class="stat">100+ dự án</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="testimonial-card" data-aos="fade-up" data-aos-delay="200">
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="testimonial-content">
                    "Hệ thống quản lý rất dễ sử dụng, khách hàng luôn hài lòng. Tôi đã giới thiệu nhiều bạn bè tham gia và họ đều thành công."
                </p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="https://i.pravatar.cc/60?img=2" alt="Trần Thị Bình">
                    </div>
                    <div class="author-info">
                        <h4>Trần Thị Bình</h4>
                        <p>Cộng tác viên • TP.HCM</p>
                        <div class="author-stats">
                            <span class="stat">1.5 năm hợp tác</span>
                            <span class="stat">80+ dự án</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="testimonial-card" data-aos="fade-up" data-aos-delay="300">
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="testimonial-content">
                    "Đội ngũ hỗ trợ luôn sẵn sàng 24/7. Công cụ marketing được cung cấp giúp tôi tiếp cận khách hàng dễ dàng và hiệu quả hơn rất nhiều."
                </p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="https://i.pravatar.cc/60?img=3" alt="Lê Minh Châu">
                    </div>
                    <div class="author-info">
                        <h4>Lê Minh Châu</h4>
                        <p>Đại lý Premium • Đà Nẵng</p>
                        <div class="author-stats">
                            <span class="stat">3 năm hợp tác</span>
                            <span class="stat">150+ dự án</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section">
    <div class="container">
        <div class="section-header">
            <div class="section-badge">
                <i class="fas fa-question-circle"></i>
                <span>FAQ</span>
            </div>
            <h2 class="section-title">Câu Hỏi Thường Gặp</h2>
            <p class="section-subtitle">Giải đáp mọi thắc mắc về chương trình đối tác</p>
        </div>
        
        <div class="faq-container">
            <div class="faq-item">
                <div class="faq-question">
                    <span>Làm thế nào để trở thành đối tác của Vũ Trụ Số?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Quá trình đăng ký rất đơn giản: điền form đăng ký online → xét duyệt trong 24h → ký hợp đồng điện tử → nhận tài khoản quản lý → bắt đầu kinh doanh. Hoàn toàn miễn phí và không ràng buộc.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">
                    <span>Mức chiết khấu cụ thể và cách tính hoa hồng?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Chiết khấu từ 30%-30% tùy gói hợp tác. Hoa hồng được tính theo doanh số thực tế, thanh toán đầu tháng tiếp theo. Hệ thống báo cáo chi tiết, minh bạch 100%.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">
                    <span>Tôi có cần kinh nghiệm về website để tham gia?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Hoàn toàn không cần. Chúng tôi có chương trình đào tạo từ A-Z: kiến thức về website, kỹ năng bán hàng, chăm sóc khách hàng. Bạn chỉ cần có tinh thần học hỏi và nhiệt tình.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">
                    <span>Có hỗ trợ marketing và tìm kiếm khách hàng không?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Có! Chúng tôi cung cấp: website giới thiệu miễn phí, tài liệu marketing chuyên nghiệp, hỗ trợ quảng cáo online, chia sẻ kinh nghiệm tìm khách hàng và chiến lược bán hàng hiệu quả.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">
                    <span>Thời gian hợp đồng và điều kiện chấm dứt?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Hợp đồng linh hoạt, có thể chấm dứt bất kỳ lúc nào với thông báo trước 30 ngày. Không có ràng buộc hay phạt hợp đồng. Quyền lợi đối tác được bảo vệ tối đa.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section id="contact" class="cta-section">
    <div class="cta-background">
        <div class="cta-pattern"></div>
    </div>
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">Sẵn Sàng Bắt Đầu Hành Trình Kinh Doanh?</h2>
            <p class="cta-subtitle">
                Hãy tham gia cùng hơn 500 đối tác đang thành công với Vũ Trụ Số. 
                Cơ hội tốt nhất đang chờ bạn!
            </p>
            <div class="cta-buttons">
                <a href="https://vutruso.com/lien-he/" class="btn-cta-primary">
                    <i class="fas fa-rocket"></i>
                    <span>Đăng Ký Ngay</span>
                </a>
                <a href="tel:0123456789" class="btn-cta-secondary">
                    <i class="fas fa-phone"></i>
                    <span>Zalo Tư Vấn: 0868017791</span>
                </a>
            </div>
        </div>
    </div>
</section>

<style>
/* Import Font & Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Banner */
.hero-banner {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;    width: 100vw;
    padding: 0;
    margin: 0;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        #0d47a1 0%, 
        #174ba3 25%, 
        #1e4fa5 50%, 
        #2b57a8 75%, 
        #0d6bb2 100%);
    background-image: linear-gradient(to right top, #0d47a1, #174ba3, #1e4fa5, #2553a6, #2b57a8, #1d61ae, #0d6bb2, #0075b6, #0086b8, #0095b5, #1ca2af, #4eaea8);
    background-size: 300% 300%;
    animation: gradientShift 8s ease infinite;    
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 60px 60px;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-element {
    position: absolute;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 70%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: 120px 0;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.title-highlight {
    display: block;
    color: rgba(255,255,255,0.9);
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.title-main {
    display: block;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-5px);
}

.stat-icon {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: #FFD700;
    flex-shrink: 0;
}

.stat-content {
    text-align: left;
}

.stat-number {
    font-size: clamp(1.5rem, 3vw, 1.8rem);
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: clamp(0.8rem, 2vw, 0.9rem);
    opacity: 0.8;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.btn-hero-primary, .btn-hero-secondary {
    padding: clamp(0.8rem, 2vw, 1rem) clamp(1.5rem, 3vw, 2rem);
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    min-width: 200px;
    justify-content: center;
}

.btn-hero-primary {
    background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.btn-hero-secondary {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-hero-secondary:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-3px);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: clamp(3rem, 5vw, 4rem);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    font-size: clamp(0.8rem, 2vw, 0.9rem);
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-title {
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2D3748;
}

.section-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.2rem);
    color: #718096;
    max-width: 800px;
    margin: 0 auto;
}

/* Features Section */
.features-section {
    padding: clamp(60px, 8vw, 100px) 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 320px), 1fr));
    gap: clamp(1.5rem, 3vw, 2rem);
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: clamp(2rem, 4vw, 2.5rem);
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    border: 1px solid #E2E8F0;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: clamp(60px, 8vw, 70px);
    height: clamp(60px, 8vw, 70px);
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: clamp(1.3rem, 3vw, 1.5rem);
    margin-bottom: 1.5rem;
}

.feature-title {
    font-size: clamp(1.2rem, 3vw, 1.4rem);
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2D3748;
}

.feature-description {
    color: #718096;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: clamp(0.95rem, 2vw, 1rem);
}

.feature-highlight {
    display: inline-block;
    background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
    color: white;
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-size: clamp(0.8rem, 2vw, 0.9rem);
    font-weight: 600;
}

/* Process Section */
.process-section {
    padding: clamp(80px, 10vw, 120px) 0 clamp(60px, 8vw, 100px);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow: visible;
}

.process-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 280px), 1fr));
    gap: clamp(1.5rem, 3vw, 2rem);
    margin-top: 3rem;
    overflow: visible;
}

.process-step {
    background: white;
    border-radius: 20px;
    padding: clamp(2.5rem, 4vw, 3rem) clamp(1.5rem, 3vw, 2rem) clamp(1.5rem, 3vw, 2rem);
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    position: relative;
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.step-number {
    position: absolute;
    top: -20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: clamp(0.9rem, 2vw, 1rem);
    z-index: 2;
}

.step-icon {
    width: clamp(50px, 6vw, 60px);
    height: clamp(50px, 6vw, 60px);
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    margin-bottom: 1.5rem;
}

.step-content h3 {
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2D3748;
}

.step-content p {
    color: #718096;
    line-height: 1.6;
    font-size: clamp(0.95rem, 2vw, 1rem);
}

.step-arrow {
    position: absolute;
    top: 50%;
    right: -23px;
    transform: translateY(-50%);
    color: #CBD5E0;
    font-size: 1.2rem;
}

/* Pricing Section */
.pricing-section {
    padding: clamp(60px, 8vw, 100px) 0;
    background: white;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 350px), 1fr));
    gap: clamp(1.5rem, 3vw, 2rem);
    margin-bottom: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 80px rgba(0,0,0,0.2);
}

.pricing-card.business {
    border: 2px solid #28a745;
    transform: scale(1.02);
    box-shadow: 0 15px 30px rgba(40, 167, 69, 0.15);
}

.pricing-card.premium {
    border: 2px solid #FFD700;
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: clamp(0.7rem, 2vw, 0.8rem);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    z-index: 1;
}

.pricing-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: clamp(2rem, 4vw, 3rem) clamp(1.5rem, 3vw, 2rem) clamp(1.5rem, 3vw, 2rem);
    text-align: center;
    position: relative;
}

.pricing-card.business .pricing-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.pricing-card.premium .pricing-header {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.pricing-icon {
    width: clamp(60px, 8vw, 80px);
    height: clamp(60px, 8vw, 80px);
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: clamp(1.5rem, 4vw, 2rem);
    margin: 0 auto 1.5rem;
}

.pricing-title {
    font-size: clamp(1.5rem, 4vw, 2rem);
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.pricing-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.pricing-price {
    margin-bottom: 1rem;
}

.price-currency {
    font-size: clamp(1.5rem, 4vw, 2rem);
    font-weight: 700;
}

.price-amount {
    font-size: clamp(2rem, 5vw, 2.5rem);
    font-weight: 800;
}

.price-period {
    font-size: clamp(0.9rem, 2vw, 1rem);
    opacity: 0.8;
}

.pricing-features {
    padding: clamp(1.5rem, 3vw, 2rem);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.feature-item i {
    color: #48BB78;
    font-size: 1rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.feature-item.highlighted i {
    color: #FFD700;
}

.feature-item span {
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.pricing-action {
    padding: 0 clamp(1.5rem, 3vw, 2rem) clamp(1.5rem, 3vw, 2rem);
}

.btn-pricing {
    width: 100%;
    padding: clamp(0.8rem, 2vw, 1rem) clamp(1.5rem, 3vw, 2rem);
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: clamp(1rem, 2.5vw, 1.1rem);
}

.btn-pricing.business {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.btn-pricing.premium {
    background: linear-gradient(45deg, #FFD700, #FFA500);
}

.btn-pricing:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.pricing-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: clamp(1rem, 2vw, 2rem);
    margin-top: 3rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #718096;
    font-size: clamp(0.9rem, 2vw, 0.95rem);
    text-align: center;
}

.benefit-item i {
    color: #667eea;
    flex-shrink: 0;
}

/* Testimonials Section */
.testimonials-section {
    padding: clamp(60px, 8vw, 100px) 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 350px), 1fr));
    gap: clamp(1.5rem, 3vw, 2rem);
}

.testimonial-card {
    background: white;
    border-radius: 20px;
    padding: clamp(2rem, 4vw, 2.5rem);
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.testimonial-rating {
    margin-bottom: 1.5rem;
}

.testimonial-rating i {
    color: #FFD700;
    margin-right: 0.2rem;
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.testimonial-content {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    line-height: 1.7;
    color: #4A5568;
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar img {
    width: clamp(50px, 6vw, 60px);
    height: clamp(50px, 6vw, 60px);
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.author-info h4 {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    font-weight: 700;
    color: #2D3748;
    margin-bottom: 0.3rem;
}

.author-info p {
    color: #718096;
    font-size: clamp(0.8rem, 2vw, 0.9rem);
    margin-bottom: 0.5rem;
}

.author-stats {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.author-stats .stat {
    background: #EDF2F7;
    color: #4A5568;
    padding: 0.2rem 0.8rem;
    border-radius: 15px;
    font-size: clamp(0.7rem, 1.8vw, 0.8rem);
    font-weight: 500;
}

/* FAQ Section */
.faq-section {
    padding: clamp(60px, 8vw, 100px) 0;
    background: white;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 15px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    border: 1px solid #E2E8F0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.faq-question {
    padding: clamp(1rem, 3vw, 1.5rem) clamp(1.5rem, 3vw, 2rem);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: white;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #F7FAFC;
}

.faq-question span {
    font-weight: 600;
    color: #2D3748;
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    flex: 1;
    margin-right: 1rem;
}

.faq-question i {
    color: #CBD5E0;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
    color: #667eea;
}

.faq-answer {
    padding: 0 clamp(1.5rem, 3vw, 2rem) clamp(1rem, 3vw, 1.5rem);
    display: none;
    animation: slideDown 0.3s ease;
}

.faq-item.active .faq-answer {
    display: block;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.faq-answer p {
    color: #718096;
    line-height: 1.7;
    font-size: clamp(0.95rem, 2vw, 1rem);
}

/* CTA Section */
.cta-section {
    padding: clamp(60px, 8vw, 100px) 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    color: white;
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.cta-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 60px 60px;
}

.cta-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.2rem);
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn-cta-primary, .btn-cta-secondary {
    padding: clamp(0.8rem, 2vw, 1rem) clamp(1.5rem, 3vw, 2rem);
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    min-width: 200px;
    justify-content: center;
}

.btn-cta-primary {
    background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.btn-cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
}

.btn-cta-secondary {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-cta-secondary:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

/* Responsive Media Queries */

/* Extra Large Desktop */
@media (min-width: 1400px) {
    .container {
        max-width: 1300px;
    }
}

/* Large Desktop */
@media (max-width: 1199px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium Desktop / Small Laptop */
@media (max-width: 991px) {
    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        max-width: 500px;
    }
    
    .features-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .process-timeline {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .step-arrow {
        display: none;
    }

.pricing-grid {
    display: flex;
    flex-wrap: nowrap;         
    overflow-x: auto;         
    width: 100vw;              
    padding-bottom: 10px;      
    gap: 20px;                
}

    .pricing-grid {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 20px;
        padding-bottom: 10px;
        scroll-behavior: smooth;
    }

    .pricing-card {
        flex: 0 0 auto;           
        width: 70vw;              
        max-width: 350px;
    }

    .faq-question span {
        font-size: clamp(1rem, 2.5vw, 1.2rem);
        line-height: 1.4;
    }

}

/* Tablet */
@media (max-width: 768px) {
    .cta-subtitle {
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
        font-size: clamp(1rem, 2.5vw, 1.1rem);
        line-height: 1.6;
    }

    .container {
        padding: 0 15px;
    }
    
    .hero-content {
        padding: 80px 0;
    }
    
    .hero-title {
        font-size: clamp(2.2rem, 5.5vw, 2.8rem);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.15rem);
        line-height: 1.5;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
        max-width: 300px;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: clamp(1.8rem, 4.5vw, 2.3rem);
    }
    
    .stat-label {
        font-size: clamp(0.8rem, 2vw, 0.9rem);
    }
    
    .hero-cta {
        gap: 1rem;
    }
    
    .btn-hero-primary, .btn-hero-secondary {
        width: 100%;
        max-width: 300px;
        font-size: clamp(0.9rem, 2.3vw, 1rem);
    }
    
    .feature-title {
        font-size: clamp(1.2rem, 3vw, 1.4rem);
    }
    
    .feature-description {
        font-size: clamp(0.95rem, 2.3vw, 1.05rem);
        line-height: 1.6;
    }
    
    .process-timeline {
        grid-template-columns: 1fr;
    }
    
    .step-number {
        position: relative;
        top: 0;
        right: 0;
        margin-bottom: 1rem;
        align-self: flex-start;
    }
    
    .process-step {
        text-align: center;
    }
    
    .step-content h3 {
        font-size: clamp(1.1rem, 2.8vw, 1.3rem);
    }
    
    .step-content p {
        font-size: clamp(0.95rem, 2.3vw, 1.05rem);
        line-height: 1.5;
    }
    
    .testimonial-author {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .testimonial-content {
        font-size: clamp(1rem, 2.5vw, 1.1rem);
        line-height: 1.6;
    }
    
    .author-stats {
        justify-content: center;
    }
    
    .pricing-benefits {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .pricing-title {
        font-size: clamp(1.4rem, 3.5vw, 1.7rem);
    }
    
    .pricing-price {
        font-size: clamp(2rem, 5vw, 2.5rem);
    }
    
    .faq-question span {
        font-size: clamp(1rem, 2.5vw, 1.15rem);
        line-height: 1.4;
    }
    
    .faq-answer p {
        font-size: clamp(0.9rem, 2.2vw, 1rem);
        line-height: 1.6;
    }
    
    .cta-title {
        font-size: clamp(1.8rem, 4.5vw, 2.2rem);
    }
    
    .cta-buttons {
        gap: 1rem;
    }
    
    .btn-cta-primary, .btn-cta-secondary {
        width: 100%;
        max-width: 300px;
        font-size: clamp(0.9rem, 2.3vw, 1rem);
    }
}

/* Mobile Large */
@media (max-width: 576px) {
    .hero-content {
        padding: 60px 0;
    }
    
    .floating-element {
        display: none;
    }
    
    .feature-card, .testimonial-card, .process-step {
        padding: 1.5rem;
    }
    
    .feature-title {
        font-size: clamp(1.1rem, 2.8vw, 1.25rem);
    }
    
    .feature-description {
        font-size: clamp(0.9rem, 2.2vw, 1rem);
        line-height: 1.6;
    }
    
    .pricing-card {
        margin: 0;
    }
    
    .pricing-header {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .pricing-title {
        font-size: clamp(1.3rem, 3.5vw, 1.6rem);
    }
    
    .popular-badge {
        position: static;
        margin-bottom: 1rem;
        align-self: center;
        font-size: clamp(0.7rem, 1.8vw, 0.8rem);
    }
    
    .faq-question {
        padding: 1rem 1.5rem;
    }
    
    .faq-question span {
        font-size: clamp(0.95rem, 2.3vw, 1.1rem);
        line-height: 1.4;
    }
    
    .faq-answer {
        padding: 0 1.5rem 1rem;
    }
    
    .faq-answer p {
        font-size: clamp(0.85rem, 2.1vw, 0.95rem);
        line-height: 1.6;
    }
    
    .testimonial-content {
        font-size: clamp(0.95rem, 2.3vw, 1.05rem);
        line-height: 1.6;
    }
    
    .step-content h3 {
        font-size: clamp(1rem, 2.5vw, 1.2rem);
    }
    
    .step-content p {
        font-size: clamp(0.9rem, 2.2vw, 1rem);
        line-height: 1.5;
    }
}

/* Mobile Small */
@media (max-width: 480px) {
    .hero-stats {
        gap: 0.8rem;
    }
    
    .stat-card {
        padding: 0.8rem;
        min-width: auto;
    }
    
    .stat-number {
        font-size: clamp(1.5rem, 4vw, 2rem);
    }
    
    .stat-label {
        font-size: clamp(0.75rem, 2vw, 0.85rem);
    }
    
    .btn-hero-primary, .btn-hero-secondary {
        padding: 0.8rem 1.5rem;
        font-size: clamp(0.85rem, 2.2vw, 0.95rem);
    }
    
    .feature-card, .testimonial-card {
        padding: 1.2rem;
    }
    
    .feature-title {
        font-size: clamp(1rem, 2.5vw, 1.15rem);
    }
    
    .feature-description {
        font-size: clamp(0.85rem, 2.1vw, 0.95rem);
    }
    
    .process-step {
        padding: 1.2rem;
    }
    
    .step-content h3 {
        font-size: clamp(0.95rem, 2.3vw, 1.1rem);
    }
    
    .step-content p {
        font-size: clamp(0.85rem, 2.1vw, 0.95rem);
    }
    
    .pricing-features {
        padding: 1.2rem;
    }
    
    .pricing-title {
        font-size: clamp(1.2rem, 3.2vw, 1.4rem);
    }
    
    .pricing-price {
        font-size: clamp(1.8rem, 4.5vw, 2.2rem);
    }
    
    .btn-pricing {
        padding: 0.8rem 1.5rem;
        font-size: clamp(0.85rem, 2.2vw, 0.95rem);
    }
    
    .faq-question span {
        font-size: clamp(0.9rem, 2.2vw, 1rem);
    }
    
    .faq-answer p {
        font-size: clamp(0.8rem, 2vw, 0.9rem);
    }
    
    .testimonial-content {
        font-size: clamp(0.9rem, 2.2vw, 1rem);
    }
}

/* Ultra Small Mobile */
@media (max-width: 360px) {
    .container {
        padding: 0 10px;
    }
    
    .hero-title {
        font-size: clamp(1.8rem, 5vw, 2.2rem);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: clamp(0.9rem, 2.5vw, 1rem);
        line-height: 1.5;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-hero-primary, .btn-hero-secondary {
        width: 100%;
        font-size: clamp(0.8rem, 2.1vw, 0.9rem);
        padding: 0.7rem 1.2rem;
    }
    
    .stat-number {
        font-size: clamp(1.3rem, 3.5vw, 1.6rem);
    }
    
    .stat-label {
        font-size: clamp(0.7rem, 1.8vw, 0.8rem);
    }
    
    .feature-title {
        font-size: clamp(0.95rem, 2.3vw, 1.05rem);
    }
    
    .feature-description {
        font-size: clamp(0.8rem, 2vw, 0.9rem);
    }
    
    .step-content h3 {
        font-size: clamp(0.9rem, 2.2vw, 1rem);
    }
    
    .step-content p {
        font-size: clamp(0.8rem, 2vw, 0.9rem);
    }
    
    .pricing-title {
        font-size: clamp(1.1rem, 3vw, 1.25rem);
    }
    
    .pricing-price {
        font-size: clamp(1.6rem, 4.2vw, 1.9rem);
    }
    
    .btn-pricing {
        font-size: clamp(0.8rem, 2.1vw, 0.9rem);
        padding: 0.7rem 1.2rem;
    }
    
    .faq-question span {
        font-size: clamp(0.85rem, 2.1vw, 0.95rem);
    }
    
    .faq-answer p {
        font-size: clamp(0.75rem, 1.9vw, 0.85rem);
    }
    
    .testimonial-content {
        font-size: clamp(0.85rem, 2.1vw, 0.95rem);
    }
    
    .author-name {
        font-size: clamp(0.8rem, 2vw, 0.9rem);
    }
    
    .author-role {
        font-size: clamp(0.7rem, 1.8vw, 0.8rem);
    }
    
    .cta-title {
        font-size: clamp(1.5rem, 4vw, 1.8rem);
    }
    
    .cta-subtitle {
        font-size: clamp(0.85rem, 2.1vw, 0.95rem);
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-cta-primary, .btn-cta-secondary {
        width: 100%;
    }
}

/* Performance Optimizations */
.feature-card,
.pricing-card,
.testimonial-card,
.process-step {
    will-change: transform;
}

.floating-element {
    will-change: transform;
    contain: layout style paint;
}

.hero-gradient {
    will-change: background-position;
}

/* AOS Animation Compatibility */
[data-aos] {
    pointer-events: none;
}

[data-aos].aos-animate {
    pointer-events: auto;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Optimized Loading Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-up {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Print Styles */
@media print {
    .floating-elements,
    .hero-pattern,
    .cta-pattern {
        display: none;
    }
    
    * {
        box-shadow: none !important;
        text-shadow: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .feature-card,
    .testimonial-card,
    .pricing-card {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating-element {
        animation: none;
    }
    
    .hero-gradient {
        animation: none;
    }
}

/* Reduce motion class for JavaScript control */
.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

.reduce-motion .floating-element {
    animation: none !important;
}

.reduce-motion .hero-gradient {
    animation: none !important;
}

/* Optimized Card Hover Effect */
.card-hover {
    transform: translateY(-10px) scale(1.02) !important;
    transition: transform 0.3s ease !important;
}

/* Focus Styles for Better Accessibility */
button:focus,
a:focus,
.btn-pricing:focus,
.btn-hero-primary:focus,
.btn-hero-secondary:focus,
.btn-cta-primary:focus,
.btn-cta-secondary:focus {
    outline: 2px solid #FFD700;
    outline-offset: 2px;
}

/* Improve Text Readability */
@media (max-width: 768px) {
    body {
        font-size: 16px;
        line-height: 1.7;
    }
}
</style>

<script>
// Optimized for better INP performance
document.addEventListener('DOMContentLoaded', function() {
    // FAQ functionality with event delegation for better performance
    const faqContainer = document.querySelector('.faq-container');
    
    if (faqContainer) {
        faqContainer.addEventListener('click', function(e) {
            const question = e.target.closest('.faq-question');
            if (!question) return;
            
            const item = question.closest('.faq-item');
            const isActive = item.classList.contains('active');
            
            // Use requestAnimationFrame for smooth animations
            requestAnimationFrame(() => {
                // Close all FAQ items
                const activeItems = faqContainer.querySelectorAll('.faq-item.active');
                activeItems.forEach(faqItem => {
                    faqItem.classList.remove('active');
                });
                
                // Open clicked item if it wasn't active
                if (!isActive) {
                    item.classList.add('active');
                }
            });
        });
    }

    // Optimized smooth scrolling with event delegation
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a[href^="#"]');
        if (!link) return;
        
        e.preventDefault();
        
        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);
        
        if (targetSection) {
            // Use requestAnimationFrame for better performance
            requestAnimationFrame(() => {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        }
    }, { passive: false });

    // Optimized scroll animation with better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        // Batch DOM updates for better performance
        requestAnimationFrame(() => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                    observer.unobserve(entry.target); // Stop observing once animated
                }
            });
        });
    }, observerOptions);
    
    // Observe elements for animation with reduced initial setup
    const animatedElements = document.querySelectorAll(
        '.feature-card, .process-step, .testimonial-card, .pricing-card'
    );
    
    // Use CSS classes instead of inline styles for better performance
    animatedElements.forEach((el, index) => {
        el.classList.add('animate-on-scroll');
        el.style.animationDelay = `${index * 0.1}s`;
        observer.observe(el);
    });

    // Optimized button handling with event delegation
    document.addEventListener('click', function(e) {
        const button = e.target.closest('.btn-pricing, .btn-cta-primary, .btn-cta-secondary');
        if (!button) return;
        
        // Add loading effect if it's a form submission
        if (button.href && button.href.includes('lien-he')) {
            requestAnimationFrame(() => {
                button.style.opacity = '0.8';
                button.style.pointerEvents = 'none';
                
                setTimeout(() => {
                    button.style.opacity = '1';
                    button.style.pointerEvents = 'auto';
                }, 2000);
            });
        }
    }, { passive: true });

    // Optimized parallax effect with throttling (only on desktop)
    if (window.innerWidth > 768) {
        let ticking = false;
        
        function updateParallax() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating-element');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.3 + (index * 0.1);
                element.style.transform = `translate3d(0, ${scrolled * speed}px, 0)`;
            });
            
            ticking = false;
        }
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }, { passive: true });
    }

    // Optimized hover effects using CSS classes
    const cardsContainer = document.body;
    
    cardsContainer.addEventListener('mouseenter', function(e) {
        const card = e.target.closest('.feature-card, .pricing-card, .testimonial-card');
        if (card && window.innerWidth > 768) {
            card.classList.add('card-hover');
        }
    }, true);
    
    cardsContainer.addEventListener('mouseleave', function(e) {
        const card = e.target.closest('.feature-card, .pricing-card, .testimonial-card');
        if (card) {
            card.classList.remove('card-hover');
        }
    }, true);

    // Optimized animation handling for reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (prefersReducedMotion.matches) {
        // Add CSS class to disable animations
        document.documentElement.classList.add('reduce-motion');
    }
    
    // Listen for changes in motion preference
    prefersReducedMotion.addEventListener('change', () => {
        if (prefersReducedMotion.matches) {
            document.documentElement.classList.add('reduce-motion');
        } else {
            document.documentElement.classList.remove('reduce-motion');
        }
    });

    // Optimized lazy loading for images
    const lazyImages = document.querySelectorAll('img[data-src]');
    if (lazyImages.length > 0) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            requestAnimationFrame(() => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        lazyImages.forEach(img => imageObserver.observe(img));
    }
});

// Optimized utility functions
function handleContactForm() {
    // Add your contact form logic here
    return Promise.resolve();
}

function trackInteraction(action, element) {
    // Add your analytics tracking here (non-blocking)
    if (typeof gtag !== 'undefined') {
        gtag('event', action, { element });
    }
}
</script>
</body>
</html>