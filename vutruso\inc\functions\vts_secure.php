<?php

/* Disable Application Passwords
*===============================================================*/
add_filter( 'wp_is_application_passwords_available', '__return_false' );


// Khoa dang nhap tu 0h-6h sang
function block_login_during_night() {
    // Set the timezone
    date_default_timezone_set('Asia/Ho_Chi_Minh');
    
    // Get the current hour in 24-hour format
    $current_hour = intval(date('G'));

    // Debug: Display current hour and full datetime for verification
    //error_log("Current Hour: " . $current_hour);
    //error_log("Current Datetime: " . date('Y-m-d H:i:s'));

    // Check if the current hour is between 0 and 6 a.m.
    if ($current_hour >= 0 && $current_hour < 6) {
        wp_redirect(home_url());
        exit;
    }
}

// Hook into standard WordPress login
add_action('wp_login', 'block_login_during_night', 10, 2);

// Hook into WooCommerce login form
add_action('woocommerce_login_form_start', 'block_login_during_night');

// Hook into any other login forms using the standard login form hook
add_action('login_form_login', 'block_login_during_night');



